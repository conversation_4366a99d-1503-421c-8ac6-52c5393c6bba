#!/usr/bin/env python3
"""
محرك كشف الحركة المتقدم
Advanced Motion Detection Engine
"""

import cv2
import numpy as np
import sqlite3
import os
import time
from datetime import datetime
import threading

class AdvancedMotionDetector:
    def __init__(self, database_path="database.db"):
        self.database_path = database_path
        
        # إعدادات كشف الحركة
        self.sensitivity = 0.3
        self.min_area = 500
        self.blur_size = 21
        self.threshold = 25
        self.dilate_iterations = 2
        
        # إعدادات التسجيل
        self.record_on_motion = True
        self.pre_record_seconds = 5
        self.post_record_seconds = 10
        
        # إعدادات الحفظ
        self.save_motion_images = True
        self.motion_images_path = "motion_captures"
        
        # متغيرات الحالة
        self.background_subtractor = None
        self.previous_frame = None
        self.motion_detected = False
        self.last_motion_time = 0
        self.motion_cooldown = 2  # ثانيتين بين كشوفات الحركة
        
        # إنشاء مجلد الحفظ
        os.makedirs(self.motion_images_path, exist_ok=True)
        
        # تهيئة قاعدة البيانات
        self.init_motion_database()
    
    def init_motion_database(self):
        """تهيئة جدول كشف الحركة في قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS motion_detections (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    camera_id INTEGER,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    motion_area INTEGER,
                    confidence REAL,
                    image_path TEXT,
                    video_path TEXT,
                    FOREIGN KEY (camera_id) REFERENCES cameras (id)
                )
            ''')
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"خطأ في تهيئة قاعدة بيانات كشف الحركة: {e}")
    
    def detect_motion_background_subtraction(self, frame):
        """كشف الحركة باستخدام Background Subtraction"""
        if self.background_subtractor is None:
            self.background_subtractor = cv2.createBackgroundSubtractorMOG2(
                detectShadows=True, varThreshold=50, history=500
            )
        
        # تطبيق Background Subtraction
        fg_mask = self.background_subtractor.apply(frame)
        
        # تنظيف القناع
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        fg_mask = cv2.morphologyEx(fg_mask, cv2.MORPH_OPEN, kernel)
        
        # العثور على الكونتورات
        contours, _ = cv2.findContours(fg_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        motion_areas = []
        total_motion_area = 0
        
        for contour in contours:
            area = cv2.contourArea(contour)
            if area > self.min_area:
                x, y, w, h = cv2.boundingRect(contour)
                motion_areas.append((x, y, w, h, area))
                total_motion_area += area
        
        return motion_areas, total_motion_area, fg_mask
    
    def detect_motion_frame_difference(self, frame):
        """كشف الحركة باستخدام Frame Difference"""
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        gray = cv2.GaussianBlur(gray, (self.blur_size, self.blur_size), 0)
        
        if self.previous_frame is None:
            self.previous_frame = gray
            return [], 0, None
        
        # حساب الفرق بين الإطارات
        frame_diff = cv2.absdiff(self.previous_frame, gray)
        
        # تطبيق العتبة
        _, thresh = cv2.threshold(frame_diff, self.threshold, 255, cv2.THRESH_BINARY)
        
        # تطبيق التمدد
        thresh = cv2.dilate(thresh, None, iterations=self.dilate_iterations)
        
        # العثور على الكونتورات
        contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        motion_areas = []
        total_motion_area = 0
        
        for contour in contours:
            area = cv2.contourArea(contour)
            if area > self.min_area:
                x, y, w, h = cv2.boundingRect(contour)
                motion_areas.append((x, y, w, h, area))
                total_motion_area += area
        
        self.previous_frame = gray
        return motion_areas, total_motion_area, thresh
    
    def detect_motion(self, frame, camera_id=None, method="background_subtraction"):
        """كشف الحركة الرئيسي"""
        current_time = time.time()
        
        # فحص فترة التهدئة
        if current_time - self.last_motion_time < self.motion_cooldown:
            return False, [], None
        
        # اختيار طريقة الكشف
        if method == "background_subtraction":
            motion_areas, total_area, debug_frame = self.detect_motion_background_subtraction(frame)
        else:
            motion_areas, total_area, debug_frame = self.detect_motion_frame_difference(frame)
        
        # تحديد ما إذا كان هناك حركة
        motion_detected = len(motion_areas) > 0 and total_area > self.min_area
        
        if motion_detected:
            self.last_motion_time = current_time
            self.motion_detected = True
            
            # حفظ صورة الحركة
            if self.save_motion_images:
                self.save_motion_image(frame, motion_areas, camera_id)
            
            # تسجيل في قاعدة البيانات
            if camera_id:
                self.log_motion_detection(camera_id, total_area, motion_areas)
            
            print(f"🎯 كشف حركة! المساحة: {total_area}, عدد المناطق: {len(motion_areas)}")
        
        return motion_detected, motion_areas, debug_frame
    
    def save_motion_image(self, frame, motion_areas, camera_id):
        """حفظ صورة كشف الحركة"""
        try:
            # رسم مربعات حول مناطق الحركة
            motion_frame = frame.copy()
            
            for x, y, w, h, area in motion_areas:
                cv2.rectangle(motion_frame, (x, y), (x + w, y + h), (0, 255, 0), 2)
                cv2.putText(motion_frame, f"Area: {int(area)}", 
                           (x, y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
            
            # إنشاء اسم الملف
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            camera_str = f"cam{camera_id}" if camera_id else "unknown"
            filename = f"motion_{camera_str}_{timestamp}.jpg"
            filepath = os.path.join(self.motion_images_path, filename)
            
            # حفظ الصورة
            cv2.imwrite(filepath, motion_frame)
            
            return filepath
            
        except Exception as e:
            print(f"خطأ في حفظ صورة الحركة: {e}")
            return None
    
    def log_motion_detection(self, camera_id, total_area, motion_areas):
        """تسجيل كشف الحركة في قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()
            
            # حساب الثقة بناءً على المساحة
            confidence = min(total_area / 10000, 1.0)  # تطبيع إلى 0-1
            
            cursor.execute('''
                INSERT INTO motion_detections 
                (camera_id, motion_area, confidence)
                VALUES (?, ?, ?)
            ''', (camera_id, int(total_area), confidence))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"خطأ في تسجيل كشف الحركة: {e}")
    
    def draw_motion_overlay(self, frame, motion_areas):
        """رسم تراكب كشف الحركة على الإطار"""
        overlay_frame = frame.copy()
        
        for x, y, w, h, area in motion_areas:
            # رسم مربع أخضر حول منطقة الحركة
            cv2.rectangle(overlay_frame, (x, y), (x + w, y + h), (0, 255, 0), 2)
            
            # إضافة نص يوضح المساحة
            cv2.putText(overlay_frame, f"Motion: {int(area)}", 
                       (x, y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
        
        # إضافة حالة كشف الحركة
        status_text = "MOTION DETECTED" if len(motion_areas) > 0 else "NO MOTION"
        status_color = (0, 255, 0) if len(motion_areas) > 0 else (0, 0, 255)
        
        cv2.putText(overlay_frame, status_text, (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, status_color, 2)
        
        # إضافة الوقت
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        cv2.putText(overlay_frame, timestamp, (10, overlay_frame.shape[0] - 10), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        return overlay_frame
    
    def get_motion_statistics(self, camera_id=None, hours=24):
        """الحصول على إحصائيات كشف الحركة"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()
            
            query = '''
                SELECT COUNT(*) as total_detections,
                       AVG(motion_area) as avg_area,
                       MAX(motion_area) as max_area,
                       AVG(confidence) as avg_confidence
                FROM motion_detections 
                WHERE timestamp > datetime('now', '-{} hours')
            '''.format(hours)
            
            params = []
            if camera_id:
                query += " AND camera_id = ?"
                params.append(camera_id)
            
            cursor.execute(query, params)
            result = cursor.fetchone()
            
            conn.close()
            
            return {
                'total_detections': result[0] or 0,
                'avg_area': result[1] or 0,
                'max_area': result[2] or 0,
                'avg_confidence': result[3] or 0
            }
            
        except Exception as e:
            print(f"خطأ في الحصول على إحصائيات الحركة: {e}")
            return {}
    
    def set_sensitivity(self, sensitivity):
        """ضبط حساسية كشف الحركة"""
        self.sensitivity = max(0.1, min(1.0, sensitivity))
        self.min_area = int(1000 * (1 - self.sensitivity))
        self.threshold = int(50 * (1 - self.sensitivity))
    
    def set_recording_settings(self, record_on_motion=True, pre_seconds=5, post_seconds=10):
        """ضبط إعدادات التسجيل"""
        self.record_on_motion = record_on_motion
        self.pre_record_seconds = pre_seconds
        self.post_record_seconds = post_seconds
    
    def cleanup_old_detections(self, days=30):
        """تنظيف كشوفات الحركة القديمة"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                DELETE FROM motion_detections 
                WHERE timestamp < datetime('now', '-{} days')
            '''.format(days))
            
            deleted_count = cursor.rowcount
            conn.commit()
            conn.close()
            
            print(f"تم حذف {deleted_count} كشف حركة قديم")
            return deleted_count
            
        except Exception as e:
            print(f"خطأ في تنظيف كشوفات الحركة: {e}")
            return 0


def test_motion_detector():
    """اختبار محرك كشف الحركة"""
    print("🔍 اختبار محرك كشف الحركة...")
    
    detector = AdvancedMotionDetector()
    
    # اختبار مع كاميرا ويب (إذا متوفرة)
    cap = cv2.VideoCapture(0)
    
    if not cap.isOpened():
        print("❌ لا يمكن فتح الكاميرا للاختبار")
        return
    
    print("✅ بدء اختبار كشف الحركة - اضغط 'q' للخروج")
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        # كشف الحركة
        motion_detected, motion_areas, debug_frame = detector.detect_motion(frame, camera_id=1)
        
        # رسم التراكب
        display_frame = detector.draw_motion_overlay(frame, motion_areas)
        
        # عرض الإطار
        cv2.imshow('Motion Detection Test', display_frame)
        
        # عرض إطار التصحيح إذا متوفر
        if debug_frame is not None:
            cv2.imshow('Debug Frame', debug_frame)
        
        # الخروج عند الضغط على 'q'
        if cv2.waitKey(1) & 0xFF == ord('q'):
            break
    
    cap.release()
    cv2.destroyAllWindows()
    
    # عرض الإحصائيات
    stats = detector.get_motion_statistics(camera_id=1, hours=1)
    print(f"📊 إحصائيات الاختبار: {stats}")


if __name__ == "__main__":
    test_motion_detector()
