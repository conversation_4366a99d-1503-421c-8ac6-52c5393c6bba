#!/usr/bin/env python3
"""
إصلاح مشكلة قاعدة البيانات المقفلة
Database Lock Fix Tool
"""

import sys
import os
import time
import subprocess
import sqlite3

def print_header():
    """طباعة رأس البرنامج"""
    print("=" * 50)
    print("🗄️ إصلاح مشكلة قاعدة البيانات")
    print("Database Lock Fix Tool")
    print("=" * 50)
    print()

def find_processes_using_database():
    """البحث عن العمليات التي تستخدم قاعدة البيانات"""
    print("🔍 البحث عن العمليات التي تستخدم قاعدة البيانات...")
    
    try:
        # استخدام tasklist للبحث عن عمليات Python
        result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq python.exe'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            lines = result.stdout.split('\n')
            python_processes = []
            
            for line in lines:
                if 'python.exe' in line:
                    parts = line.split()
                    if len(parts) >= 2:
                        pid = parts[1]
                        python_processes.append(pid)
            
            if python_processes:
                print(f"📊 تم العثور على {len(python_processes)} عملية Python:")
                for pid in python_processes:
                    print(f"  PID: {pid}")
                return python_processes
            else:
                print("ℹ️ لم يتم العثور على عمليات Python")
                return []
        else:
            print("⚠️ فشل في تشغيل tasklist")
            return []
            
    except Exception as e:
        print(f"❌ خطأ في البحث عن العمليات: {str(e)}")
        return []

def kill_python_processes():
    """إنهاء عمليات Python"""
    processes = find_processes_using_database()
    
    if not processes:
        print("ℹ️ لا توجد عمليات Python للإنهاء")
        return True
    
    print(f"\n⚠️ سيتم إنهاء {len(processes)} عملية Python")
    choice = input("هل تريد المتابعة؟ (y/n): ").lower()
    
    if choice not in ['y', 'yes', 'نعم']:
        print("❌ تم إلغاء العملية")
        return False
    
    killed_count = 0
    
    for pid in processes:
        try:
            subprocess.run(['taskkill', '/PID', pid, '/F'], 
                          capture_output=True, check=True)
            print(f"✅ تم إنهاء العملية PID: {pid}")
            killed_count += 1
        except subprocess.CalledProcessError:
            print(f"⚠️ فشل في إنهاء العملية PID: {pid}")
        except Exception as e:
            print(f"❌ خطأ في إنهاء العملية {pid}: {str(e)}")
    
    print(f"\n📊 تم إنهاء {killed_count} عملية من أصل {len(processes)}")
    
    # انتظار قليل للتأكد من إنهاء العمليات
    time.sleep(2)
    
    return killed_count > 0

def backup_database():
    """إنشاء نسخة احتياطية من قاعدة البيانات"""
    print("💾 إنشاء نسخة احتياطية من قاعدة البيانات...")
    
    if not os.path.exists('database.db'):
        print("ℹ️ قاعدة البيانات غير موجودة")
        return True
    
    try:
        import shutil
        backup_name = f"database_backup_{int(time.time())}.db"
        shutil.copy2('database.db', backup_name)
        print(f"✅ تم إنشاء نسخة احتياطية: {backup_name}")
        return True
    except Exception as e:
        print(f"❌ فشل في إنشاء النسخة الاحتياطية: {str(e)}")
        return False

def force_unlock_database():
    """فرض إلغاء قفل قاعدة البيانات"""
    print("🔓 محاولة فرض إلغاء قفل قاعدة البيانات...")
    
    try:
        # محاولة الاتصال بقاعدة البيانات مع timeout قصير
        conn = sqlite3.connect('database.db', timeout=1.0)
        
        # تشغيل استعلام بسيط
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print(f"✅ تم الاتصال بقاعدة البيانات. الجداول الموجودة: {len(tables)}")
        
        # إغلاق الاتصال
        conn.close()
        
        return True
        
    except sqlite3.OperationalError as e:
        if "database is locked" in str(e):
            print("❌ قاعدة البيانات مقفلة")
            return False
        else:
            print(f"❌ خطأ في قاعدة البيانات: {str(e)}")
            return False
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {str(e)}")
        return False

def recreate_database():
    """إعادة إنشاء قاعدة البيانات"""
    print("🔄 إعادة إنشاء قاعدة البيانات...")
    
    try:
        # حذف قاعدة البيانات القديمة
        if os.path.exists('database.db'):
            try:
                os.remove('database.db')
                print("🗑️ تم حذف قاعدة البيانات القديمة")
            except PermissionError:
                print("❌ لا يمكن حذف قاعدة البيانات (مقفلة)")
                return False
        
        # إنشاء قاعدة بيانات جديدة
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        from core.database import DatabaseManager
        
        db = DatabaseManager()
        print("✅ تم إنشاء قاعدة البيانات الجديدة")
        
        # اختبار المصادقة
        result = db.authenticate_user('admin', 'admin123')
        if result:
            print("✅ اختبار المصادقة نجح")
            return True
        else:
            print("❌ اختبار المصادقة فشل")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في إعادة إنشاء قاعدة البيانات: {str(e)}")
        return False

def create_database_alternative():
    """إنشاء قاعدة بيانات بديلة"""
    print("🔄 إنشاء قاعدة بيانات بديلة...")
    
    try:
        # استخدام اسم مختلف
        alt_db_name = f"database_new_{int(time.time())}.db"
        
        # إنشاء قاعدة البيانات البديلة
        conn = sqlite3.connect(alt_db_name)
        cursor = conn.cursor()
        
        # إنشاء جدول المستخدمين
        cursor.execute('''
            CREATE TABLE users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password TEXT NOT NULL,
                role TEXT DEFAULT 'user',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_login TIMESTAMP
            )
        ''')
        
        # إضافة المستخدم الافتراضي
        import hashlib
        hashed_password = hashlib.sha256('admin123'.encode()).hexdigest()
        cursor.execute(
            "INSERT INTO users (username, password, role) VALUES (?, ?, ?)",
            ('admin', hashed_password, 'admin')
        )
        
        # إنشاء جدول الكاميرات
        cursor.execute('''
            CREATE TABLE cameras (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                url TEXT NOT NULL,
                camera_type TEXT DEFAULT 'rtsp',
                position_x INTEGER DEFAULT 0,
                position_y INTEGER DEFAULT 0,
                enabled BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        conn.close()
        
        print(f"✅ تم إنشاء قاعدة البيانات البديلة: {alt_db_name}")
        
        # إعادة تسمية قاعدة البيانات القديمة
        if os.path.exists('database.db'):
            try:
                old_name = f"database_old_{int(time.time())}.db"
                os.rename('database.db', old_name)
                print(f"📁 تم إعادة تسمية قاعدة البيانات القديمة: {old_name}")
            except:
                print("⚠️ لا يمكن إعادة تسمية قاعدة البيانات القديمة")
        
        # إعادة تسمية قاعدة البيانات الجديدة
        os.rename(alt_db_name, 'database.db')
        print("✅ تم تفعيل قاعدة البيانات الجديدة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء قاعدة البيانات البديلة: {str(e)}")
        return False

def main():
    """الوظيفة الرئيسية"""
    print_header()
    
    try:
        # الخطوة 1: فحص حالة قاعدة البيانات
        if not os.path.exists('database.db'):
            print("ℹ️ قاعدة البيانات غير موجودة، سيتم إنشاؤها")
            if recreate_database():
                print("✅ تم حل المشكلة!")
                return 0
            else:
                print("❌ فشل في إنشاء قاعدة البيانات")
                return 1
        
        # الخطوة 2: اختبار الوصول لقاعدة البيانات
        if force_unlock_database():
            print("✅ قاعدة البيانات تعمل بشكل طبيعي")
            return 0
        
        # الخطوة 3: إنشاء نسخة احتياطية
        backup_database()
        
        # الخطوة 4: إنهاء العمليات المتداخلة
        if kill_python_processes():
            print("⏳ انتظار 3 ثوان...")
            time.sleep(3)
            
            # إعادة اختبار قاعدة البيانات
            if force_unlock_database():
                print("✅ تم حل المشكلة بإنهاء العمليات!")
                return 0
        
        # الخطوة 5: إعادة إنشاء قاعدة البيانات
        if recreate_database():
            print("✅ تم حل المشكلة بإعادة إنشاء قاعدة البيانات!")
            return 0
        
        # الخطوة 6: إنشاء قاعدة بيانات بديلة
        if create_database_alternative():
            print("✅ تم حل المشكلة بإنشاء قاعدة بيانات بديلة!")
            return 0
        
        print("❌ فشل في حل مشكلة قاعدة البيانات")
        print("\n💡 حلول يدوية:")
        print("1. أعد تشغيل الكمبيوتر")
        print("2. احذف ملف database.db يدوياً")
        print("3. شغل البرنامج كمدير")
        
        return 1
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف العملية")
        return 1
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {str(e)}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    input(f"\nاضغط Enter للخروج... (Exit Code: {exit_code})")
    sys.exit(exit_code)
