# Installation Guide - Camera Monitoring System

This guide will help you install and set up the Camera Monitoring System on your computer.

## System Requirements

### Minimum Requirements
- **Operating System**: Windows 10 or later
- **Python**: 3.7 or higher
- **RAM**: 4GB minimum, 8GB recommended
- **Storage**: 2GB free space (more for recordings)
- **Network**: Internet connection for IP/RTSP cameras

### Recommended Requirements
- **RAM**: 8GB or more
- **CPU**: Multi-core processor
- **Storage**: SSD with 10GB+ free space
- **Graphics**: Dedicated graphics card (for multiple cameras)

## Installation Methods

### Method 1: Using Pre-built Executable (Recommended for End Users)

1. **Download the Release**
   - Download the latest release from the releases page
   - Extract the ZIP file to a folder (e.g., `C:\CameraSystem\`)

2. **Run the Application**
   - Double-click `CameraMonitoringSystem.exe`
   - Or run `Start_Camera_System.bat`

3. **First Login**
   - Username: `admin`
   - Password: `admin123`

### Method 2: Python Installation (For Developers)

#### Step 1: Install Python

1. **Download Python**
   - Go to https://python.org/downloads/
   - Download Python 3.7 or higher
   - **Important**: Check "Add Python to PATH" during installation

2. **Verify Installation**
   ```cmd
   python --version
   pip --version
   ```

#### Step 2: Download the Source Code

1. **Download/Clone the Repository**
   ```cmd
   git clone <repository-url>
   cd camera-monitoring-system
   ```
   
   Or download and extract the ZIP file.

#### Step 3: Install Dependencies

1. **Install Required Packages**
   ```cmd
   pip install -r requirements.txt
   ```

2. **Verify Installation**
   ```cmd
   python test_system.py
   ```

#### Step 4: Run the Application

1. **Start the Application**
   ```cmd
   python main.py
   ```

2. **Or use the batch file**
   ```cmd
   run_camera_system.bat
   ```

## Initial Setup

### 1. First Login
- Username: `admin`
- Password: `admin123`
- **Important**: Change the default password after first login

### 2. Demo Setup (Optional)
Run the demo setup to add sample cameras and users:
```cmd
python demo_setup.py
```

This will add:
- 4 demo cameras
- Additional user accounts
- Optimized settings for testing

### 3. Add Your Cameras

1. **Click "Cameras" in the toolbar**
2. **Click "Add Camera"**
3. **Enter camera details:**
   - Name: Descriptive name
   - URL: Camera stream URL
   - Type: RTSP, IP, or USB

#### Camera URL Examples:

**RTSP Cameras:**
```
rtsp://username:password@*************:554/stream
rtsp://admin:12345@************:554/Streaming/Channels/101/
```

**IP Cameras:**
```
http://*************:8080/video
**************************************/mjpeg
```

**USB Cameras:**
```
0    (first USB camera)
1    (second USB camera)
```

**DVR/NVR Systems:**
```
rtsp://admin:password@*************:554/Streaming/Channels/{}/
```
The system will auto-generate URLs for multiple channels.

## Configuration

### Application Settings

Edit `config.json` to customize:

```json
{
    "app_settings": {
        "window_width": 1280,
        "window_height": 800,
        "fullscreen": false,
        "auto_record": true,
        "motion_sensitivity": 50
    },
    "recording_settings": {
        "format": "mp4",
        "quality": "high",
        "fps": 30,
        "duration_minutes": 60,
        "storage_path": "recordings/"
    },
    "motion_detection": {
        "enabled": true,
        "threshold": 25,
        "min_area": 500
    }
}
```

### User Management

1. **Add Users** (Admin only)
   - Go to Tools → Users
   - Click "Add User"
   - Set username, password, and role

2. **User Roles:**
   - **Admin**: Full access to all features
   - **Operator**: Camera and recording management
   - **User**: View-only access

## Troubleshooting

### Common Issues

#### 1. "Python is not recognized"
**Solution:**
- Reinstall Python with "Add to PATH" checked
- Or manually add Python to system PATH

#### 2. "Module not found" errors
**Solution:**
```cmd
pip install --upgrade pip
pip install -r requirements.txt
```

#### 3. Camera connection failed
**Solutions:**
- Check camera URL and credentials
- Verify network connectivity
- Test with VLC media player first
- Check firewall settings

#### 4. Poor performance with multiple cameras
**Solutions:**
- Reduce number of simultaneous cameras
- Lower video quality/resolution
- Close other applications
- Use wired network connection

#### 5. Recording issues
**Solutions:**
- Check disk space
- Verify write permissions to recordings folder
- Ensure stable camera connection

### Performance Optimization

#### For Multiple Cameras:
1. **Hardware Recommendations:**
   - Use SSD for recordings
   - Minimum 8GB RAM
   - Dedicated graphics card

2. **Software Settings:**
   - Reduce motion detection sensitivity
   - Lower recording quality if needed
   - Limit concurrent recordings

3. **Network Optimization:**
   - Use wired connections
   - Dedicated network for cameras
   - Quality of Service (QoS) settings

## Building Executable

To create your own executable:

1. **Install PyInstaller**
   ```cmd
   pip install pyinstaller
   ```

2. **Build Executable**
   ```cmd
   python build_exe.py
   ```

3. **Find Output**
   - Executable: `dist/CameraMonitoringSystem.exe`
   - All files: `dist/` folder

## Security Considerations

### Default Passwords
- **Change default admin password immediately**
- Use strong passwords for all accounts
- Regularly update passwords

### Network Security
- Use HTTPS/RTSP over TLS when possible
- Secure camera network (VLAN)
- Regular firmware updates for cameras

### File Permissions
- Restrict access to database file
- Secure recordings folder
- Regular backups

## Support

### Getting Help

1. **Check Documentation**
   - README.md
   - This installation guide
   - In-app help

2. **Run Diagnostics**
   ```cmd
   python test_system.py
   ```

3. **Check Logs**
   - Application logs in console
   - System event logs
   - Camera connection logs

### Reporting Issues

When reporting issues, include:
- Operating system version
- Python version
- Error messages
- Steps to reproduce
- Camera types and URLs (without credentials)

## Advanced Configuration

### Custom Themes
- Modify CSS in UI files
- Create custom color schemes
- Add company branding

### Database Customization
- SQLite database location
- Backup and restore procedures
- Data export options

### Integration
- API endpoints for external systems
- Webhook notifications
- Third-party integrations

## Maintenance

### Regular Tasks
- Clean old recordings
- Update software dependencies
- Check camera connections
- Review user accounts
- Backup database

### Updates
- Check for new releases
- Test updates in development environment
- Backup before updating
- Update documentation

---

For additional support or questions, please refer to the main documentation or contact your system administrator.
