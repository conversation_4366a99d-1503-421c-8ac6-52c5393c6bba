#!/usr/bin/env python3
"""
أداة اختبار وإصلاح روابط الكاميرات
Camera Link Tester and Fixer Tool
"""

import sys
import os
import cv2
import time
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class CameraLinkTester(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.current_cap = None
        
    def setup_ui(self):
        """إعداد واجهة اختبار الكاميرات"""
        self.setWindowTitle("أداة اختبار روابط الكاميرات - Camera Link Tester")
        self.setGeometry(100, 100, 900, 700)
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        central_widget.setLayout(main_layout)
        
        # العنوان
        title = QLabel("🎥 أداة اختبار روابط الكاميرات")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 20px; font-weight: bold; margin: 10px; color: #2c3e50;")
        main_layout.addWidget(title)
        
        # قسم إدخال رابط الكاميرا
        input_group = QGroupBox("إعدادات الكاميرا")
        input_layout = QFormLayout()
        
        # نوع الكاميرا
        self.camera_type = QComboBox()
        self.camera_type.addItems(["RTSP", "HTTP/IP", "USB", "File"])
        self.camera_type.currentTextChanged.connect(self.on_camera_type_changed)
        input_layout.addRow("نوع الكاميرا:", self.camera_type)
        
        # رابط الكاميرا
        self.camera_url = QLineEdit()
        self.camera_url.setPlaceholderText("أدخل رابط الكاميرا هنا...")
        input_layout.addRow("رابط الكاميرا:", self.camera_url)
        
        # اسم المستخدم وكلمة المرور
        self.username = QLineEdit()
        self.username.setPlaceholderText("اسم المستخدم (اختياري)")
        input_layout.addRow("اسم المستخدم:", self.username)
        
        self.password = QLineEdit()
        self.password.setPlaceholderText("كلمة المرور (اختياري)")
        self.password.setEchoMode(QLineEdit.Password)
        input_layout.addRow("كلمة المرور:", self.password)
        
        input_group.setLayout(input_layout)
        main_layout.addWidget(input_group)
        
        # أزرار التحكم
        button_layout = QHBoxLayout()
        
        self.generate_url_btn = QPushButton("🔗 توليد الرابط")
        self.generate_url_btn.clicked.connect(self.generate_url)
        button_layout.addWidget(self.generate_url_btn)
        
        self.test_btn = QPushButton("🧪 اختبار الاتصال")
        self.test_btn.clicked.connect(self.test_connection)
        button_layout.addWidget(self.test_btn)
        
        self.preview_btn = QPushButton("👁️ معاينة مباشرة")
        self.preview_btn.clicked.connect(self.start_preview)
        button_layout.addWidget(self.preview_btn)
        
        self.stop_btn = QPushButton("⏹️ إيقاف")
        self.stop_btn.clicked.connect(self.stop_preview)
        self.stop_btn.setEnabled(False)
        button_layout.addWidget(self.stop_btn)
        
        main_layout.addLayout(button_layout)
        
        # منطقة عرض الفيديو
        self.video_label = QLabel()
        self.video_label.setMinimumSize(640, 480)
        self.video_label.setStyleSheet("border: 2px solid #bdc3c7; background-color: #ecf0f1;")
        self.video_label.setAlignment(Qt.AlignCenter)
        self.video_label.setText("منطقة عرض الفيديو\nVideo Preview Area")
        main_layout.addWidget(self.video_label)
        
        # منطقة النتائج والسجلات
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(150)
        self.log_text.setPlaceholderText("سجل النتائج والأخطاء...")
        main_layout.addWidget(self.log_text)
        
        # شريط الحالة
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("جاهز لاختبار الكاميرات")
        
        # تايمر لتحديث الفيديو
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_frame)
        
        # تحميل أمثلة الروابط
        self.load_sample_urls()
        
        # تطبيق النمط
        self.apply_style()
    
    def apply_style(self):
        """تطبيق نمط الواجهة"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f8f9fa;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #2c3e50;
            }
            QLineEdit, QComboBox {
                padding: 8px;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                font-size: 12px;
            }
            QLineEdit:focus, QComboBox:focus {
                border-color: #3498db;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 15px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
                color: #7f8c8d;
            }
            QTextEdit {
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                background-color: white;
                font-family: 'Courier New', monospace;
                font-size: 11px;
            }
        """)
    
    def load_sample_urls(self):
        """تحميل أمثلة الروابط"""
        self.sample_urls = {
            "RTSP": [
                "rtsp://admin:admin@*************:554/stream",
                "rtsp://admin:12345@************:554/Streaming/Channels/101/",
                "rtsp://username:password@ip:port/path"
            ],
            "HTTP/IP": [
                "http://*************:8080/video",
                "********************************/mjpeg",
                "http://*************/axis-cgi/mjpg/video.cgi"
            ],
            "USB": ["0", "1", "2"],
            "File": ["test_video.mp4", "sample.avi"]
        }
    
    def on_camera_type_changed(self, camera_type):
        """عند تغيير نوع الكاميرا"""
        if camera_type in self.sample_urls:
            sample = self.sample_urls[camera_type][0]
            self.camera_url.setText(sample)
            self.camera_url.setPlaceholderText(f"مثال: {sample}")
        
        # إظهار/إخفاء حقول المصادقة
        show_auth = camera_type in ["RTSP", "HTTP/IP"]
        self.username.setVisible(show_auth)
        self.password.setVisible(show_auth)
    
    def generate_url(self):
        """توليد رابط الكاميرا مع المصادقة"""
        camera_type = self.camera_type.currentText()
        base_url = self.camera_url.text().strip()
        username = self.username.text().strip()
        password = self.password.text().strip()
        
        if not base_url:
            self.log_message("❌ يرجى إدخال رابط الكاميرا", "error")
            return
        
        if camera_type in ["RTSP", "HTTP/IP"] and username and password:
            # إضافة المصادقة إلى الرابط
            if "://" in base_url:
                protocol, rest = base_url.split("://", 1)
                if "@" not in rest:
                    # إضافة المصادقة
                    new_url = f"{protocol}://{username}:{password}@{rest}"
                    self.camera_url.setText(new_url)
                    self.log_message(f"✅ تم توليد الرابط مع المصادقة: {new_url}", "success")
                else:
                    self.log_message("⚠️ الرابط يحتوي على مصادقة مسبقاً", "warning")
            else:
                self.log_message("❌ تنسيق الرابط غير صحيح", "error")
        else:
            self.log_message(f"ℹ️ الرابط الحالي: {base_url}", "info")
    
    def test_connection(self):
        """اختبار الاتصال بالكاميرا"""
        url = self.camera_url.text().strip()
        if not url:
            self.log_message("❌ يرجى إدخال رابط الكاميرا", "error")
            return
        
        self.log_message(f"🔍 اختبار الاتصال بـ: {url}", "info")
        self.test_btn.setEnabled(False)
        self.status_bar.showMessage("جاري اختبار الاتصال...")
        
        # تشغيل الاختبار في خيط منفصل
        self.test_thread = CameraTestThread(url)
        self.test_thread.result_ready.connect(self.on_test_result)
        self.test_thread.start()
    
    def on_test_result(self, success, message, details):
        """معالجة نتيجة اختبار الاتصال"""
        self.test_btn.setEnabled(True)
        
        if success:
            self.log_message(f"✅ {message}", "success")
            if details:
                self.log_message(f"📊 التفاصيل: {details}", "info")
            self.status_bar.showMessage("الاتصال ناجح!")
        else:
            self.log_message(f"❌ {message}", "error")
            if details:
                self.log_message(f"🔍 تفاصيل الخطأ: {details}", "error")
            self.status_bar.showMessage("فشل الاتصال")
    
    def start_preview(self):
        """بدء المعاينة المباشرة"""
        url = self.camera_url.text().strip()
        if not url:
            self.log_message("❌ يرجى إدخال رابط الكاميرا", "error")
            return
        
        try:
            # إيقاف المعاينة السابقة
            self.stop_preview()
            
            # فتح الكاميرا
            if url.isdigit():
                self.current_cap = cv2.VideoCapture(int(url))
            else:
                self.current_cap = cv2.VideoCapture(url)
            
            # تعيين خصائص الكاميرا
            self.current_cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
            self.current_cap.set(cv2.CAP_PROP_FPS, 30)
            
            # اختبار قراءة إطار
            ret, frame = self.current_cap.read()
            if ret:
                self.log_message("✅ بدأت المعاينة المباشرة", "success")
                self.preview_btn.setEnabled(False)
                self.stop_btn.setEnabled(True)
                self.timer.start(33)  # ~30 FPS
                self.status_bar.showMessage("المعاينة المباشرة نشطة")
            else:
                self.log_message("❌ فشل في قراءة الإطار من الكاميرا", "error")
                self.current_cap.release()
                self.current_cap = None
                
        except Exception as e:
            self.log_message(f"❌ خطأ في بدء المعاينة: {str(e)}", "error")
            if self.current_cap:
                self.current_cap.release()
                self.current_cap = None
    
    def stop_preview(self):
        """إيقاف المعاينة المباشرة"""
        self.timer.stop()
        if self.current_cap:
            self.current_cap.release()
            self.current_cap = None
        
        self.video_label.clear()
        self.video_label.setText("منطقة عرض الفيديو\nVideo Preview Area")
        self.preview_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.status_bar.showMessage("تم إيقاف المعاينة")
        self.log_message("⏹️ تم إيقاف المعاينة المباشرة", "info")
    
    def update_frame(self):
        """تحديث إطار الفيديو"""
        if self.current_cap and self.current_cap.isOpened():
            ret, frame = self.current_cap.read()
            if ret:
                # تحويل الإطار إلى QPixmap
                rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                h, w, ch = rgb_frame.shape
                bytes_per_line = ch * w
                qt_image = QImage(rgb_frame.data, w, h, bytes_per_line, QImage.Format_RGB888)
                
                # تغيير حجم الصورة لتناسب العرض
                pixmap = QPixmap.fromImage(qt_image)
                scaled_pixmap = pixmap.scaled(
                    self.video_label.size(), 
                    Qt.KeepAspectRatio, 
                    Qt.SmoothTransformation
                )
                self.video_label.setPixmap(scaled_pixmap)
            else:
                self.log_message("⚠️ فقدان الاتصال بالكاميرا", "warning")
                self.stop_preview()
    
    def log_message(self, message, msg_type="info"):
        """إضافة رسالة إلى السجل"""
        timestamp = QTime.currentTime().toString("hh:mm:ss")
        
        color_map = {
            "info": "#2c3e50",
            "success": "#27ae60", 
            "warning": "#f39c12",
            "error": "#e74c3c"
        }
        
        color = color_map.get(msg_type, "#2c3e50")
        formatted_message = f'<span style="color: {color};">[{timestamp}] {message}</span>'
        
        self.log_text.append(formatted_message)
        self.log_text.verticalScrollBar().setValue(
            self.log_text.verticalScrollBar().maximum()
        )
        
        QApplication.processEvents()
    
    def closeEvent(self, event):
        """عند إغلاق النافذة"""
        self.stop_preview()
        event.accept()


class CameraTestThread(QThread):
    result_ready = pyqtSignal(bool, str, str)
    
    def __init__(self, url):
        super().__init__()
        self.url = url
    
    def run(self):
        """تشغيل اختبار الكاميرا"""
        try:
            # محاولة فتح الكاميرا
            if self.url.isdigit():
                cap = cv2.VideoCapture(int(self.url))
            else:
                cap = cv2.VideoCapture(self.url)
            
            # تعيين مهلة زمنية
            cap.set(cv2.CAP_PROP_OPEN_TIMEOUT_MSEC, 10000)
            cap.set(cv2.CAP_PROP_READ_TIMEOUT_MSEC, 5000)
            
            if cap.isOpened():
                # محاولة قراءة إطار
                ret, frame = cap.read()
                if ret and frame is not None:
                    # الحصول على معلومات الفيديو
                    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                    fps = cap.get(cv2.CAP_PROP_FPS)
                    
                    details = f"الدقة: {width}x{height}, FPS: {fps:.1f}"
                    cap.release()
                    
                    self.result_ready.emit(True, "الاتصال ناجح!", details)
                else:
                    cap.release()
                    self.result_ready.emit(False, "فشل في قراءة البيانات من الكاميرا", "")
            else:
                self.result_ready.emit(False, "فشل في فتح الكاميرا", "تحقق من الرابط والشبكة")
                
        except Exception as e:
            self.result_ready.emit(False, "خطأ في الاتصال", str(e))


def main():
    """تشغيل أداة اختبار الكاميرات"""
    app = QApplication(sys.argv)
    
    # تعيين خط يدعم العربية
    font = QFont("Arial", 10)
    app.setFont(font)
    
    # إنشاء النافذة الرئيسية
    window = CameraLinkTester()
    window.show()
    
    return app.exec_()


if __name__ == "__main__":
    sys.exit(main())
