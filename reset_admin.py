#!/usr/bin/env python3
"""
إعادة تعيين كلمة مرور المدير
Reset admin password script
"""

import sys
import os
import sqlite3
import hashlib

def reset_admin_password():
    """إعادة تعيين كلمة مرور المدير"""
    print("=" * 50)
    print("إعادة تعيين كلمة مرور المدير")
    print("Reset Admin Password")
    print("=" * 50)
    
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect("database.db")
        cursor = conn.cursor()
        
        # التحقق من وجود جدول المستخدمين
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='users'")
        if not cursor.fetchone():
            print("❌ جدول المستخدمين غير موجود! إنشاء قاعدة البيانات...")
            
            # إنشاء جدول المستخدمين
            cursor.execute('''
                CREATE TABLE users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    password TEXT NOT NULL,
                    role TEXT DEFAULT 'user',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_login TIMESTAMP
                )
            ''')
            print("✅ تم إنشاء جدول المستخدمين")
        
        # حذف المستخدم admin إذا كان موجوداً
        cursor.execute("DELETE FROM users WHERE username = 'admin'")
        deleted_count = cursor.rowcount
        if deleted_count > 0:
            print(f"🗑️ تم حذف {deleted_count} مستخدم admin موجود")
        
        # إنشاء كلمة مرور مشفرة
        password = "admin123"
        hashed_password = hashlib.sha256(password.encode()).hexdigest()
        
        # إضافة المستخدم admin الجديد
        cursor.execute(
            "INSERT INTO users (username, password, role) VALUES (?, ?, ?)",
            ('admin', hashed_password, 'admin')
        )
        
        # حفظ التغييرات
        conn.commit()
        
        print("✅ تم إنشاء مستخدم admin جديد")
        print(f"   اسم المستخدم: admin")
        print(f"   كلمة المرور: {password}")
        print(f"   كلمة المرور المشفرة: {hashed_password[:20]}...")
        
        # التحقق من النتيجة
        cursor.execute("SELECT id, username, role FROM users WHERE username = 'admin'")
        result = cursor.fetchone()
        
        if result:
            user_id, username, role = result
            print(f"✅ تم التحقق: ID={user_id}, Username={username}, Role={role}")
        else:
            print("❌ فشل في التحقق من إنشاء المستخدم!")
        
        conn.close()
        
        print("\n" + "=" * 50)
        print("✅ تم إعادة تعيين كلمة المرور بنجاح!")
        print("يمكنك الآن تسجيل الدخول باستخدام:")
        print("Username: admin")
        print("Password: admin123")
        print("=" * 50)
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ: {str(e)}")
        return False

def test_login():
    """اختبار تسجيل الدخول"""
    print("\n🔍 اختبار تسجيل الدخول...")
    
    try:
        conn = sqlite3.connect("database.db")
        cursor = conn.cursor()
        
        # اختبار المصادقة
        username = "admin"
        password = "admin123"
        hashed_password = hashlib.sha256(password.encode()).hexdigest()
        
        cursor.execute(
            "SELECT id, role FROM users WHERE username = ? AND password = ?",
            (username, hashed_password)
        )
        
        result = cursor.fetchone()
        
        if result:
            user_id, role = result
            print(f"✅ اختبار المصادقة نجح!")
            print(f"   User ID: {user_id}")
            print(f"   Role: {role}")
        else:
            print("❌ اختبار المصادقة فشل!")
        
        conn.close()
        return result is not None
        
    except Exception as e:
        print(f"❌ خطأ في اختبار المصادقة: {str(e)}")
        return False

def main():
    """الوظيفة الرئيسية"""
    # إعادة تعيين كلمة المرور
    if reset_admin_password():
        # اختبار تسجيل الدخول
        test_login()
        
        print("\n🚀 يمكنك الآن تشغيل التطبيق:")
        print("python main.py")
        print("\nأو تشغيل اختبار تسجيل الدخول:")
        print("python simple_login_test.py")
        
        return 0
    else:
        return 1

if __name__ == "__main__":
    sys.exit(main())
