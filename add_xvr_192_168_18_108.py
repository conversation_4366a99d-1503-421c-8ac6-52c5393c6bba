#!/usr/bin/env python3
"""
إضافة جهاز XVR المحدد
Add Specific XVR Device: **************
"""

import sys
import os
import sqlite3
from datetime import datetime

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def print_banner():
    """طباعة شعار الإضافة"""
    print("=" * 60)
    print("📹 إضافة جهاز XVR المحدد")
    print("📹 Adding Specific XVR Device")
    print("=" * 60)
    print()
    print("🔧 معلومات الجهاز:")
    print(f"📍 عنوان IP: **************")
    print(f"🏷️ الاسم: XVR")
    print(f"🆔 الرقم التسلسلي: 3B01B6FPAENW3UZ")
    print(f"👤 اسم المستخدم: admin")
    print(f"🔒 كلمة المرور: Mnbv@1978")
    print()

def add_xvr_device():
    """إضافة جهاز XVR إلى قاعدة البيانات"""
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect('database.db')
        cursor = conn.cursor()
        
        # معلومات الجهاز
        device_info = {
            'ip': '**************',
            'name': 'XVR',
            'serial': '3B01B6FPAENW3UZ',
            'username': 'admin',
            'password': 'Mnbv@1978',
            'port': 554,  # منفذ RTSP الافتراضي
            'type': 'XVR'
        }
        
        print("🔄 إضافة الجهاز إلى قاعدة البيانات...")
        
        # إضافة الجهاز الرئيسي
        cursor.execute('''
            INSERT OR REPLACE INTO cameras 
            (name, rtsp_url, username, password, is_active, camera_type, ip_address, port, serial_number)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            f"{device_info['name']} - Main",
            f"rtsp://{device_info['username']}:{device_info['password']}@{device_info['ip']}:{device_info['port']}/cam/realmonitor?channel=1&subtype=0",
            device_info['username'],
            device_info['password'],
            1,  # نشط
            device_info['type'],
            device_info['ip'],
            device_info['port'],
            device_info['serial']
        ))
        
        main_camera_id = cursor.lastrowid
        print(f"✅ تم إضافة الكاميرا الرئيسية (ID: {main_camera_id})")
        
        # إضافة كاميرات فرعية (افتراض 8 قنوات)
        channels = 8
        added_cameras = []
        
        for channel in range(1, channels + 1):
            camera_name = f"{device_info['name']} - قناة {channel}"
            rtsp_url = f"rtsp://{device_info['username']}:{device_info['password']}@{device_info['ip']}:{device_info['port']}/cam/realmonitor?channel={channel}&subtype=0"
            
            cursor.execute('''
                INSERT OR REPLACE INTO cameras 
                (name, rtsp_url, username, password, is_active, camera_type, ip_address, port, serial_number)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                camera_name,
                rtsp_url,
                device_info['username'],
                device_info['password'],
                1,  # نشط
                f"{device_info['type']}_CH{channel}",
                device_info['ip'],
                device_info['port'],
                device_info['serial']
            ))
            
            camera_id = cursor.lastrowid
            added_cameras.append({
                'id': camera_id,
                'name': camera_name,
                'channel': channel,
                'url': rtsp_url
            })
            
            print(f"✅ تم إضافة {camera_name} (ID: {camera_id})")
        
        # حفظ التغييرات
        conn.commit()
        conn.close()
        
        print(f"\n🎉 تم إضافة {len(added_cameras)} كاميرا بنجاح!")
        
        return True, added_cameras
        
    except Exception as e:
        print(f"❌ خطأ في إضافة الجهاز: {e}")
        return False, []

def test_connections(cameras):
    """اختبار الاتصال بالكاميرات"""
    print("\n🔍 اختبار الاتصال بالكاميرات...")
    
    try:
        import cv2
        
        successful_connections = 0
        
        for camera in cameras[:3]:  # اختبار أول 3 كاميرات فقط
            print(f"🔄 اختبار {camera['name']}...")
            
            try:
                cap = cv2.VideoCapture(camera['url'])
                
                if cap.isOpened():
                    ret, frame = cap.read()
                    if ret and frame is not None:
                        print(f"✅ {camera['name']}: متصل بنجاح")
                        successful_connections += 1
                    else:
                        print(f"⚠️ {camera['name']}: متصل لكن لا يوجد إطار")
                else:
                    print(f"❌ {camera['name']}: فشل الاتصال")
                
                cap.release()
                
            except Exception as e:
                print(f"❌ {camera['name']}: خطأ في الاتصال - {e}")
        
        print(f"\n📊 نتائج الاختبار: {successful_connections}/3 كاميرات متصلة")
        
        if successful_connections > 0:
            print("✅ الجهاز يعمل بشكل صحيح!")
        else:
            print("⚠️ قد تحتاج لفحص إعدادات الشبكة أو الجهاز")
            
    except ImportError:
        print("⚠️ OpenCV غير متوفر - تخطي اختبار الاتصال")

def show_camera_urls(cameras):
    """عرض روابط الكاميرات"""
    print("\n📋 روابط الكاميرات المضافة:")
    print("=" * 60)
    
    for camera in cameras:
        print(f"\n🎥 {camera['name']}:")
        print(f"   🔗 الرابط: {camera['url']}")
        print(f"   📺 القناة: {camera['channel']}")
        print(f"   🆔 المعرف: {camera['id']}")

def create_backup_script():
    """إنشاء ملف نسخ احتياطي للإعدادات"""
    backup_content = f"""#!/usr/bin/env python3
# نسخة احتياطية من إعدادات XVR
# تم الإنشاء في: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

XVR_CONFIG = {{
    'ip': '**************',
    'name': 'XVR',
    'serial': '3B01B6FPAENW3UZ',
    'username': 'admin',
    'password': 'Mnbv@1978',
    'port': 554,
    'channels': 8,
    'type': 'XVR'
}}

def restore_xvr_config():
    '''استعادة إعدادات XVR'''
    import sqlite3
    
    conn = sqlite3.connect('database.db')
    cursor = conn.cursor()
    
    for channel in range(1, XVR_CONFIG['channels'] + 1):
        camera_name = f"{{XVR_CONFIG['name']}} - قناة {{channel}}"
        rtsp_url = f"rtsp://{{XVR_CONFIG['username']}}:{{XVR_CONFIG['password']}}@{{XVR_CONFIG['ip']}}:{{XVR_CONFIG['port']}}/cam/realmonitor?channel={{channel}}&subtype=0"
        
        cursor.execute('''
            INSERT OR REPLACE INTO cameras 
            (name, rtsp_url, username, password, is_active, camera_type, ip_address, port, serial_number)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            camera_name, rtsp_url, XVR_CONFIG['username'], XVR_CONFIG['password'],
            1, f"{{XVR_CONFIG['type']}}_CH{{channel}}", XVR_CONFIG['ip'], 
            XVR_CONFIG['port'], XVR_CONFIG['serial']
        ))
    
    conn.commit()
    conn.close()
    print("✅ تم استعادة إعدادات XVR")

if __name__ == "__main__":
    restore_xvr_config()
"""
    
    try:
        with open('backup_xvr_config.py', 'w', encoding='utf-8') as f:
            f.write(backup_content)
        print("✅ تم إنشاء ملف النسخ الاحتياطي: backup_xvr_config.py")
    except Exception as e:
        print(f"⚠️ فشل في إنشاء النسخة الاحتياطية: {e}")

def main():
    """الوظيفة الرئيسية"""
    print_banner()
    
    # تأكيد الإضافة
    confirm = input("هل تريد إضافة هذا الجهاز؟ (y/n): ").lower()
    
    if confirm not in ['y', 'yes', 'نعم']:
        print("❌ تم إلغاء العملية")
        return
    
    # إضافة الجهاز
    success, cameras = add_xvr_device()
    
    if not success:
        print("❌ فشل في إضافة الجهاز")
        return
    
    # عرض النتائج
    show_camera_urls(cameras)
    
    # اختبار الاتصال
    test_choice = input("\nهل تريد اختبار الاتصال؟ (y/n): ").lower()
    if test_choice in ['y', 'yes', 'نعم']:
        test_connections(cameras)
    
    # إنشاء نسخة احتياطية
    backup_choice = input("\nهل تريد إنشاء نسخة احتياطية من الإعدادات؟ (y/n): ").lower()
    if backup_choice in ['y', 'yes', 'نعم']:
        create_backup_script()
    
    print("\n" + "=" * 60)
    print("🎉 تم الانتهاء من إضافة الجهاز!")
    print("💡 يمكنك الآن تشغيل النظام ومشاهدة الكاميرات")
    print("🚀 لتشغيل النظام: python main.py")
    print("=" * 60)

if __name__ == "__main__":
    try:
        main()
        input("\nاضغط Enter للخروج...")
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف العملية")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        input("اضغط Enter للخروج...")
