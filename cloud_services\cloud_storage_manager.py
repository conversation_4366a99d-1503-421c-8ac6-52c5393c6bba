#!/usr/bin/env python3
"""
مدير التخزين السحابي المتقدم
Advanced Cloud Storage Manager
"""

import os
import json
import sqlite3
import hashlib
import threading
import time
from datetime import datetime, timedelta
from pathlib import Path
import requests
import zipfile
import shutil

class CloudStorageManager:
    def __init__(self, database_path="database.db", config_path="cloud_config.json"):
        self.database_path = database_path
        self.config_path = config_path
        self.config = self.load_config()
        
        # Storage settings
        self.max_file_size = 100 * 1024 * 1024  # 100MB
        self.compression_enabled = True
        self.encryption_enabled = True
        self.auto_upload_enabled = True
        
        # Upload queue
        self.upload_queue = []
        self.upload_thread = None
        self.upload_running = False
        
        # Supported cloud providers
        self.providers = {
            'google_drive': GoogleDriveProvider(),
            'dropbox': DropboxProvider(),
            'onedrive': OneDriveProvider(),
            'aws_s3': AWSS3Provider(),
            'custom_server': CustomServerProvider()
        }
        
        # Initialize database
        self.init_cloud_database()
        
        # Start upload service
        self.start_upload_service()
    
    def load_config(self):
        """Load cloud configuration"""
        default_config = {
            'enabled': False,
            'provider': 'google_drive',
            'auto_upload': True,
            'upload_important_only': True,
            'compression': True,
            'encryption': True,
            'max_file_size_mb': 100,
            'retention_days': 30,
            'credentials': {}
        }
        
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    # Merge with defaults
                    for key, value in default_config.items():
                        if key not in config:
                            config[key] = value
                    return config
            else:
                return default_config
        except Exception as e:
            print(f"Error loading cloud config: {e}")
            return default_config
    
    def save_config(self):
        """Save cloud configuration"""
        try:
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"Error saving cloud config: {e}")
    
    def init_cloud_database(self):
        """Initialize cloud storage database tables"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()
            
            # Cloud uploads table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS cloud_uploads (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    local_file_path TEXT NOT NULL,
                    cloud_file_path TEXT,
                    provider TEXT NOT NULL,
                    file_size INTEGER,
                    compressed_size INTEGER,
                    upload_status TEXT DEFAULT 'pending',
                    upload_progress REAL DEFAULT 0.0,
                    upload_start_time TIMESTAMP,
                    upload_end_time TIMESTAMP,
                    file_hash TEXT,
                    metadata TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Cloud sync status table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS cloud_sync_status (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    last_sync_time TIMESTAMP,
                    provider TEXT NOT NULL,
                    sync_status TEXT DEFAULT 'idle',
                    files_uploaded INTEGER DEFAULT 0,
                    files_failed INTEGER DEFAULT 0,
                    total_size_uploaded INTEGER DEFAULT 0,
                    error_message TEXT
                )
            ''')
            
            # Cloud settings table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS cloud_settings (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    setting_name TEXT UNIQUE NOT NULL,
                    setting_value TEXT,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"Error initializing cloud database: {e}")
    
    def configure_provider(self, provider_name, credentials):
        """Configure cloud storage provider"""
        if provider_name not in self.providers:
            return False, f"Unsupported provider: {provider_name}"
        
        try:
            # Test connection
            provider = self.providers[provider_name]
            success, message = provider.test_connection(credentials)
            
            if success:
                self.config['provider'] = provider_name
                self.config['credentials'][provider_name] = credentials
                self.config['enabled'] = True
                self.save_config()
                return True, "Provider configured successfully"
            else:
                return False, f"Connection test failed: {message}"
                
        except Exception as e:
            return False, f"Configuration error: {str(e)}"
    
    def queue_file_for_upload(self, file_path, priority='normal', metadata=None):
        """Queue file for cloud upload"""
        if not self.config['enabled']:
            return False, "Cloud storage not enabled"
        
        if not os.path.exists(file_path):
            return False, "File not found"
        
        file_size = os.path.getsize(file_path)
        if file_size > self.max_file_size:
            return False, f"File too large: {file_size} bytes"
        
        # Check if file already uploaded
        file_hash = self.calculate_file_hash(file_path)
        if self.is_file_uploaded(file_hash):
            return False, "File already uploaded"
        
        # Add to queue
        upload_item = {
            'file_path': file_path,
            'priority': priority,
            'metadata': metadata or {},
            'file_hash': file_hash,
            'file_size': file_size,
            'queued_at': datetime.now()
        }
        
        self.upload_queue.append(upload_item)
        
        # Sort queue by priority
        priority_order = {'high': 0, 'normal': 1, 'low': 2}
        self.upload_queue.sort(key=lambda x: priority_order.get(x['priority'], 1))
        
        # Log to database
        self.log_upload_request(upload_item)
        
        return True, "File queued for upload"
    
    def start_upload_service(self):
        """Start background upload service"""
        if self.upload_thread and self.upload_thread.is_alive():
            return
        
        self.upload_running = True
        self.upload_thread = threading.Thread(target=self.upload_worker, daemon=True)
        self.upload_thread.start()
    
    def stop_upload_service(self):
        """Stop background upload service"""
        self.upload_running = False
        if self.upload_thread:
            self.upload_thread.join(timeout=5)
    
    def upload_worker(self):
        """Background worker for uploading files"""
        while self.upload_running:
            try:
                if not self.config['enabled'] or not self.upload_queue:
                    time.sleep(5)
                    continue
                
                # Get next file to upload
                upload_item = self.upload_queue.pop(0)
                
                # Upload file
                success, message = self.upload_file(upload_item)
                
                if success:
                    print(f"✅ Uploaded: {upload_item['file_path']}")
                else:
                    print(f"❌ Upload failed: {upload_item['file_path']} - {message}")
                    
                    # Retry logic for failed uploads
                    if upload_item.get('retry_count', 0) < 3:
                        upload_item['retry_count'] = upload_item.get('retry_count', 0) + 1
                        upload_item['priority'] = 'low'  # Lower priority for retries
                        self.upload_queue.append(upload_item)
                
                # Small delay between uploads
                time.sleep(2)
                
            except Exception as e:
                print(f"Error in upload worker: {e}")
                time.sleep(10)
    
    def upload_file(self, upload_item):
        """Upload a single file to cloud storage"""
        try:
            file_path = upload_item['file_path']
            provider_name = self.config['provider']
            provider = self.providers[provider_name]
            
            # Update status
            self.update_upload_status(upload_item['file_hash'], 'uploading', 0.0)
            
            # Prepare file for upload
            upload_file_path = file_path
            
            # Compression
            if self.config['compression']:
                compressed_path = self.compress_file(file_path)
                if compressed_path:
                    upload_file_path = compressed_path
            
            # Encryption
            if self.config['encryption']:
                encrypted_path = self.encrypt_file(upload_file_path)
                if encrypted_path:
                    upload_file_path = encrypted_path
            
            # Generate cloud file path
            cloud_path = self.generate_cloud_path(file_path)
            
            # Upload to provider
            credentials = self.config['credentials'].get(provider_name, {})
            success, cloud_url = provider.upload_file(
                upload_file_path, cloud_path, credentials,
                progress_callback=lambda p: self.update_upload_status(upload_item['file_hash'], 'uploading', p)
            )
            
            if success:
                # Update status
                self.update_upload_status(upload_item['file_hash'], 'completed', 100.0, cloud_url)
                
                # Clean up temporary files
                if upload_file_path != file_path:
                    try:
                        os.remove(upload_file_path)
                    except:
                        pass
                
                return True, "Upload successful"
            else:
                self.update_upload_status(upload_item['file_hash'], 'failed', 0.0)
                return False, cloud_url  # Error message
                
        except Exception as e:
            self.update_upload_status(upload_item['file_hash'], 'failed', 0.0)
            return False, str(e)
    
    def compress_file(self, file_path):
        """Compress file for upload"""
        try:
            compressed_path = file_path + '.zip'
            
            with zipfile.ZipFile(compressed_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                zipf.write(file_path, os.path.basename(file_path))
            
            # Check if compression was beneficial
            original_size = os.path.getsize(file_path)
            compressed_size = os.path.getsize(compressed_path)
            
            if compressed_size < original_size * 0.9:  # At least 10% reduction
                return compressed_path
            else:
                os.remove(compressed_path)
                return None
                
        except Exception as e:
            print(f"Compression error: {e}")
            return None
    
    def encrypt_file(self, file_path):
        """Encrypt file for upload"""
        try:
            # Simple XOR encryption (replace with proper encryption in production)
            encrypted_path = file_path + '.enc'
            key = self.get_encryption_key()
            
            with open(file_path, 'rb') as infile, open(encrypted_path, 'wb') as outfile:
                while True:
                    chunk = infile.read(8192)
                    if not chunk:
                        break
                    
                    encrypted_chunk = bytes(b ^ key[i % len(key)] for i, b in enumerate(chunk))
                    outfile.write(encrypted_chunk)
            
            return encrypted_path
            
        except Exception as e:
            print(f"Encryption error: {e}")
            return None
    
    def get_encryption_key(self):
        """Get encryption key"""
        # In production, use proper key management
        return b"camera_system_key_2024"
    
    def generate_cloud_path(self, local_path):
        """Generate cloud storage path"""
        # Create organized folder structure
        now = datetime.now()
        year_month = now.strftime("%Y/%m")
        filename = os.path.basename(local_path)
        
        return f"camera_recordings/{year_month}/{filename}"
    
    def calculate_file_hash(self, file_path):
        """Calculate file hash for deduplication"""
        hash_md5 = hashlib.md5()
        try:
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            print(f"Error calculating hash: {e}")
            return None
    
    def is_file_uploaded(self, file_hash):
        """Check if file is already uploaded"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()
            
            cursor.execute(
                "SELECT id FROM cloud_uploads WHERE file_hash = ? AND upload_status = 'completed'",
                (file_hash,)
            )
            
            result = cursor.fetchone()
            conn.close()
            
            return result is not None
            
        except Exception as e:
            print(f"Error checking upload status: {e}")
            return False
    
    def log_upload_request(self, upload_item):
        """Log upload request to database"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO cloud_uploads 
                (local_file_path, provider, file_size, file_hash, metadata)
                VALUES (?, ?, ?, ?, ?)
            ''', (
                upload_item['file_path'],
                self.config['provider'],
                upload_item['file_size'],
                upload_item['file_hash'],
                json.dumps(upload_item['metadata'])
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"Error logging upload request: {e}")
    
    def update_upload_status(self, file_hash, status, progress, cloud_path=None):
        """Update upload status in database"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()
            
            update_fields = ["upload_status = ?", "upload_progress = ?"]
            params = [status, progress]
            
            if status == 'uploading' and progress == 0.0:
                update_fields.append("upload_start_time = CURRENT_TIMESTAMP")
            elif status in ['completed', 'failed']:
                update_fields.append("upload_end_time = CURRENT_TIMESTAMP")
                if cloud_path:
                    update_fields.append("cloud_file_path = ?")
                    params.append(cloud_path)
            
            params.append(file_hash)
            
            query = f"UPDATE cloud_uploads SET {', '.join(update_fields)} WHERE file_hash = ?"
            cursor.execute(query, params)
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"Error updating upload status: {e}")
    
    def get_upload_statistics(self):
        """Get upload statistics"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()
            
            # Total uploads
            cursor.execute("SELECT COUNT(*) FROM cloud_uploads")
            total_uploads = cursor.fetchone()[0]
            
            # Completed uploads
            cursor.execute("SELECT COUNT(*) FROM cloud_uploads WHERE upload_status = 'completed'")
            completed_uploads = cursor.fetchone()[0]
            
            # Failed uploads
            cursor.execute("SELECT COUNT(*) FROM cloud_uploads WHERE upload_status = 'failed'")
            failed_uploads = cursor.fetchone()[0]
            
            # Total size uploaded
            cursor.execute("SELECT SUM(file_size) FROM cloud_uploads WHERE upload_status = 'completed'")
            total_size = cursor.fetchone()[0] or 0
            
            # Recent uploads (last 24 hours)
            cursor.execute('''
                SELECT COUNT(*) FROM cloud_uploads 
                WHERE upload_status = 'completed' 
                AND upload_end_time > datetime('now', '-24 hours')
            ''')
            recent_uploads = cursor.fetchone()[0]
            
            conn.close()
            
            return {
                'total_uploads': total_uploads,
                'completed_uploads': completed_uploads,
                'failed_uploads': failed_uploads,
                'success_rate': (completed_uploads / total_uploads * 100) if total_uploads > 0 else 0,
                'total_size_uploaded': total_size,
                'recent_uploads_24h': recent_uploads,
                'queue_size': len(self.upload_queue)
            }
            
        except Exception as e:
            print(f"Error getting upload statistics: {e}")
            return {}
    
    def get_recent_uploads(self, limit=50):
        """Get recent upload history"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT local_file_path, cloud_file_path, upload_status, 
                       upload_progress, upload_start_time, upload_end_time, file_size
                FROM cloud_uploads 
                ORDER BY created_at DESC 
                LIMIT ?
            ''', (limit,))
            
            uploads = cursor.fetchall()
            conn.close()
            
            return [
                {
                    'local_path': u[0],
                    'cloud_path': u[1],
                    'status': u[2],
                    'progress': u[3],
                    'start_time': u[4],
                    'end_time': u[5],
                    'file_size': u[6]
                }
                for u in uploads
            ]
            
        except Exception as e:
            print(f"Error getting recent uploads: {e}")
            return []
    
    def cleanup_old_uploads(self, days=30):
        """Clean up old upload records"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                DELETE FROM cloud_uploads 
                WHERE created_at < datetime('now', '-{} days')
                AND upload_status IN ('completed', 'failed')
            '''.format(days))
            
            deleted_count = cursor.rowcount
            conn.commit()
            conn.close()
            
            return deleted_count
            
        except Exception as e:
            print(f"Error cleaning up uploads: {e}")
            return 0


# Cloud provider base class and implementations
class CloudProvider:
    """Base class for cloud storage providers"""
    
    def test_connection(self, credentials):
        """Test connection to cloud provider"""
        raise NotImplementedError
    
    def upload_file(self, local_path, cloud_path, credentials, progress_callback=None):
        """Upload file to cloud storage"""
        raise NotImplementedError
    
    def download_file(self, cloud_path, local_path, credentials, progress_callback=None):
        """Download file from cloud storage"""
        raise NotImplementedError
    
    def delete_file(self, cloud_path, credentials):
        """Delete file from cloud storage"""
        raise NotImplementedError


class GoogleDriveProvider(CloudProvider):
    """Google Drive storage provider"""
    
    def test_connection(self, credentials):
        # Implementation for Google Drive API
        return True, "Google Drive connection test (placeholder)"
    
    def upload_file(self, local_path, cloud_path, credentials, progress_callback=None):
        # Implementation for Google Drive upload
        return True, f"https://drive.google.com/file/{cloud_path}"


class DropboxProvider(CloudProvider):
    """Dropbox storage provider"""
    
    def test_connection(self, credentials):
        return True, "Dropbox connection test (placeholder)"
    
    def upload_file(self, local_path, cloud_path, credentials, progress_callback=None):
        return True, f"https://dropbox.com/{cloud_path}"


class OneDriveProvider(CloudProvider):
    """OneDrive storage provider"""
    
    def test_connection(self, credentials):
        return True, "OneDrive connection test (placeholder)"
    
    def upload_file(self, local_path, cloud_path, credentials, progress_callback=None):
        return True, f"https://onedrive.com/{cloud_path}"


class AWSS3Provider(CloudProvider):
    """AWS S3 storage provider"""
    
    def test_connection(self, credentials):
        return True, "AWS S3 connection test (placeholder)"
    
    def upload_file(self, local_path, cloud_path, credentials, progress_callback=None):
        return True, f"https://s3.amazonaws.com/{cloud_path}"


class CustomServerProvider(CloudProvider):
    """Custom server storage provider"""
    
    def test_connection(self, credentials):
        return True, "Custom server connection test (placeholder)"
    
    def upload_file(self, local_path, cloud_path, credentials, progress_callback=None):
        return True, f"https://custom-server.com/{cloud_path}"
