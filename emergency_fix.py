#!/usr/bin/env python3
"""
حل طارئ سريع لمشكلة إغلاق النظام
Emergency Quick Fix
"""

import sys
import os
import subprocess
import time

def emergency_fix():
    """حل طارئ سريع"""
    print("🚨 حل طارئ سريع - Emergency Fix")
    print("=" * 40)
    
    try:
        # 1. إنهاء جميع عمليات Python
        print("1️⃣ إنهاء عمليات Python...")
        try:
            subprocess.run(['taskkill', '/F', '/IM', 'python.exe'], 
                          capture_output=True, timeout=5)
            subprocess.run(['taskkill', '/F', '/IM', 'pythonw.exe'], 
                          capture_output=True, timeout=5)
            print("✅ تم إنهاء العمليات")
        except:
            print("⚠️ لا توجد عمليات للإنهاء")
        
        time.sleep(2)
        
        # 2. حذف قاعدة البيانات المقفلة
        print("2️⃣ حذف قاعدة البيانات...")
        try:
            if os.path.exists('database.db'):
                os.remove('database.db')
                print("✅ تم حذف قاعدة البيانات")
            else:
                print("ℹ️ قاعدة البيانات غير موجودة")
        except Exception as e:
            print(f"⚠️ لا يمكن حذف قاعدة البيانات: {e}")
        
        # 3. تنظيف ملفات مؤقتة
        print("3️⃣ تنظيف ملفات مؤقتة...")
        try:
            import shutil
            for root, dirs, files in os.walk('.'):
                if '__pycache__' in dirs:
                    shutil.rmtree(os.path.join(root, '__pycache__'))
                for file in files:
                    if file.endswith('.pyc'):
                        os.remove(os.path.join(root, file))
            print("✅ تم التنظيف")
        except:
            print("⚠️ تم تخطي التنظيف")
        
        # 4. إنشاء قاعدة بيانات جديدة
        print("4️⃣ إنشاء قاعدة بيانات جديدة...")
        try:
            import sqlite3
            import hashlib
            
            conn = sqlite3.connect('database.db')
            cursor = conn.cursor()
            
            # إنشاء جدول المستخدمين
            cursor.execute('''
                CREATE TABLE users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    password TEXT NOT NULL,
                    role TEXT DEFAULT 'user',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_login TIMESTAMP
                )
            ''')
            
            # إضافة مستخدم admin
            hashed_password = hashlib.sha256('admin123'.encode()).hexdigest()
            cursor.execute(
                "INSERT INTO users (username, password, role) VALUES (?, ?, ?)",
                ('admin', hashed_password, 'admin')
            )
            
            # إنشاء جدول الكاميرات
            cursor.execute('''
                CREATE TABLE cameras (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    url TEXT NOT NULL,
                    camera_type TEXT DEFAULT 'rtsp',
                    position_x INTEGER DEFAULT 0,
                    position_y INTEGER DEFAULT 0,
                    enabled BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # إنشاء جدول الأحداث
            cursor.execute('''
                CREATE TABLE motion_events (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    camera_id INTEGER,
                    confidence REAL,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (camera_id) REFERENCES cameras (id)
                )
            ''')
            
            conn.commit()
            conn.close()
            print("✅ تم إنشاء قاعدة البيانات")
            
        except Exception as e:
            print(f"❌ خطأ في قاعدة البيانات: {e}")
            return False
        
        # 5. إنشاء مشغل مبسط
        print("5️⃣ إنشاء مشغل مبسط...")
        
        simple_launcher = '''#!/usr/bin/env python3
import sys
import os

# إضافة المسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from PyQt5.QtWidgets import QApplication
    from ui.login_window import LoginWindow
    from ui.main_window import MainWindow
    
    app = QApplication(sys.argv)
    
    # نافذة تسجيل الدخول
    login = LoginWindow()
    if login.exec_() == LoginWindow.Accepted:
        # النافذة الرئيسية
        main_window = MainWindow()
        user_info = login.user_manager.get_current_user()
        if user_info:
            main_window.set_current_user(user_info)
        main_window.show()
        sys.exit(app.exec_())
    
except Exception as e:
    print(f"خطأ: {e}")
    input("اضغط Enter...")
'''
        
        with open('simple_start.py', 'w', encoding='utf-8') as f:
            f.write(simple_launcher)
        
        print("✅ تم إنشاء المشغل المبسط")
        
        print("\n🎉 تم الإصلاح الطارئ!")
        print("🚀 شغل النظام الآن:")
        print("python simple_start.py")
        
        return True
        
    except Exception as e:
        print(f"❌ فشل الإصلاح الطارئ: {e}")
        return False

def install_requirements():
    """تثبيت المتطلبات بسرعة"""
    print("📦 تثبيت المتطلبات...")
    
    basic_packages = [
        'opencv-python',
        'PyQt5', 
        'numpy',
        'Pillow'
    ]
    
    for package in basic_packages:
        try:
            print(f"تثبيت {package}...")
            subprocess.run([sys.executable, '-m', 'pip', 'install', package], 
                          check=True, timeout=60, capture_output=True)
            print(f"✅ {package}")
        except:
            print(f"❌ {package}")

def main():
    """الوظيفة الرئيسية"""
    print("🚨 إصلاح طارئ سريع")
    print("=" * 30)
    
    # تثبيت المتطلبات أولاً
    install_requirements()
    
    # تشغيل الإصلاح الطارئ
    if emergency_fix():
        print("\n✅ تم الإصلاح!")
        
        # محاولة تشغيل النظام مباشرة
        try:
            print("🚀 محاولة تشغيل النظام...")
            subprocess.run([sys.executable, 'simple_start.py'])
        except:
            print("⚠️ شغل النظام يدوياً: python simple_start.py")
    else:
        print("\n❌ فشل الإصلاح")
    
    input("اضغط Enter...")

if __name__ == "__main__":
    main()
