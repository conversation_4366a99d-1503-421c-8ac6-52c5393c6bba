#!/usr/bin/env python3
"""
إصلاح سريع لمشاكل النظام الشائعة
Quick Fix for Common System Issues
"""

import sys
import os
import sqlite3
import json
import subprocess
import time

def print_quick_fix_banner():
    """طباعة شعار الإصلاح السريع"""
    print("=" * 50)
    print("⚡ الإصلاح السريع للنظام")
    print("⚡ Quick System Fix")
    print("=" * 50)
    print()

def fix_1_install_packages():
    """إصلاح 1: تثبيت المكتبات المطلوبة"""
    print("📦 إصلاح 1: تثبيت المكتبات المطلوبة...")
    
    packages = [
        'opencv-python',
        'numpy', 
        'PyQt5',
        'Pillow',
        'requests'
    ]
    
    try:
        print("🔄 جاري التثبيت...")
        subprocess.run([
            sys.executable, '-m', 'pip', 'install', '--upgrade'
        ] + packages, check=True)
        
        print("✅ تم تثبيت جميع المكتبات")
        return True
        
    except Exception as e:
        print(f"❌ فشل التثبيت: {e}")
        return False

def fix_2_create_database():
    """إصلاح 2: إنشاء/إصلاح قاعدة البيانات"""
    print("🗄️ إصلاح 2: إنشاء/إصلاح قاعدة البيانات...")
    
    try:
        # نسخ احتياطي إذا كانت موجودة
        if os.path.exists('database.db'):
            backup_name = f'database_backup_{int(time.time())}.db'
            os.rename('database.db', backup_name)
            print(f"📋 نسخة احتياطية: {backup_name}")
        
        # إنشاء قاعدة بيانات جديدة
        conn = sqlite3.connect('database.db')
        cursor = conn.cursor()
        
        # جدول المستخدمين
        cursor.execute('''
            CREATE TABLE users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password TEXT NOT NULL,
                role TEXT DEFAULT 'user',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول الكاميرات
        cursor.execute('''
            CREATE TABLE cameras (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                rtsp_url TEXT NOT NULL,
                username TEXT,
                password TEXT,
                is_active BOOLEAN DEFAULT 1,
                camera_type TEXT,
                ip_address TEXT,
                port INTEGER DEFAULT 554,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول التسجيلات
        cursor.execute('''
            CREATE TABLE recordings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                camera_id INTEGER,
                filename TEXT NOT NULL,
                start_time TIMESTAMP,
                end_time TIMESTAMP,
                file_size INTEGER,
                FOREIGN KEY (camera_id) REFERENCES cameras (id)
            )
        ''')
        
        # جداول الميزات المتقدمة
        cursor.execute('''
            CREATE TABLE motion_detections (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                camera_id INTEGER,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                motion_area INTEGER,
                confidence REAL,
                image_path TEXT,
                FOREIGN KEY (camera_id) REFERENCES cameras (id)
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE face_detections (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                camera_id INTEGER,
                person_name TEXT,
                confidence REAL,
                is_known BOOLEAN,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                image_path TEXT,
                FOREIGN KEY (camera_id) REFERENCES cameras (id)
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE license_plates (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                camera_id INTEGER,
                plate_number TEXT,
                confidence REAL,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                image_path TEXT,
                is_whitelisted BOOLEAN DEFAULT 0,
                is_blacklisted BOOLEAN DEFAULT 0,
                FOREIGN KEY (camera_id) REFERENCES cameras (id)
            )
        ''')
        
        # إضافة مستخدم افتراضي
        cursor.execute('''
            INSERT INTO users (username, password, role)
            VALUES ('admin', 'admin123', 'admin')
        ''')
        
        # إضافة كاميرات XVR
        ip = "**************"
        username = "admin"
        password = "Mnbv@1978"
        
        for channel in range(1, 9):
            camera_name = f"XVR قناة {channel}"
            rtsp_url = f"rtsp://{username}:{password}@{ip}:554/cam/realmonitor?channel={channel}&subtype=0"
            
            cursor.execute('''
                INSERT INTO cameras 
                (name, rtsp_url, username, password, is_active, camera_type, ip_address, port)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (camera_name, rtsp_url, username, password, 1, f"XVR_CH{channel}", ip, 554))
        
        conn.commit()
        conn.close()
        
        print("✅ تم إنشاء قاعدة البيانات مع 8 كاميرات XVR")
        return True
        
    except Exception as e:
        print(f"❌ فشل في إنشاء قاعدة البيانات: {e}")
        return False

def fix_3_create_config():
    """إصلاح 3: إنشاء ملف الإعدادات"""
    print("⚙️ إصلاح 3: إنشاء ملف الإعدادات...")
    
    try:
        # إعدادات أساسية
        config = {
            "app_name": "نظام مراقبة الكاميرات المتقدم",
            "version": "2.0",
            "language": "ar",
            "theme": "dark",
            "auto_save": True,
            "recording_path": "recordings",
            "max_recording_size_mb": 1000,
            "camera_settings": {
                "default_fps": 15,
                "default_quality": "medium",
                "motion_detection": True,
                "face_recognition": True,
                "license_plate_recognition": True
            },
            "xvr_settings": {
                "ip": "**************",
                "username": "admin", 
                "password": "Mnbv@1978",
                "port": 554,
                "channels": 8
            }
        }
        
        with open('config.json', 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        # إعدادات متقدمة
        advanced_config = {
            "system_info": {
                "version": "2.0",
                "activated_date": time.strftime("%Y-%m-%d %H:%M:%S"),
                "features_enabled": True
            },
            "motion_detection": {
                "enabled": True,
                "sensitivity": 0.3,
                "min_area": 500,
                "record_on_motion": True,
                "alert_on_motion": True
            },
            "face_recognition": {
                "enabled": True,
                "confidence_threshold": 0.6,
                "detection_frequency": 2,
                "alert_on_unknown": True
            },
            "license_plate_recognition": {
                "enabled": True,
                "confidence_threshold": 0.7,
                "save_plate_images": True,
                "alert_on_detection": True
            }
        }
        
        with open('advanced_config.json', 'w', encoding='utf-8') as f:
            json.dump(advanced_config, f, indent=2, ensure_ascii=False)
        
        print("✅ تم إنشاء ملفات الإعدادات")
        return True
        
    except Exception as e:
        print(f"❌ فشل في إنشاء الإعدادات: {e}")
        return False

def fix_4_create_directories():
    """إصلاح 4: إنشاء المجلدات المطلوبة"""
    print("📁 إصلاح 4: إنشاء المجلدات المطلوبة...")
    
    directories = [
        'recordings',
        'assets', 
        'logs',
        'ui',
        'core',
        'utils',
        'motion_captures',
        'face_database',
        'unknown_faces',
        'plates_database',
        'behavior_events',
        'ai_models',
        'temp_processing',
        'exports'
    ]
    
    created_count = 0
    
    for directory in directories:
        try:
            os.makedirs(directory, exist_ok=True)
            print(f"✅ {directory}/")
            created_count += 1
        except Exception as e:
            print(f"❌ فشل في إنشاء {directory}: {e}")
    
    print(f"📊 تم إنشاء {created_count}/{len(directories)} مجلد")
    return created_count > 0

def fix_5_create_basic_files():
    """إصلاح 5: إنشاء الملفات الأساسية المفقودة"""
    print("📄 إصلاح 5: إنشاء الملفات الأساسية...")
    
    # ملف requirements.txt
    requirements_content = """opencv-python>=4.5.0
numpy>=1.19.0
PyQt5>=5.15.0
Pillow>=8.0.0
requests>=2.25.0
sqlite3
face-recognition
pytesseract
scikit-learn
"""
    
    try:
        with open('requirements.txt', 'w') as f:
            f.write(requirements_content)
        print("✅ requirements.txt")
    except Exception as e:
        print(f"❌ فشل في إنشاء requirements.txt: {e}")
    
    # ملف README.md
    readme_content = """# نظام مراقبة الكاميرات المتقدم

## التشغيل السريع
```bash
python START_SAFE.py
```

## إصلاح المشاكل
```bash
python QUICK_FIX.py
```

## الميزات
- كشف الحركة المتقدم
- التعرف على الوجوه
- قراءة أرقام السيارات
- دعم كاميرات XVR

## المتطلبات
- Python 3.6+
- OpenCV
- PyQt5
- NumPy
"""
    
    try:
        with open('README.md', 'w', encoding='utf-8') as f:
            f.write(readme_content)
        print("✅ README.md")
    except Exception as e:
        print(f"❌ فشل في إنشاء README.md: {e}")
    
    return True

def fix_6_test_system():
    """إصلاح 6: اختبار النظام"""
    print("🧪 إصلاح 6: اختبار النظام...")
    
    tests_passed = 0
    total_tests = 4
    
    # اختبار 1: Python modules
    try:
        import cv2, numpy, sqlite3
        from PyQt5.QtWidgets import QApplication
        print("✅ اختبار المكتبات")
        tests_passed += 1
    except Exception as e:
        print(f"❌ اختبار المكتبات: {e}")
    
    # اختبار 2: Database
    try:
        conn = sqlite3.connect('database.db')
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM users")
        user_count = cursor.fetchone()[0]
        cursor.execute("SELECT COUNT(*) FROM cameras") 
        camera_count = cursor.fetchone()[0]
        conn.close()
        print(f"✅ اختبار قاعدة البيانات: {user_count} مستخدم، {camera_count} كاميرا")
        tests_passed += 1
    except Exception as e:
        print(f"❌ اختبار قاعدة البيانات: {e}")
    
    # اختبار 3: Config files
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        print("✅ اختبار ملف الإعدادات")
        tests_passed += 1
    except Exception as e:
        print(f"❌ اختبار ملف الإعدادات: {e}")
    
    # اختبار 4: Directories
    required_dirs = ['recordings', 'ui', 'core', 'utils']
    dirs_exist = all(os.path.exists(d) for d in required_dirs)
    if dirs_exist:
        print("✅ اختبار المجلدات")
        tests_passed += 1
    else:
        print("❌ اختبار المجلدات")
    
    print(f"📊 نجح {tests_passed}/{total_tests} اختبار")
    return tests_passed >= 3

def run_all_fixes():
    """تشغيل جميع الإصلاحات"""
    print("🔧 تشغيل جميع الإصلاحات...")
    
    fixes = [
        ("تثبيت المكتبات", fix_1_install_packages),
        ("إنشاء قاعدة البيانات", fix_2_create_database),
        ("إنشاء ملفات الإعدادات", fix_3_create_config),
        ("إنشاء المجلدات", fix_4_create_directories),
        ("إنشاء الملفات الأساسية", fix_5_create_basic_files),
        ("اختبار النظام", fix_6_test_system)
    ]
    
    completed_fixes = 0
    
    for fix_name, fix_function in fixes:
        print(f"\n🔄 {fix_name}...")
        try:
            if fix_function():
                print(f"✅ {fix_name}: مكتمل")
                completed_fixes += 1
            else:
                print(f"⚠️ {fix_name}: مكتمل جزئ<|im_start|>")
        except Exception as e:
            print(f"❌ {fix_name}: فشل - {e}")
    
    return completed_fixes, len(fixes)

def main():
    """الوظيفة الرئيسية"""
    print_quick_fix_banner()
    
    print("🎯 هذا الإصلاح السريع سيقوم بـ:")
    print("1. تثبيت جميع المكتبات المطلوبة")
    print("2. إنشاء قاعدة بيانات جديدة مع 8 كاميرات XVR")
    print("3. إنشاء ملفات الإعدادات")
    print("4. إنشاء جميع المجلدات المطلوبة")
    print("5. إنشاء الملفات الأساسية")
    print("6. اختبار النظام")
    print()
    
    confirm = input("هل تريد المتابعة؟ (y/n): ").lower()
    if confirm not in ['y', 'yes', 'نعم']:
        print("❌ تم إلغاء الإصلاح")
        return
    
    print("\n🚀 بدء الإصلاح السريع...")
    
    # تشغيل جميع الإصلاحات
    completed, total = run_all_fixes()
    
    # النتائج
    print("\n" + "=" * 50)
    print("📊 نتائج الإصلاح السريع:")
    print("=" * 50)
    print(f"✅ تم إكمال {completed}/{total} إصلاح")
    
    if completed >= 4:
        print("🎉 الإصلاح السريع نجح!")
        print("\n🚀 النظام جاهز للتشغيل:")
        print("• python START_SAFE.py - للتشغيل الآمن")
        print("• python main.py - للتشغيل المباشر")
        print("• python COMPLETE_SETUP.py - للإعداد الشامل")
        
        # خيار التشغيل المباشر
        run_now = input("\nهل تريد تشغيل النظام الآن؟ (y/n): ").lower()
        if run_now in ['y', 'yes', 'نعم']:
            print("\n🚀 تشغيل النظام...")
            try:
                subprocess.run([sys.executable, 'START_SAFE.py'])
            except Exception as e:
                print(f"❌ فشل التشغيل: {e}")
                print("💡 جرب: python START_SAFE.py")
    else:
        print("⚠️ الإصلاح غير مكتمل")
        print("💡 جرب تشغيل fix_system_crash.py للتشخيص المفصل")
    
    print("\n💡 ملفات مهمة تم إنشاؤها:")
    print("• database.db - قاعدة البيانات مع 8 كاميرات XVR")
    print("• config.json - إعدادات النظام")
    print("• advanced_config.json - إعدادات الميزات المتقدمة")
    print("• requirements.txt - قائمة المكتبات المطلوبة")
    print("• START_SAFE.py - المشغل الآمن")

if __name__ == "__main__":
    try:
        main()
        input("\nاضغط Enter للخروج...")
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الإصلاح")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        input("اضغط Enter للخروج...")
