import json
import os

class ConfigManager:
    def __init__(self, config_file="config.json"):
        self.config_file = config_file
        self.config = self.load_config()
    
    def load_config(self):
        """Load configuration from JSON file"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"Error loading config: {e}")
                return self.get_default_config()
        else:
            return self.get_default_config()
    
    def get_default_config(self):
        """Return default configuration"""
        return {
            "app_settings": {
                "window_width": 1280,
                "window_height": 800,
                "theme": "dark",
                "language": "arabic",
                "fullscreen": False,
                "auto_record": True,
                "motion_sensitivity": 50
            },
            "cameras": [],
            "recording_settings": {
                "format": "mp4",
                "quality": "high",
                "fps": 30,
                "duration_minutes": 60,
                "storage_path": "recordings/"
            },
            "motion_detection": {
                "enabled": True,
                "threshold": 25,
                "min_area": 500,
                "blur_size": 21
            },
            "database": {
                "path": "database.db"
            }
        }
    
    def save_config(self):
        """Save configuration to JSON file"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=4, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"Error saving config: {e}")
            return False
    
    def get(self, key, default=None):
        """Get configuration value"""
        keys = key.split('.')
        value = self.config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def set(self, key, value):
        """Set configuration value"""
        keys = key.split('.')
        config = self.config
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
        self.save_config()
    
    def get_camera_grid_size(self, camera_count):
        """Calculate optimal grid size for cameras"""
        if camera_count <= 1:
            return (1, 1)
        elif camera_count <= 4:
            return (2, 2)
        elif camera_count <= 9:
            return (3, 3)
        elif camera_count <= 16:
            return (4, 4)
        else:
            return (4, 4)  # Maximum 16 cameras
    
    def get_camera_size(self, grid_rows, grid_cols, window_width, window_height):
        """Calculate camera display size based on grid"""
        # Reserve space for UI elements
        available_width = window_width - 100
        available_height = window_height - 150
        
        camera_width = available_width // grid_cols
        camera_height = available_height // grid_rows
        
        return (camera_width, camera_height)
