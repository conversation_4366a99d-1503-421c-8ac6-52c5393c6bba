import sys
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.arabic_support import ArabicText
from core.database import DatabaseManager
from core.camera_handler import CameraHandler

class CameraManagerDialog(QDialog):
    cameras_updated = pyqtSignal()  # Signal emitted when cameras are updated
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.db_manager = DatabaseManager()
        self.setup_ui()
        self.apply_dark_theme()
        self.load_cameras()
        
    def setup_ui(self):
        """Setup the camera manager interface"""
        self.setWindowTitle(ArabicText.get('cameras'))
        self.setMinimumSize(800, 600)
        self.setModal(True)
        
        # Main layout
        main_layout = QVBoxLayout()
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # Title
        title_label = QLabel(ArabicText.get('cameras'))
        title_label.setStyleSheet("font-size: 20px; font-weight: bold; color: #ecf0f1; margin-bottom: 10px;")
        main_layout.addWidget(title_label)
        
        # Toolbar
        toolbar_layout = QHBoxLayout()
        
        self.add_button = QPushButton(ArabicText.get('add_camera'))
        self.add_button.clicked.connect(self.add_camera)
        toolbar_layout.addWidget(self.add_button)
        
        self.edit_button = QPushButton(ArabicText.get('edit_camera'))
        self.edit_button.clicked.connect(self.edit_camera)
        self.edit_button.setEnabled(False)
        toolbar_layout.addWidget(self.edit_button)
        
        self.delete_button = QPushButton(ArabicText.get('delete_camera'))
        self.delete_button.clicked.connect(self.delete_camera)
        self.delete_button.setEnabled(False)
        toolbar_layout.addWidget(self.delete_button)
        
        self.test_button = QPushButton("Test Connection")
        self.test_button.clicked.connect(self.test_connection)
        self.test_button.setEnabled(False)
        toolbar_layout.addWidget(self.test_button)
        
        toolbar_layout.addStretch()
        
        self.refresh_button = QPushButton("Refresh")
        self.refresh_button.clicked.connect(self.load_cameras)
        toolbar_layout.addWidget(self.refresh_button)
        
        main_layout.addLayout(toolbar_layout)
        
        # Camera list
        self.camera_table = QTableWidget()
        self.camera_table.setColumnCount(5)
        self.camera_table.setHorizontalHeaderLabels([
            ArabicText.get('camera_name'),
            ArabicText.get('camera_url'),
            ArabicText.get('camera_type'),
            "Status",
            "Actions"
        ])
        
        # Configure table
        header = self.camera_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        
        self.camera_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.camera_table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.camera_table.selectionModel().selectionChanged.connect(self.on_selection_changed)
        
        main_layout.addWidget(self.camera_table)
        
        # Status bar
        self.status_label = QLabel("Ready")
        self.status_label.setStyleSheet("color: #95a5a6; font-size: 12px;")
        main_layout.addWidget(self.status_label)
        
        # Buttons
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.close_button = QPushButton("Close")
        self.close_button.clicked.connect(self.accept)
        button_layout.addWidget(self.close_button)
        
        main_layout.addLayout(button_layout)
        
        self.setLayout(main_layout)
    
    def apply_dark_theme(self):
        """Apply dark theme"""
        self.setStyleSheet("""
            QDialog {
                background-color: #2c3e50;
                color: #ecf0f1;
            }
            
            QTableWidget {
                background-color: #34495e;
                alternate-background-color: #2c3e50;
                gridline-color: #5d6d7e;
                color: #ecf0f1;
                border: 1px solid #5d6d7e;
                border-radius: 5px;
            }
            
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #5d6d7e;
            }
            
            QTableWidget::item:selected {
                background-color: #3498db;
            }
            
            QHeaderView::section {
                background-color: #34495e;
                color: #ecf0f1;
                padding: 8px;
                border: 1px solid #5d6d7e;
                font-weight: bold;
            }
            
            QPushButton {
                background-color: #3498db;
                border: none;
                border-radius: 5px;
                color: white;
                padding: 8px 16px;
                font-weight: bold;
            }
            
            QPushButton:hover {
                background-color: #5dade2;
            }
            
            QPushButton:pressed {
                background-color: #2980b9;
            }
            
            QPushButton:disabled {
                background-color: #7f8c8d;
                color: #bdc3c7;
            }
            
            QLabel {
                color: #ecf0f1;
            }
        """)
    
    def load_cameras(self):
        """Load cameras from database"""
        try:
            cameras = self.db_manager.get_cameras()
            self.camera_table.setRowCount(len(cameras))
            
            for row, camera in enumerate(cameras):
                camera_id, name, url, camera_type, pos_x, pos_y, enabled, created_at = camera
                
                # Name
                self.camera_table.setItem(row, 0, QTableWidgetItem(name))
                
                # URL (truncate if too long)
                display_url = url if len(url) <= 50 else url[:47] + "..."
                self.camera_table.setItem(row, 1, QTableWidgetItem(display_url))
                
                # Type
                type_text = dict(ArabicText.get_camera_types()).get(camera_type, camera_type)
                self.camera_table.setItem(row, 2, QTableWidgetItem(type_text))
                
                # Status
                status_item = QTableWidgetItem("Unknown")
                status_item.setData(Qt.UserRole, camera_id)  # Store camera ID
                self.camera_table.setItem(row, 3, status_item)
                
                # Actions (placeholder)
                self.camera_table.setItem(row, 4, QTableWidgetItem(""))
            
            self.status_label.setText(f"Loaded {len(cameras)} cameras")
            
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to load cameras: {str(e)}")
    
    def on_selection_changed(self):
        """Handle table selection change"""
        has_selection = len(self.camera_table.selectionModel().selectedRows()) > 0
        self.edit_button.setEnabled(has_selection)
        self.delete_button.setEnabled(has_selection)
        self.test_button.setEnabled(has_selection)
    
    def add_camera(self):
        """Add new camera"""
        dialog = CameraEditDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            camera_data = dialog.get_camera_data()
            try:
                self.db_manager.add_camera(
                    camera_data['name'],
                    camera_data['url'],
                    camera_data['type']
                )
                self.load_cameras()
                self.cameras_updated.emit()
                self.status_label.setText("Camera added successfully")
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to add camera: {str(e)}")
    
    def edit_camera(self):
        """Edit selected camera"""
        selected_rows = self.camera_table.selectionModel().selectedRows()
        if not selected_rows:
            return
        
        row = selected_rows[0].row()
        camera_id = self.camera_table.item(row, 3).data(Qt.UserRole)
        
        # Get camera data
        cameras = self.db_manager.get_cameras()
        camera_data = None
        for camera in cameras:
            if camera[0] == camera_id:
                camera_data = {
                    'id': camera[0],
                    'name': camera[1],
                    'url': camera[2],
                    'type': camera[3]
                }
                break
        
        if camera_data:
            dialog = CameraEditDialog(self, camera_data)
            if dialog.exec_() == QDialog.Accepted:
                updated_data = dialog.get_camera_data()
                try:
                    self.db_manager.update_camera(
                        camera_id,
                        updated_data['name'],
                        updated_data['url'],
                        updated_data['type']
                    )
                    self.load_cameras()
                    self.cameras_updated.emit()
                    self.status_label.setText("Camera updated successfully")
                except Exception as e:
                    QMessageBox.critical(self, "Error", f"Failed to update camera: {str(e)}")
    
    def delete_camera(self):
        """Delete selected camera"""
        selected_rows = self.camera_table.selectionModel().selectedRows()
        if not selected_rows:
            return
        
        row = selected_rows[0].row()
        camera_name = self.camera_table.item(row, 0).text()
        camera_id = self.camera_table.item(row, 3).data(Qt.UserRole)
        
        reply = QMessageBox.question(
            self, "Confirm Delete",
            f"Are you sure you want to delete camera '{camera_name}'?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                self.db_manager.delete_camera(camera_id)
                self.load_cameras()
                self.cameras_updated.emit()
                self.status_label.setText("Camera deleted successfully")
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to delete camera: {str(e)}")
    
    def test_connection(self):
        """Test connection to selected camera"""
        selected_rows = self.camera_table.selectionModel().selectedRows()
        if not selected_rows:
            return
        
        row = selected_rows[0].row()
        camera_url = self.camera_table.item(row, 1).text()
        camera_type = self.camera_table.item(row, 2).text()
        
        # Convert display type back to internal type
        type_mapping = {v: k for k, v in ArabicText.get_camera_types()}
        internal_type = type_mapping.get(camera_type, 'rtsp')
        
        self.status_label.setText("Testing connection...")
        QApplication.processEvents()
        
        success, message = CameraHandler.test_camera_connection(camera_url, internal_type)
        
        if success:
            self.status_label.setText("Connection successful")
            QMessageBox.information(self, "Connection Test", "Camera connection successful!")
        else:
            self.status_label.setText("Connection failed")
            QMessageBox.warning(self, "Connection Test", f"Connection failed: {message}")


class CameraEditDialog(QDialog):
    def __init__(self, parent=None, camera_data=None):
        super().__init__(parent)
        self.camera_data = camera_data
        self.setup_ui()
        self.apply_dark_theme()
        
        if camera_data:
            self.load_camera_data()
    
    def setup_ui(self):
        """Setup camera edit dialog"""
        title = ArabicText.get('edit_camera') if self.camera_data else ArabicText.get('add_camera')
        self.setWindowTitle(title)
        self.setFixedSize(500, 400)
        self.setModal(True)
        
        layout = QVBoxLayout()
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # Form
        form_layout = QFormLayout()
        form_layout.setSpacing(10)
        
        # Camera name
        self.name_input = QLineEdit()
        self.name_input.setPlaceholderText("Enter camera name")
        form_layout.addRow(ArabicText.get('camera_name') + ":", self.name_input)
        
        # Camera URL
        self.url_input = QLineEdit()
        self.url_input.setPlaceholderText("rtsp://admin:password@192.168.1.100:554/stream")
        form_layout.addRow(ArabicText.get('camera_url') + ":", self.url_input)
        
        # Camera type
        self.type_combo = QComboBox()
        for type_code, type_name in ArabicText.get_camera_types():
            self.type_combo.addItem(type_name, type_code)
        form_layout.addRow(ArabicText.get('camera_type') + ":", self.type_combo)
        
        layout.addLayout(form_layout)
        
        # DVR Helper section
        dvr_group = QGroupBox("DVR/NVR Helper")
        dvr_layout = QVBoxLayout()
        
        dvr_info = QLabel("For DVR/NVR systems, enter base URL and channels will be auto-generated:")
        dvr_info.setWordWrap(True)
        dvr_info.setStyleSheet("color: #95a5a6; font-size: 12px;")
        dvr_layout.addWidget(dvr_info)
        
        dvr_example = QLabel("Example: rtsp://admin:password@192.168.1.100:554/Streaming/Channels/{}/")
        dvr_example.setStyleSheet("color: #3498db; font-size: 11px; font-family: monospace;")
        dvr_layout.addWidget(dvr_example)
        
        dvr_group.setLayout(dvr_layout)
        layout.addWidget(dvr_group)
        
        # Buttons
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.test_button = QPushButton("Test Connection")
        self.test_button.clicked.connect(self.test_connection)
        button_layout.addWidget(self.test_button)
        
        self.cancel_button = QPushButton(ArabicText.get('cancel'))
        self.cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_button)
        
        self.save_button = QPushButton(ArabicText.get('save'))
        self.save_button.clicked.connect(self.save_camera)
        self.save_button.setDefault(True)
        button_layout.addWidget(self.save_button)
        
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
    
    def apply_dark_theme(self):
        """Apply dark theme"""
        self.setStyleSheet("""
            QDialog {
                background-color: #2c3e50;
                color: #ecf0f1;
            }
            
            QLineEdit, QComboBox {
                background-color: #34495e;
                border: 2px solid #5d6d7e;
                border-radius: 5px;
                padding: 8px;
                color: #ecf0f1;
                font-size: 14px;
            }
            
            QLineEdit:focus, QComboBox:focus {
                border-color: #3498db;
            }
            
            QGroupBox {
                font-weight: bold;
                border: 2px solid #5d6d7e;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            
            QPushButton {
                background-color: #3498db;
                border: none;
                border-radius: 5px;
                color: white;
                padding: 10px 20px;
                font-weight: bold;
            }
            
            QPushButton:hover {
                background-color: #5dade2;
            }
            
            QPushButton:pressed {
                background-color: #2980b9;
            }
            
            QLabel {
                color: #ecf0f1;
            }
        """)
    
    def load_camera_data(self):
        """Load existing camera data"""
        if self.camera_data:
            self.name_input.setText(self.camera_data['name'])
            self.url_input.setText(self.camera_data['url'])
            
            # Set camera type
            for i in range(self.type_combo.count()):
                if self.type_combo.itemData(i) == self.camera_data['type']:
                    self.type_combo.setCurrentIndex(i)
                    break
    
    def test_connection(self):
        """Test camera connection"""
        url = self.url_input.text().strip()
        camera_type = self.type_combo.currentData()
        
        if not url:
            QMessageBox.warning(self, "Warning", "Please enter camera URL")
            return
        
        self.test_button.setEnabled(False)
        self.test_button.setText("Testing...")
        QApplication.processEvents()
        
        success, message = CameraHandler.test_camera_connection(url, camera_type)
        
        self.test_button.setEnabled(True)
        self.test_button.setText("Test Connection")
        
        if success:
            QMessageBox.information(self, "Test Result", "Connection successful!")
        else:
            QMessageBox.warning(self, "Test Result", f"Connection failed:\n{message}")
    
    def save_camera(self):
        """Save camera data"""
        name = self.name_input.text().strip()
        url = self.url_input.text().strip()
        camera_type = self.type_combo.currentData()
        
        if not name:
            QMessageBox.warning(self, "Warning", "Please enter camera name")
            return
        
        if not url:
            QMessageBox.warning(self, "Warning", "Please enter camera URL")
            return
        
        self.accept()
    
    def get_camera_data(self):
        """Get camera data from form"""
        return {
            'name': self.name_input.text().strip(),
            'url': self.url_input.text().strip(),
            'type': self.type_combo.currentData()
        }
