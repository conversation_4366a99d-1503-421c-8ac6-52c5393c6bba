#!/usr/bin/env python3
"""
Test script for Camera Monitoring System
Tests basic functionality without GUI
"""

import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test all imports"""
    print("Testing imports...")
    
    try:
        # Test core modules
        from core.database import DatabaseManager
        from core.camera_handler import CameraHandler
        from core.motion_detector import MotionDetector
        from core.recorder import VideoRecorder
        from core.user_manager import UserManager
        print("✓ Core modules imported successfully")
        
        # Test utils
        from utils.config import ConfigManager
        from utils.arabic_support import ArabicText
        print("✓ Utils modules imported successfully")
        
        # Test external dependencies
        import cv2
        import numpy as np
        from PyQt5.QtWidgets import QApplication
        print("✓ External dependencies imported successfully")
        
        return True
        
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False

def test_database():
    """Test database functionality"""
    print("\nTesting database...")

    try:
        from core.database import DatabaseManager
        db = DatabaseManager("test_database.db")
        
        # Test user creation
        success = db.add_user("testuser", "testpass", "user")
        if success:
            print("✓ User creation successful")
        
        # Test authentication
        result = db.authenticate_user("admin", "admin123")
        if result:
            print("✓ User authentication successful")
        
        # Test camera operations
        camera_id = db.add_camera("Test Camera", "rtsp://test", "rtsp")
        if camera_id:
            print("✓ Camera creation successful")
        
        cameras = db.get_cameras()
        if cameras:
            print(f"✓ Camera retrieval successful ({len(cameras)} cameras)")
        
        # Cleanup
        os.remove("test_database.db")
        
        return True
        
    except Exception as e:
        print(f"✗ Database error: {e}")
        return False

def test_config():
    """Test configuration management"""
    print("\nTesting configuration...")

    try:
        from utils.config import ConfigManager
        config = ConfigManager("test_config.json")
        
        # Test setting and getting values
        config.set("test.value", "hello")
        value = config.get("test.value")
        
        if value == "hello":
            print("✓ Configuration set/get successful")
        
        # Test grid calculation
        grid = config.get_camera_grid_size(9)
        if grid == (3, 3):
            print("✓ Grid calculation successful")
        
        # Cleanup
        if os.path.exists("test_config.json"):
            os.remove("test_config.json")
        
        return True
        
    except Exception as e:
        print(f"✗ Configuration error: {e}")
        return False

def test_arabic_support():
    """Test Arabic text support"""
    print("\nTesting Arabic support...")

    try:
        from utils.arabic_support import ArabicText
        # Test text retrieval
        title = ArabicText.get('app_title')
        if title:
            print(f"✓ Arabic text retrieval successful: {title}")
        
        # Test camera types
        types = ArabicText.get_camera_types()
        if types:
            print(f"✓ Camera types retrieval successful ({len(types)} types)")
        
        return True
        
    except Exception as e:
        print(f"✗ Arabic support error: {e}")
        return False

def test_camera_handler():
    """Test camera handler (without actual camera)"""
    print("\nTesting camera handler...")

    try:
        from core.camera_handler import CameraHandler
        # Create handler
        handler = CameraHandler(1, "Test Camera", "0", "usb")
        
        # Test status
        status = handler.get_status()
        if status:
            print("✓ Camera status retrieval successful")
        
        # Test URL generation
        urls = CameraHandler.generate_camera_urls(
            "rtsp://admin:pass@192.168.1.100:554/Streaming/Channels/{}/",
            ["101", "201", "301"]
        )
        if len(urls) == 3:
            print("✓ Camera URL generation successful")
        
        return True
        
    except Exception as e:
        print(f"✗ Camera handler error: {e}")
        return False

def test_motion_detector():
    """Test motion detector"""
    print("\nTesting motion detector...")

    try:
        import numpy as np
        from core.motion_detector import MotionDetector

        detector = MotionDetector()
        
        # Create test frame
        frame = np.zeros((480, 640, 3), dtype=np.uint8)
        
        # Test motion detection
        motion_detected, processed_frame = detector.detect_motion(frame)
        
        if processed_frame is not None:
            print("✓ Motion detection processing successful")
        
        # Test status
        status = detector.get_motion_status()
        if status:
            print("✓ Motion detector status retrieval successful")
        
        return True
        
    except Exception as e:
        print(f"✗ Motion detector error: {e}")
        return False

def test_recorder():
    """Test video recorder"""
    print("\nTesting video recorder...")

    try:
        from core.recorder import VideoRecorder
        recorder = VideoRecorder("test_recordings")
        
        # Test status
        status = recorder.get_recording_status()
        if status:
            print("✓ Recorder status retrieval successful")
        
        # Test parameter setting
        recorder.set_recording_params(fps=25)
        print("✓ Recorder parameter setting successful")
        
        # Cleanup
        if os.path.exists("test_recordings"):
            os.rmdir("test_recordings")
        
        return True
        
    except Exception as e:
        print(f"✗ Video recorder error: {e}")
        return False

def main():
    """Run all tests"""
    print("=" * 50)
    print("Camera Monitoring System - Test Suite")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_database,
        test_config,
        test_arabic_support,
        test_camera_handler,
        test_motion_detector,
        test_recorder
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with exception: {e}")
            failed += 1
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed} passed, {failed} failed")
    print("=" * 50)
    
    if failed == 0:
        print("🎉 All tests passed! System is ready to run.")
        return 0
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
