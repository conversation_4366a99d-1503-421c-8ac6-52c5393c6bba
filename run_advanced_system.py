#!/usr/bin/env python3
"""
تشغيل النظام المتقدم مع جميع الميزات
Run Advanced Camera Monitoring System
"""

import sys
import os
import subprocess
import time
from pathlib import Path

def print_banner():
    """طباعة شعار النظام المتقدم"""
    print("=" * 80)
    print("🚀 نظام مراقبة الكاميرات المتقدم - الإصدار 2.0")
    print("🚀 Advanced Camera Monitoring System v2.0")
    print("=" * 80)
    print()
    print("✨ الميزات الجديدة:")
    print("🤖 ذكاء اصطناعي متقدم - التعرف على الوجوه وكشف الأجسام")
    print("🧠 تحليل السلوك الذكي - كشف السلوك المشبوه")
    print("☁️ التخزين السحابي - نسخ احتياطي تلقائي")
    print("🔔 إشعارات ذكية - تنبيهات متقدمة ومخصصة")
    print("📊 تحليلات متقدمة - تقارير وإحصائيات مفصلة")
    print("🌐 واجهة ويب - مراقبة عن بعد")
    print("📱 دعم الهاتف المحمول - تطبيق مخصص")
    print()

def check_python_version():
    """فحص إصدار Python"""
    print("🐍 فحص إصدار Python...")
    
    if sys.version_info < (3, 7):
        print("❌ Python 3.7+ مطلوب")
        print(f"الإصدار الحالي: {sys.version}")
        return False
    
    print(f"✅ Python {sys.version.split()[0]} - متوافق")
    return True

def check_basic_requirements():
    """فحص المتطلبات الأساسية"""
    print("\n📦 فحص المتطلبات الأساسية...")
    
    basic_packages = [
        'cv2',
        'numpy', 
        'PyQt5',
        'sqlite3',
        'requests'
    ]
    
    missing_packages = []
    
    for package in basic_packages:
        try:
            __import__(package)
            print(f"✅ {package}: متوفر")
        except ImportError:
            print(f"❌ {package}: مفقود")
            missing_packages.append(package)
    
    return missing_packages

def install_basic_requirements():
    """تثبيت المتطلبات الأساسية"""
    print("\n🔄 تثبيت المتطلبات الأساسية...")
    
    basic_packages = [
        'opencv-python==********',
        'numpy==1.24.3',
        'Pillow==10.0.1',
        'PyQt5==5.15.10',
        'requests==2.31.0',
        'psutil==5.9.5'
    ]
    
    try:
        for package in basic_packages:
            print(f"📦 تثبيت {package}...")
            result = subprocess.run([
                sys.executable, '-m', 'pip', 'install', package
            ], capture_output=True, text=True, timeout=120)
            
            if result.returncode == 0:
                print(f"✅ تم تثبيت {package}")
            else:
                print(f"⚠️ مشكلة في تثبيت {package}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في التثبيت: {e}")
        return False

def check_ai_requirements():
    """فحص متطلبات الذكاء الاصطناعي"""
    print("\n🤖 فحص متطلبات الذكاء الاصطناعي...")
    
    ai_packages = [
        ('face_recognition', 'التعرف على الوجوه'),
        ('sklearn', 'التعلم الآلي'),
        ('dlib', 'معالجة الصور المتقدمة')
    ]
    
    available_ai = []
    missing_ai = []
    
    for package, description in ai_packages:
        try:
            __import__(package)
            print(f"✅ {description}: متوفر")
            available_ai.append(package)
        except ImportError:
            print(f"⚠️ {description}: غير متوفر (اختياري)")
            missing_ai.append(package)
    
    return len(available_ai) > 0, missing_ai

def setup_directories():
    """إنشاء المجلدات المطلوبة"""
    print("\n📁 إنشاء المجلدات المطلوبة...")
    
    directories = [
        'recordings',
        'assets', 
        'logs',
        'ai_models',
        'cloud_backup',
        'temp',
        'exports'
    ]
    
    for directory in directories:
        try:
            Path(directory).mkdir(exist_ok=True)
            print(f"✅ {directory}/")
        except Exception as e:
            print(f"⚠️ فشل في إنشاء {directory}: {e}")

def check_database():
    """فحص قاعدة البيانات"""
    print("\n🗄️ فحص قاعدة البيانات...")
    
    try:
        # إضافة المسار الحالي
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        from core.database import DatabaseManager
        
        db = DatabaseManager()
        
        # اختبار الاتصال
        result = db.authenticate_user('admin', 'admin123')
        
        if result:
            print("✅ قاعدة البيانات تعمل بشكل صحيح")
            return True
        else:
            print("⚠️ مشكلة في المصادقة - سيتم إعادة إنشاء قاعدة البيانات")
            return True  # Database exists but needs user creation
            
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False

def initialize_ai_engines():
    """تهيئة محركات الذكاء الاصطناعي"""
    print("\n🤖 تهيئة محركات الذكاء الاصطناعي...")
    
    ai_status = {
        'face_recognition': False,
        'object_detection': False,
        'behavior_analysis': False
    }
    
    try:
        # محرك التعرف على الوجوه
        try:
            from ai_engine.face_recognition_engine import FaceRecognitionEngine
            face_engine = FaceRecognitionEngine()
            ai_status['face_recognition'] = True
            print("✅ محرك التعرف على الوجوه: جاهز")
        except Exception as e:
            print(f"⚠️ محرك التعرف على الوجوه: غير متوفر ({e})")
        
        # محرك كشف الأجسام
        try:
            from ai_engine.object_detection_engine import ObjectDetectionEngine
            object_engine = ObjectDetectionEngine()
            ai_status['object_detection'] = True
            print("✅ محرك كشف الأجسام: جاهز")
        except Exception as e:
            print(f"⚠️ محرك كشف الأجسام: غير متوفر ({e})")
        
        # محرك تحليل السلوك
        try:
            from ai_engine.behavior_analysis_engine import BehaviorAnalysisEngine
            behavior_engine = BehaviorAnalysisEngine()
            ai_status['behavior_analysis'] = True
            print("✅ محرك تحليل السلوك: جاهز")
        except Exception as e:
            print(f"⚠️ محرك تحليل السلوك: غير متوفر ({e})")
        
    except Exception as e:
        print(f"❌ خطأ في تهيئة محركات الذكاء الاصطناعي: {e}")
    
    return ai_status

def initialize_cloud_services():
    """تهيئة الخدمات السحابية"""
    print("\n☁️ تهيئة الخدمات السحابية...")
    
    try:
        from cloud_services.cloud_storage_manager import CloudStorageManager
        cloud_manager = CloudStorageManager()
        print("✅ مدير التخزين السحابي: جاهز")
        return True
    except Exception as e:
        print(f"⚠️ الخدمات السحابية: غير متوفرة ({e})")
        return False

def initialize_notification_system():
    """تهيئة نظام الإشعارات"""
    print("\n🔔 تهيئة نظام الإشعارات...")
    
    try:
        from notification_system.smart_notification_engine import SmartNotificationEngine
        notification_engine = SmartNotificationEngine()
        print("✅ محرك الإشعارات الذكي: جاهز")
        return True
    except Exception as e:
        print(f"⚠️ نظام الإشعارات: غير متوفر ({e})")
        return False

def run_system():
    """تشغيل النظام"""
    print("\n🚀 تشغيل النظام...")
    
    try:
        # تشغيل النظام الرئيسي
        result = subprocess.run([sys.executable, 'main.py'], timeout=5)
        return result.returncode == 0
    except subprocess.TimeoutExpired:
        # النظام يعمل (لم ينته خلال 5 ثوان)
        print("✅ النظام يعمل...")
        return True
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        return False

def show_system_status(ai_status, cloud_available, notifications_available):
    """عرض حالة النظام"""
    print("\n" + "=" * 80)
    print("📊 حالة النظام")
    print("=" * 80)
    
    print("🎯 الميزات الأساسية:")
    print("✅ مراقبة الكاميرات المباشرة")
    print("✅ كشف الحركة")
    print("✅ التسجيل التلقائي")
    print("✅ إدارة المستخدمين")
    print("✅ الواجهة العربية")
    
    print("\n🤖 ميزات الذكاء الاصطناعي:")
    for feature, status in ai_status.items():
        status_icon = "✅" if status else "⚠️"
        feature_names = {
            'face_recognition': 'التعرف على الوجوه',
            'object_detection': 'كشف الأجسام',
            'behavior_analysis': 'تحليل السلوك'
        }
        print(f"{status_icon} {feature_names[feature]}")
    
    print("\n☁️ الخدمات السحابية:")
    cloud_icon = "✅" if cloud_available else "⚠️"
    print(f"{cloud_icon} التخزين السحابي")
    
    print("\n🔔 نظام الإشعارات:")
    notif_icon = "✅" if notifications_available else "⚠️"
    print(f"{notif_icon} الإشعارات الذكية")
    
    print("\n💡 ملاحظات:")
    if not any(ai_status.values()):
        print("⚠️ لتفعيل ميزات الذكاء الاصطناعي، قم بتثبيت: pip install face-recognition scikit-learn")
    
    if not cloud_available:
        print("⚠️ الخدمات السحابية متوفرة ولكن تحتاج إعداد")
    
    if not notifications_available:
        print("⚠️ نظام الإشعارات متوفر ولكن تحتاج إعداد")

def main():
    """الوظيفة الرئيسية"""
    print_banner()
    
    # فحص Python
    if not check_python_version():
        input("اضغط Enter للخروج...")
        return 1
    
    # فحص المتطلبات الأساسية
    missing_basic = check_basic_requirements()
    
    if missing_basic:
        print(f"\n⚠️ مكتبات مفقودة: {', '.join(missing_basic)}")
        choice = input("هل تريد تثبيتها تلقائياً؟ (y/n): ").lower()
        
        if choice in ['y', 'yes', 'نعم']:
            if not install_basic_requirements():
                print("❌ فشل في تثبيت المتطلبات")
                input("اضغط Enter للخروج...")
                return 1
        else:
            print("❌ لا يمكن تشغيل النظام بدون المتطلبات الأساسية")
            input("اضغط Enter للخروج...")
            return 1
    
    # إعداد المجلدات
    setup_directories()
    
    # فحص قاعدة البيانات
    if not check_database():
        print("❌ مشكلة في قاعدة البيانات")
        input("اضغط Enter للخروج...")
        return 1
    
    # فحص الذكاء الاصطناعي
    ai_available, missing_ai = check_ai_requirements()
    ai_status = initialize_ai_engines()
    
    # تهيئة الخدمات المتقدمة
    cloud_available = initialize_cloud_services()
    notifications_available = initialize_notification_system()
    
    # عرض حالة النظام
    show_system_status(ai_status, cloud_available, notifications_available)
    
    print("\n" + "=" * 80)
    print("🎉 النظام جاهز للتشغيل!")
    print("=" * 80)
    
    # خيارات التشغيل
    print("\nخيارات التشغيل:")
    print("1. تشغيل النظام الكامل")
    print("2. تشغيل النظام الأساسي فقط")
    print("3. فتح واجهة إدارة الذكاء الاصطناعي")
    print("4. إعداد الخدمات السحابية")
    print("5. خروج")
    
    choice = input("\nاختر (1-5): ").strip()
    
    if choice == "1":
        print("\n🚀 تشغيل النظام الكامل...")
        run_system()
    elif choice == "2":
        print("\n🚀 تشغيل النظام الأساسي...")
        subprocess.run([sys.executable, 'main.py'])
    elif choice == "3":
        print("\n🤖 فتح واجهة إدارة الذكاء الاصطناعي...")
        subprocess.run([sys.executable, 'ui/ai_management_window.py'])
    elif choice == "4":
        print("\n☁️ إعداد الخدمات السحابية...")
        print("سيتم إضافة واجهة الإعداد في التحديث القادم")
    elif choice == "5":
        print("👋 وداعاً!")
        return 0
    else:
        print("❌ اختيار غير صحيح")
        return 1
    
    return 0

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف التشغيل")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        input("اضغط Enter للخروج...")
        sys.exit(1)
