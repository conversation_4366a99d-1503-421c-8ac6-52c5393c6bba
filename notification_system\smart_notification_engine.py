#!/usr/bin/env python3
"""
محرك الإشعارات الذكي المتقدم
Advanced Smart Notification Engine
"""

import json
import sqlite3
import smtplib
import requests
import threading
import time
from datetime import datetime, timedelta
from email.mime.text import MimeText
from email.mime.multipart import MimeMultipart
from email.mime.image import MimeImage
import os
import cv2
import numpy as np

class SmartNotificationEngine:
    def __init__(self, database_path="database.db", config_path="notification_config.json"):
        self.database_path = database_path
        self.config_path = config_path
        self.config = self.load_config()
        
        # Notification channels
        self.channels = {
            'email': EmailChannel(),
            'sms': SMSChannel(),
            'push': PushNotificationChannel(),
            'webhook': WebhookChannel(),
            'desktop': DesktopNotificationChannel(),
            'sound': SoundAlertChannel()
        }
        
        # Smart filtering
        self.smart_filters = {
            'duplicate_detection': True,
            'time_based_filtering': True,
            'importance_scoring': True,
            'user_preference_learning': True
        }
        
        # Notification queue
        self.notification_queue = []
        self.processing_thread = None
        self.is_running = False
        
        # Initialize database
        self.init_notification_database()
        
        # Start notification processor
        self.start_notification_processor()
    
    def load_config(self):
        """Load notification configuration"""
        default_config = {
            'enabled': True,
            'channels': {
                'email': {
                    'enabled': False,
                    'smtp_server': 'smtp.gmail.com',
                    'smtp_port': 587,
                    'username': '',
                    'password': '',
                    'recipients': []
                },
                'sms': {
                    'enabled': False,
                    'provider': 'twilio',
                    'api_key': '',
                    'api_secret': '',
                    'phone_numbers': []
                },
                'push': {
                    'enabled': False,
                    'firebase_key': '',
                    'device_tokens': []
                },
                'webhook': {
                    'enabled': False,
                    'urls': []
                },
                'desktop': {
                    'enabled': True,
                    'duration': 5000
                },
                'sound': {
                    'enabled': True,
                    'sound_file': 'assets/alert.wav'
                }
            },
            'filters': {
                'min_importance': 1,
                'max_notifications_per_hour': 10,
                'quiet_hours': {
                    'enabled': False,
                    'start': '22:00',
                    'end': '08:00'
                },
                'duplicate_threshold_minutes': 5
            },
            'templates': {
                'motion_detected': {
                    'title': '🚨 كشف حركة',
                    'message': 'تم كشف حركة في الكاميرا {camera_name} في {timestamp}',
                    'importance': 2
                },
                'face_recognized': {
                    'title': '👤 تم التعرف على شخص',
                    'message': 'تم التعرف على {person_name} في الكاميرا {camera_name}',
                    'importance': 1
                },
                'unknown_person': {
                    'title': '❓ شخص غير معروف',
                    'message': 'تم كشف شخص غير معروف في الكاميرا {camera_name}',
                    'importance': 3
                },
                'behavior_alert': {
                    'title': '⚠️ تنبيه سلوك',
                    'message': 'تم كشف سلوك مشبوه: {behavior_type} في الكاميرا {camera_name}',
                    'importance': 3
                },
                'system_error': {
                    'title': '🔧 خطأ في النظام',
                    'message': 'حدث خطأ في النظام: {error_message}',
                    'importance': 2
                }
            }
        }
        
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    # Merge with defaults
                    return self.merge_configs(default_config, config)
            else:
                return default_config
        except Exception as e:
            print(f"Error loading notification config: {e}")
            return default_config
    
    def merge_configs(self, default, user):
        """Merge user config with defaults"""
        for key, value in default.items():
            if key not in user:
                user[key] = value
            elif isinstance(value, dict) and isinstance(user[key], dict):
                user[key] = self.merge_configs(value, user[key])
        return user
    
    def save_config(self):
        """Save notification configuration"""
        try:
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"Error saving notification config: {e}")
    
    def init_notification_database(self):
        """Initialize notification database tables"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()
            
            # Notifications table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS notifications (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    notification_type TEXT NOT NULL,
                    title TEXT NOT NULL,
                    message TEXT NOT NULL,
                    importance INTEGER DEFAULT 1,
                    camera_id INTEGER,
                    image_path TEXT,
                    metadata TEXT,
                    channels_sent TEXT,
                    status TEXT DEFAULT 'pending',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    sent_at TIMESTAMP,
                    read_at TIMESTAMP
                )
            ''')
            
            # Notification rules table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS notification_rules (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    rule_name TEXT NOT NULL,
                    event_type TEXT NOT NULL,
                    conditions TEXT,
                    channels TEXT,
                    enabled BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Notification statistics table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS notification_stats (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    date DATE NOT NULL,
                    channel TEXT NOT NULL,
                    sent_count INTEGER DEFAULT 0,
                    failed_count INTEGER DEFAULT 0,
                    UNIQUE(date, channel)
                )
            ''')
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"Error initializing notification database: {e}")
    
    def send_notification(self, notification_type, data, importance=1, image_path=None):
        """Send smart notification"""
        if not self.config['enabled']:
            return False, "Notifications disabled"
        
        try:
            # Get template
            template = self.config['templates'].get(notification_type)
            if not template:
                return False, f"Unknown notification type: {notification_type}"
            
            # Format message
            title = template['title'].format(**data)
            message = template['message'].format(**data)
            importance = template.get('importance', importance)
            
            # Create notification object
            notification = {
                'type': notification_type,
                'title': title,
                'message': message,
                'importance': importance,
                'data': data,
                'image_path': image_path,
                'timestamp': datetime.now(),
                'id': None  # Will be set when saved to database
            }
            
            # Apply smart filters
            if not self.should_send_notification(notification):
                return False, "Filtered by smart filters"
            
            # Save to database
            notification_id = self.save_notification(notification)
            notification['id'] = notification_id
            
            # Add to processing queue
            self.notification_queue.append(notification)
            
            return True, f"Notification queued (ID: {notification_id})"
            
        except Exception as e:
            return False, f"Error sending notification: {str(e)}"
    
    def should_send_notification(self, notification):
        """Apply smart filters to determine if notification should be sent"""
        
        # Check minimum importance
        if notification['importance'] < self.config['filters']['min_importance']:
            return False
        
        # Check quiet hours
        quiet_hours = self.config['filters']['quiet_hours']
        if quiet_hours['enabled']:
            current_time = datetime.now().time()
            start_time = datetime.strptime(quiet_hours['start'], '%H:%M').time()
            end_time = datetime.strptime(quiet_hours['end'], '%H:%M').time()
            
            if start_time <= current_time <= end_time:
                # Only send high importance notifications during quiet hours
                if notification['importance'] < 3:
                    return False
        
        # Check rate limiting
        if self.is_rate_limited():
            return False
        
        # Check for duplicates
        if self.is_duplicate_notification(notification):
            return False
        
        return True
    
    def is_rate_limited(self):
        """Check if we're hitting rate limits"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()
            
            # Count notifications in the last hour
            cursor.execute('''
                SELECT COUNT(*) FROM notifications 
                WHERE created_at > datetime('now', '-1 hour')
                AND status = 'sent'
            ''')
            
            count = cursor.fetchone()[0]
            conn.close()
            
            max_per_hour = self.config['filters']['max_notifications_per_hour']
            return count >= max_per_hour
            
        except Exception as e:
            print(f"Error checking rate limit: {e}")
            return False
    
    def is_duplicate_notification(self, notification):
        """Check for duplicate notifications"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()
            
            threshold_minutes = self.config['filters']['duplicate_threshold_minutes']
            
            cursor.execute('''
                SELECT COUNT(*) FROM notifications 
                WHERE notification_type = ? 
                AND title = ?
                AND created_at > datetime('now', '-{} minutes')
            '''.format(threshold_minutes), (notification['type'], notification['title']))
            
            count = cursor.fetchone()[0]
            conn.close()
            
            return count > 0
            
        except Exception as e:
            print(f"Error checking duplicates: {e}")
            return False
    
    def save_notification(self, notification):
        """Save notification to database"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO notifications 
                (notification_type, title, message, importance, camera_id, image_path, metadata)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                notification['type'],
                notification['title'],
                notification['message'],
                notification['importance'],
                notification['data'].get('camera_id'),
                notification['image_path'],
                json.dumps(notification['data'])
            ))
            
            notification_id = cursor.lastrowid
            conn.commit()
            conn.close()
            
            return notification_id
            
        except Exception as e:
            print(f"Error saving notification: {e}")
            return None
    
    def start_notification_processor(self):
        """Start background notification processor"""
        if self.processing_thread and self.processing_thread.is_alive():
            return
        
        self.is_running = True
        self.processing_thread = threading.Thread(target=self.notification_processor, daemon=True)
        self.processing_thread.start()
    
    def stop_notification_processor(self):
        """Stop background notification processor"""
        self.is_running = False
        if self.processing_thread:
            self.processing_thread.join(timeout=5)
    
    def notification_processor(self):
        """Background processor for sending notifications"""
        while self.is_running:
            try:
                if not self.notification_queue:
                    time.sleep(1)
                    continue
                
                # Get next notification
                notification = self.notification_queue.pop(0)
                
                # Send through enabled channels
                channels_sent = []
                channels_failed = []
                
                for channel_name, channel in self.channels.items():
                    if self.config['channels'][channel_name]['enabled']:
                        try:
                            success = channel.send(notification, self.config['channels'][channel_name])
                            if success:
                                channels_sent.append(channel_name)
                            else:
                                channels_failed.append(channel_name)
                        except Exception as e:
                            print(f"Error sending via {channel_name}: {e}")
                            channels_failed.append(channel_name)
                
                # Update notification status
                self.update_notification_status(
                    notification['id'], 
                    'sent' if channels_sent else 'failed',
                    channels_sent
                )
                
                # Update statistics
                self.update_notification_stats(channels_sent, channels_failed)
                
                # Small delay between notifications
                time.sleep(0.5)
                
            except Exception as e:
                print(f"Error in notification processor: {e}")
                time.sleep(5)
    
    def update_notification_status(self, notification_id, status, channels_sent):
        """Update notification status in database"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                UPDATE notifications 
                SET status = ?, channels_sent = ?, sent_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (status, ','.join(channels_sent), notification_id))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"Error updating notification status: {e}")
    
    def update_notification_stats(self, channels_sent, channels_failed):
        """Update notification statistics"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()
            
            today = datetime.now().date()
            
            # Update sent statistics
            for channel in channels_sent:
                cursor.execute('''
                    INSERT OR REPLACE INTO notification_stats (date, channel, sent_count, failed_count)
                    VALUES (?, ?, 
                        COALESCE((SELECT sent_count FROM notification_stats WHERE date = ? AND channel = ?), 0) + 1,
                        COALESCE((SELECT failed_count FROM notification_stats WHERE date = ? AND channel = ?), 0))
                ''', (today, channel, today, channel, today, channel))
            
            # Update failed statistics
            for channel in channels_failed:
                cursor.execute('''
                    INSERT OR REPLACE INTO notification_stats (date, channel, sent_count, failed_count)
                    VALUES (?, ?, 
                        COALESCE((SELECT sent_count FROM notification_stats WHERE date = ? AND channel = ?), 0),
                        COALESCE((SELECT failed_count FROM notification_stats WHERE date = ? AND channel = ?), 0) + 1)
                ''', (today, channel, today, channel, today, channel))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"Error updating notification stats: {e}")
    
    def get_notification_history(self, limit=100, status=None):
        """Get notification history"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()
            
            query = '''
                SELECT id, notification_type, title, message, importance, 
                       camera_id, status, channels_sent, created_at, sent_at
                FROM notifications 
                WHERE 1=1
            '''
            
            params = []
            if status:
                query += " AND status = ?"
                params.append(status)
            
            query += " ORDER BY created_at DESC LIMIT ?"
            params.append(limit)
            
            cursor.execute(query, params)
            notifications = cursor.fetchall()
            conn.close()
            
            return [
                {
                    'id': n[0],
                    'type': n[1],
                    'title': n[2],
                    'message': n[3],
                    'importance': n[4],
                    'camera_id': n[5],
                    'status': n[6],
                    'channels_sent': n[7].split(',') if n[7] else [],
                    'created_at': n[8],
                    'sent_at': n[9]
                }
                for n in notifications
            ]
            
        except Exception as e:
            print(f"Error getting notification history: {e}")
            return []
    
    def get_notification_statistics(self, days=7):
        """Get notification statistics"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()
            
            # Total notifications
            cursor.execute('''
                SELECT COUNT(*) FROM notifications 
                WHERE created_at > datetime('now', '-{} days')
            '''.format(days))
            total_notifications = cursor.fetchone()[0]
            
            # Notifications by status
            cursor.execute('''
                SELECT status, COUNT(*) FROM notifications 
                WHERE created_at > datetime('now', '-{} days')
                GROUP BY status
            '''.format(days))
            status_stats = dict(cursor.fetchall())
            
            # Notifications by type
            cursor.execute('''
                SELECT notification_type, COUNT(*) FROM notifications 
                WHERE created_at > datetime('now', '-{} days')
                GROUP BY notification_type
                ORDER BY COUNT(*) DESC
            '''.format(days))
            type_stats = dict(cursor.fetchall())
            
            # Channel statistics
            cursor.execute('''
                SELECT channel, SUM(sent_count), SUM(failed_count) 
                FROM notification_stats 
                WHERE date > date('now', '-{} days')
                GROUP BY channel
            '''.format(days))
            channel_stats = {row[0]: {'sent': row[1], 'failed': row[2]} for row in cursor.fetchall()}
            
            conn.close()
            
            return {
                'total_notifications': total_notifications,
                'status_breakdown': status_stats,
                'type_breakdown': type_stats,
                'channel_performance': channel_stats,
                'success_rate': (status_stats.get('sent', 0) / total_notifications * 100) if total_notifications > 0 else 0
            }
            
        except Exception as e:
            print(f"Error getting notification statistics: {e}")
            return {}


# Notification channel implementations
class NotificationChannel:
    """Base class for notification channels"""
    
    def send(self, notification, config):
        """Send notification through this channel"""
        raise NotImplementedError


class EmailChannel(NotificationChannel):
    """Email notification channel"""
    
    def send(self, notification, config):
        try:
            # Create message
            msg = MimeMultipart()
            msg['From'] = config['username']
            msg['Subject'] = notification['title']
            
            # Add text content
            text_content = f"{notification['message']}\n\nوقت الحدث: {notification['timestamp']}"
            msg.attach(MimeText(text_content, 'plain', 'utf-8'))
            
            # Add image if available
            if notification['image_path'] and os.path.exists(notification['image_path']):
                with open(notification['image_path'], 'rb') as f:
                    img_data = f.read()
                    image = MimeImage(img_data)
                    image.add_header('Content-Disposition', 'attachment', filename='snapshot.jpg')
                    msg.attach(image)
            
            # Send to all recipients
            server = smtplib.SMTP(config['smtp_server'], config['smtp_port'])
            server.starttls()
            server.login(config['username'], config['password'])
            
            for recipient in config['recipients']:
                msg['To'] = recipient
                server.send_message(msg)
                del msg['To']
            
            server.quit()
            return True
            
        except Exception as e:
            print(f"Email send error: {e}")
            return False


class SMSChannel(NotificationChannel):
    """SMS notification channel"""
    
    def send(self, notification, config):
        try:
            # Implementation depends on SMS provider (Twilio, etc.)
            message = f"{notification['title']}\n{notification['message']}"
            
            # Placeholder for SMS sending logic
            print(f"SMS sent: {message}")
            return True
            
        except Exception as e:
            print(f"SMS send error: {e}")
            return False


class PushNotificationChannel(NotificationChannel):
    """Push notification channel"""
    
    def send(self, notification, config):
        try:
            # Implementation for Firebase/FCM push notifications
            payload = {
                'title': notification['title'],
                'body': notification['message'],
                'data': notification['data']
            }
            
            # Placeholder for push notification logic
            print(f"Push notification sent: {payload}")
            return True
            
        except Exception as e:
            print(f"Push notification error: {e}")
            return False


class WebhookChannel(NotificationChannel):
    """Webhook notification channel"""
    
    def send(self, notification, config):
        try:
            payload = {
                'type': notification['type'],
                'title': notification['title'],
                'message': notification['message'],
                'importance': notification['importance'],
                'timestamp': notification['timestamp'].isoformat(),
                'data': notification['data']
            }
            
            for url in config['urls']:
                response = requests.post(url, json=payload, timeout=10)
                if response.status_code != 200:
                    print(f"Webhook failed for {url}: {response.status_code}")
            
            return True
            
        except Exception as e:
            print(f"Webhook error: {e}")
            return False


class DesktopNotificationChannel(NotificationChannel):
    """Desktop notification channel"""
    
    def send(self, notification, config):
        try:
            # Use system notification (Windows/Linux/Mac)
            if os.name == 'nt':  # Windows
                import win10toast
                toaster = win10toast.ToastNotifier()
                toaster.show_toast(
                    notification['title'],
                    notification['message'],
                    duration=config.get('duration', 5000) // 1000
                )
            else:  # Linux/Mac
                os.system(f'notify-send "{notification["title"]}" "{notification["message"]}"')
            
            return True
            
        except Exception as e:
            print(f"Desktop notification error: {e}")
            return False


class SoundAlertChannel(NotificationChannel):
    """Sound alert channel"""
    
    def send(self, notification, config):
        try:
            sound_file = config.get('sound_file', 'assets/alert.wav')
            
            if os.path.exists(sound_file):
                # Play sound file
                if os.name == 'nt':  # Windows
                    import winsound
                    winsound.PlaySound(sound_file, winsound.SND_FILENAME)
                else:  # Linux/Mac
                    os.system(f'aplay {sound_file}')
            else:
                # System beep as fallback
                print('\a')  # ASCII bell character
            
            return True
            
        except Exception as e:
            print(f"Sound alert error: {e}")
            return False
