#!/usr/bin/env python3
"""
إعداد سريع لكاميرات XVR
Quick XVR Camera Setup
"""

import sys
import os
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.database import DatabaseManager

def quick_xvr_setup():
    """إعداد سريع لكاميرات XVR"""
    
    # بيانات الجهاز من الصورة
    device_info = {
        'ip': '*************',  # يجب تحديد IP الفعلي
        'username': 'admin',
        'password': 'Mnbv@1978',
        'port': '554',
        'device_name': 'XVR'
    }
    
    print("🎥 إعداد سريع لكاميرات XVR")
    print("=" * 40)
    
    # طلب عنوان IP من المستخدم
    print(f"المعلومات الحالية:")
    print(f"اسم المستخدم: {device_info['username']}")
    print(f"كلمة المرور: {device_info['password']}")
    print(f"المنفذ: {device_info['port']}")
    print()
    
    # إدخال عنوان IP
    ip = input("أدخل عنوان IP للجهاز (مثال: *************): ").strip()
    if ip:
        device_info['ip'] = ip
    
    # إدخال عدد الكاميرات
    try:
        camera_count = int(input("أدخل عدد الكاميرات (افتراضي: 8): ") or "8")
    except:
        camera_count = 8
    
    print(f"\n🔗 توليد روابط لـ {camera_count} كاميرا...")
    
    # توليد روابط Dahua XVR
    urls = []
    for i in range(1, camera_count + 1):
        url = f"rtsp://{device_info['username']}:{device_info['password']}@{device_info['ip']}:{device_info['port']}/cam/realmonitor?channel={i}&subtype=0"
        urls.append((f"XVR_Camera_{i}", url))
        print(f"📹 كاميرا {i}: {url}")
    
    print(f"\n✅ تم توليد {len(urls)} رابط")
    
    # سؤال عن إضافة إلى قاعدة البيانات
    add_to_db = input("\nهل تريد إضافة الكاميرات إلى قاعدة البيانات؟ (y/n): ").lower()
    
    if add_to_db in ['y', 'yes', 'نعم']:
        try:
            db = DatabaseManager()
            added_count = 0
            
            for camera_name, url in urls:
                camera_id = db.add_camera(camera_name, url, "rtsp")
                if camera_id:
                    added_count += 1
                    print(f"✅ تم إضافة {camera_name} (ID: {camera_id})")
            
            print(f"\n🎉 تم إضافة {added_count} كاميرا إلى قاعدة البيانات!")
            
        except Exception as e:
            print(f"❌ خطأ في إضافة الكاميرات: {str(e)}")
    
    # حفظ الروابط في ملف
    save_to_file = input("\nهل تريد حفظ الروابط في ملف؟ (y/n): ").lower()
    
    if save_to_file in ['y', 'yes', 'نعم']:
        filename = f"xvr_cameras_{device_info['ip'].replace('.', '_')}.txt"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(f"XVR Camera URLs - {device_info['ip']}\n")
                f.write("=" * 40 + "\n")
                f.write(f"Device IP: {device_info['ip']}\n")
                f.write(f"Username: {device_info['username']}\n")
                f.write(f"Password: {device_info['password']}\n")
                f.write(f"Port: {device_info['port']}\n")
                f.write(f"Camera Count: {camera_count}\n")
                f.write("=" * 40 + "\n\n")
                
                for i, (camera_name, url) in enumerate(urls, 1):
                    f.write(f"Camera {i}: {camera_name}\n")
                    f.write(f"URL: {url}\n\n")
            
            print(f"💾 تم حفظ الروابط في: {filename}")
            
        except Exception as e:
            print(f"❌ خطأ في حفظ الملف: {str(e)}")
    
    print("\n🎯 الإعداد مكتمل!")
    print("يمكنك الآن استخدام هذه الروابط في نظام مراقبة الكاميرات")


if __name__ == "__main__":
    quick_xvr_setup()
    input("\nاضغط Enter للخروج...")
