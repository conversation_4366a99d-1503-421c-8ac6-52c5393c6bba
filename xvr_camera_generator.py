#!/usr/bin/env python3
"""
مولد روابط كاميرات XVR/DVR
XVR/DVR Camera URL Generator
"""

import sys
import os
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.database import DatabaseManager

class XVRCameraGenerator(QDialog):
    def __init__(self):
        super().__init__()
        self.db_manager = DatabaseManager()
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة مولد روابط XVR"""
        self.setWindowTitle("🎥 مولد روابط كاميرات XVR/DVR")
        self.setFixedSize(700, 600)
        
        layout = QVBoxLayout()
        
        # العنوان
        title = QLabel("🎥 مولد روابط كاميرات XVR/DVR")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 20px; font-weight: bold; margin: 15px; color: #2c3e50;")
        layout.addWidget(title)
        
        # معلومات الجهاز
        device_group = QGroupBox("معلومات جهاز XVR/DVR")
        device_layout = QFormLayout()
        
        # عنوان IP
        self.ip_input = QLineEdit()
        self.ip_input.setPlaceholderText("*************")
        device_layout.addRow("عنوان IP:", self.ip_input)
        
        # المنفذ
        self.port_input = QLineEdit()
        self.port_input.setText("554")
        device_layout.addRow("المنفذ:", self.port_input)
        
        # اسم المستخدم
        self.username_input = QLineEdit()
        self.username_input.setText("admin")
        device_layout.addRow("اسم المستخدم:", self.username_input)
        
        # كلمة المرور
        self.password_input = QLineEdit()
        self.password_input.setText("Mnbv@1978")
        self.password_input.setEchoMode(QLineEdit.Password)
        device_layout.addRow("كلمة المرور:", self.password_input)
        
        # إظهار كلمة المرور
        show_password_cb = QCheckBox("إظهار كلمة المرور")
        show_password_cb.toggled.connect(self.toggle_password_visibility)
        device_layout.addRow("", show_password_cb)
        
        device_group.setLayout(device_layout)
        layout.addWidget(device_group)
        
        # إعدادات الكاميرات
        cameras_group = QGroupBox("إعدادات الكاميرات")
        cameras_layout = QVBoxLayout()
        
        # عدد الكاميرات
        camera_count_layout = QHBoxLayout()
        camera_count_layout.addWidget(QLabel("عدد الكاميرات:"))
        
        self.camera_count_spin = QSpinBox()
        self.camera_count_spin.setMinimum(1)
        self.camera_count_spin.setMaximum(32)
        self.camera_count_spin.setValue(8)
        camera_count_layout.addWidget(self.camera_count_spin)
        
        camera_count_layout.addStretch()
        cameras_layout.addLayout(camera_count_layout)
        
        # نوع الجهاز
        device_type_layout = QHBoxLayout()
        device_type_layout.addWidget(QLabel("نوع الجهاز:"))
        
        self.device_type_combo = QComboBox()
        self.device_type_combo.addItems([
            "Hikvision DVR/NVR",
            "Dahua XVR/DVR", 
            "Generic RTSP",
            "ONVIF Camera"
        ])
        self.device_type_combo.currentTextChanged.connect(self.on_device_type_changed)
        device_type_layout.addWidget(self.device_type_combo)
        
        device_type_layout.addStretch()
        cameras_layout.addLayout(device_type_layout)
        
        cameras_group.setLayout(cameras_layout)
        layout.addWidget(cameras_group)
        
        # أزرار التحكم
        button_layout = QHBoxLayout()
        
        generate_btn = QPushButton("🔗 توليد الروابط")
        generate_btn.clicked.connect(self.generate_urls)
        button_layout.addWidget(generate_btn)
        
        test_btn = QPushButton("🧪 اختبار الاتصال")
        test_btn.clicked.connect(self.test_connection)
        button_layout.addWidget(test_btn)
        
        add_to_db_btn = QPushButton("💾 إضافة إلى قاعدة البيانات")
        add_to_db_btn.clicked.connect(self.add_to_database)
        button_layout.addWidget(add_to_db_btn)
        
        layout.addLayout(button_layout)
        
        # منطقة عرض الروابط
        self.urls_text = QTextEdit()
        self.urls_text.setPlaceholderText("الروابط المولدة ستظهر هنا...")
        self.urls_text.setMaximumHeight(200)
        layout.addWidget(self.urls_text)
        
        # أزرار إضافية
        extra_button_layout = QHBoxLayout()
        
        copy_btn = QPushButton("📋 نسخ جميع الروابط")
        copy_btn.clicked.connect(self.copy_urls)
        extra_button_layout.addWidget(copy_btn)
        
        clear_btn = QPushButton("🧹 مسح")
        clear_btn.clicked.connect(self.urls_text.clear)
        extra_button_layout.addWidget(clear_btn)
        
        extra_button_layout.addStretch()
        
        close_btn = QPushButton("❌ إغلاق")
        close_btn.clicked.connect(self.accept)
        extra_button_layout.addWidget(close_btn)
        
        layout.addLayout(extra_button_layout)
        
        self.setLayout(layout)
        
        # تحميل القيم الافتراضية
        self.load_default_values()
        
        # تطبيق النمط
        self.apply_style()
    
    def load_default_values(self):
        """تحميل القيم الافتراضية"""
        # تحديد نوع الجهاز بناءً على كلمة المرور
        password = self.password_input.text()
        if "Mnbv" in password:
            self.device_type_combo.setCurrentText("Dahua XVR/DVR")
        
        # تحديث تنسيق الروابط
        self.on_device_type_changed()
    
    def toggle_password_visibility(self, checked):
        """تبديل إظهار كلمة المرور"""
        if checked:
            self.password_input.setEchoMode(QLineEdit.Normal)
        else:
            self.password_input.setEchoMode(QLineEdit.Password)
    
    def on_device_type_changed(self):
        """عند تغيير نوع الجهاز"""
        device_type = self.device_type_combo.currentText()
        
        # تحديث المنفذ الافتراضي
        if "ONVIF" in device_type:
            self.port_input.setText("80")
        else:
            self.port_input.setText("554")
    
    def generate_urls(self):
        """توليد روابط الكاميرات"""
        ip = self.ip_input.text().strip()
        port = self.port_input.text().strip()
        username = self.username_input.text().strip()
        password = self.password_input.text().strip()
        camera_count = self.camera_count_spin.value()
        device_type = self.device_type_combo.currentText()
        
        if not all([ip, port, username, password]):
            QMessageBox.warning(self, "خطأ", "يرجى ملء جميع الحقول المطلوبة")
            return
        
        self.urls_text.clear()
        self.urls_text.append(f"🎥 روابط كاميرات {device_type}")
        self.urls_text.append(f"📡 الجهاز: {ip}:{port}")
        self.urls_text.append(f"👤 المستخدم: {username}")
        self.urls_text.append("=" * 50)
        
        urls = []
        
        if "Hikvision" in device_type:
            # تنسيق Hikvision
            for i in range(1, camera_count + 1):
                channel = f"{i:02d}1"  # 101, 201, 301, etc.
                url = f"rtsp://{username}:{password}@{ip}:{port}/Streaming/Channels/{channel}/"
                urls.append(url)
                self.urls_text.append(f"📹 كاميرا {i}: {url}")
        
        elif "Dahua" in device_type:
            # تنسيق Dahua XVR
            for i in range(1, camera_count + 1):
                url = f"rtsp://{username}:{password}@{ip}:{port}/cam/realmonitor?channel={i}&subtype=0"
                urls.append(url)
                self.urls_text.append(f"📹 كاميرا {i}: {url}")
        
        elif "ONVIF" in device_type:
            # تنسيق ONVIF
            for i in range(1, camera_count + 1):
                url = f"rtsp://{username}:{password}@{ip}:{port}/onvif/profile{i}"
                urls.append(url)
                self.urls_text.append(f"📹 كاميرا {i}: {url}")
        
        else:
            # تنسيق عام
            for i in range(1, camera_count + 1):
                channel = f"{i}01"
                url = f"rtsp://{username}:{password}@{ip}:{port}/stream{i}"
                urls.append(url)
                self.urls_text.append(f"📹 كاميرا {i}: {url}")
        
        self.generated_urls = urls
        self.urls_text.append("=" * 50)
        self.urls_text.append(f"✅ تم توليد {len(urls)} رابط كاميرا")
    
    def test_connection(self):
        """اختبار الاتصال بالجهاز"""
        ip = self.ip_input.text().strip()
        
        if not ip:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال عنوان IP")
            return
        
        self.urls_text.append("\n🔍 اختبار الاتصال...")
        QApplication.processEvents()
        
        try:
            import subprocess
            
            # اختبار ping
            result = subprocess.run(['ping', '-n', '4', ip], 
                                  capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                self.urls_text.append(f"✅ الاتصال ناجح مع {ip}")
                
                # اختبار المنفذ
                port = int(self.port_input.text())
                import socket
                
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(5)
                port_result = sock.connect_ex((ip, port))
                sock.close()
                
                if port_result == 0:
                    self.urls_text.append(f"✅ المنفذ {port} مفتوح")
                else:
                    self.urls_text.append(f"❌ المنفذ {port} مغلق أو محجوب")
            else:
                self.urls_text.append(f"❌ فشل الاتصال مع {ip}")
                
        except Exception as e:
            self.urls_text.append(f"❌ خطأ في الاختبار: {str(e)}")
    
    def add_to_database(self):
        """إضافة الكاميرات إلى قاعدة البيانات"""
        if not hasattr(self, 'generated_urls') or not self.generated_urls:
            QMessageBox.warning(self, "خطأ", "يرجى توليد الروابط أولاً")
            return
        
        device_name = f"XVR_{self.ip_input.text()}"
        added_count = 0
        
        try:
            for i, url in enumerate(self.generated_urls, 1):
                camera_name = f"{device_name}_Camera_{i}"
                camera_id = self.db_manager.add_camera(camera_name, url, "rtsp")
                if camera_id:
                    added_count += 1
            
            self.urls_text.append(f"\n💾 تم إضافة {added_count} كاميرا إلى قاعدة البيانات")
            QMessageBox.information(self, "نجح", f"تم إضافة {added_count} كاميرا بنجاح!")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إضافة الكاميرات:\n{str(e)}")
    
    def copy_urls(self):
        """نسخ جميع الروابط"""
        if hasattr(self, 'generated_urls') and self.generated_urls:
            urls_text = "\n".join(self.generated_urls)
            clipboard = QApplication.clipboard()
            clipboard.setText(urls_text)
            
            QMessageBox.information(self, "تم النسخ", f"تم نسخ {len(self.generated_urls)} رابط")
        else:
            QMessageBox.warning(self, "خطأ", "لا توجد روابط للنسخ")
    
    def apply_style(self):
        """تطبيق نمط الواجهة"""
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #2c3e50;
            }
            QLineEdit, QSpinBox, QComboBox {
                padding: 8px;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                font-size: 12px;
                background-color: white;
            }
            QLineEdit:focus, QSpinBox:focus, QComboBox:focus {
                border-color: #3498db;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 15px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
            QTextEdit {
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                background-color: white;
                font-family: 'Courier New', monospace;
                font-size: 11px;
            }
            QCheckBox {
                font-size: 12px;
            }
        """)


def main():
    """تشغيل مولد روابط XVR"""
    app = QApplication(sys.argv)
    
    # تعيين خط يدعم العربية
    font = QFont("Arial", 10)
    app.setFont(font)
    
    # إنشاء النافذة
    window = XVRCameraGenerator()
    window.exec_()
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
