#!/usr/bin/env python3
"""
مشغل آمن ومبسط لنظام مراقبة الكاميرات
Safe and Simple Launcher for Camera Monitoring System
"""

import sys
import os
import traceback
import time
import subprocess

def print_safe_banner():
    """طباعة شعار المشغل الآمن"""
    print("=" * 60)
    print("🛡️ المشغل الآمن لنظام مراقبة الكاميرات")
    print("🛡️ Safe Launcher for Camera System")
    print("=" * 60)
    print()

def check_requirements():
    """فحص المتطلبات الأساسية"""
    print("🔍 فحص المتطلبات الأساسية...")
    
    # فحص Python
    if sys.version_info < (3, 6):
        print(f"❌ إصدار Python قديم: {sys.version}")
        print("💡 يتطلب Python 3.6 أو أحدث")
        return False
    
    print(f"✅ Python {sys.version.split()[0]}")
    
    # فحص المكتبات الأساسية
    required_modules = {
        'cv2': 'opencv-python',
        'numpy': 'numpy', 
        'PyQt5': 'PyQt5',
        'sqlite3': None,  # مدمج
        'PIL': 'Pillow'
    }
    
    missing = []
    
    for module, package in required_modules.items():
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError:
            print(f"❌ {module} مفقود")
            if package:
                missing.append(package)
    
    if missing:
        print(f"\n📦 مكتبات مفقودة: {', '.join(missing)}")
        print("💡 لتثبيتها:")
        print(f"pip install {' '.join(missing)}")
        
        install = input("\nهل تريد تثبيتها الآن؟ (y/n): ").lower()
        if install in ['y', 'yes', 'نعم']:
            try:
                print("📦 جاري التثبيت...")
                subprocess.run([sys.executable, '-m', 'pip', 'install'] + missing, check=True)
                print("✅ تم التثبيت بنجاح")
                return True
            except Exception as e:
                print(f"❌ فشل التثبيت: {e}")
                return False
        else:
            return False
    
    return True

def check_files():
    """فحص الملفات الأساسية"""
    print("\n📁 فحص الملفات الأساسية...")
    
    # الملفات الأساسية المطلوبة
    critical_files = ['main.py']
    important_files = ['database.db', 'config.json']
    
    missing_critical = []
    missing_important = []
    
    for file in critical_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} مفقود")
            missing_critical.append(file)
    
    for file in important_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"⚠️ {file} مفقود")
            missing_important.append(file)
    
    if missing_critical:
        print(f"\n❌ ملفات أساسية مفقودة: {', '.join(missing_critical)}")
        return False
    
    if missing_important:
        print(f"\n⚠️ ملفات مهمة مفقودة: {', '.join(missing_important)}")
        print("💡 سيتم إنشاؤها تلقائ<|im_start|>")
        create_missing_files(missing_important)
    
    return True

def create_missing_files(missing_files):
    """إنشاء الملفات المفقودة"""
    print("🔧 إنشاء الملفات المفقودة...")
    
    # إنشاء قاعدة البيانات
    if 'database.db' in missing_files:
        try:
            import sqlite3
            
            conn = sqlite3.connect('database.db')
            cursor = conn.cursor()
            
            # جدول المستخدمين
            cursor.execute('''
                CREATE TABLE users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    password TEXT NOT NULL,
                    role TEXT DEFAULT 'user',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # جدول الكاميرات
            cursor.execute('''
                CREATE TABLE cameras (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    rtsp_url TEXT NOT NULL,
                    username TEXT,
                    password TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # جدول التسجيلات
            cursor.execute('''
                CREATE TABLE recordings (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    camera_id INTEGER,
                    filename TEXT NOT NULL,
                    start_time TIMESTAMP,
                    end_time TIMESTAMP,
                    file_size INTEGER,
                    FOREIGN KEY (camera_id) REFERENCES cameras (id)
                )
            ''')
            
            # مستخدم افتراضي
            cursor.execute('''
                INSERT INTO users (username, password, role)
                VALUES ('admin', 'admin123', 'admin')
            ''')
            
            # إضافة كاميرات XVR
            ip = "**************"
            username = "admin"
            password = "Mnbv@1978"
            
            for channel in range(1, 9):
                camera_name = f"XVR قناة {channel}"
                rtsp_url = f"rtsp://{username}:{password}@{ip}:554/cam/realmonitor?channel={channel}&subtype=0"
                
                cursor.execute('''
                    INSERT INTO cameras (name, rtsp_url, username, password, is_active)
                    VALUES (?, ?, ?, ?, ?)
                ''', (camera_name, rtsp_url, username, password, 1))
            
            conn.commit()
            conn.close()
            
            print("✅ تم إنشاء قاعدة البيانات مع 8 كاميرات XVR")
            
        except Exception as e:
            print(f"❌ فشل في إنشاء قاعدة البيانات: {e}")
    
    # إنشاء ملف الإعدادات
    if 'config.json' in missing_files:
        try:
            import json
            
            config = {
                "app_name": "نظام مراقبة الكاميرات",
                "version": "1.0",
                "language": "ar",
                "theme": "dark",
                "auto_save": True,
                "recording_path": "recordings",
                "max_recording_size_mb": 1000,
                "camera_settings": {
                    "default_fps": 15,
                    "default_quality": "medium",
                    "motion_detection": True
                }
            }
            
            with open('config.json', 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            
            print("✅ تم إنشاء ملف الإعدادات")
            
        except Exception as e:
            print(f"❌ فشل في إنشاء ملف الإعدادات: {e}")
    
    # إنشاء المجلدات
    directories = ['recordings', 'assets', 'logs', 'ui', 'core', 'utils']
    for directory in directories:
        try:
            os.makedirs(directory, exist_ok=True)
            print(f"✅ مجلد {directory}")
        except Exception as e:
            print(f"❌ فشل في إنشاء مجلد {directory}: {e}")

def test_system():
    """اختبار سريع للنظام"""
    print("\n🧪 اختبار سريع للنظام...")
    
    try:
        # اختبار قاعدة البيانات
        import sqlite3
        conn = sqlite3.connect('database.db')
        cursor = conn.cursor()
        
        cursor.execute("SELECT COUNT(*) FROM users")
        user_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM cameras")
        camera_count = cursor.fetchone()[0]
        
        conn.close()
        
        print(f"✅ قاعدة البيانات: {user_count} مستخدم، {camera_count} كاميرا")
        
        # اختبار الواجهة الرسومية
        from PyQt5.QtWidgets import QApplication
        app = QApplication([])
        print("✅ واجهة PyQt5 تعمل")
        app.quit()
        
        # اختبار OpenCV
        import cv2
        print("✅ OpenCV متوفر")
        
        return True
        
    except Exception as e:
        print(f"❌ فشل الاختبار: {e}")
        return False

def launch_system():
    """تشغيل النظام الرئيسي"""
    print("\n🚀 تشغيل النظام الرئيسي...")
    
    try:
        # إضافة المسار الحالي
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        # استيراد وتشغيل النظام
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        app = QApplication(sys.argv)
        app.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        
        # محاولة تشغيل نافذة تسجيل الدخول
        try:
            from ui.login_window import LoginWindow
            window = LoginWindow()
        except ImportError:
            # إذا لم تكن متوفرة، استخدم نافذة بسيطة
            from PyQt5.QtWidgets import QWidget, QVBoxLayout, QLabel, QPushButton
            
            window = QWidget()
            window.setWindowTitle("نظام مراقبة الكاميرات")
            window.resize(400, 300)
            
            layout = QVBoxLayout()
            
            label = QLabel("مرحباً بك في نظام مراقبة الكاميرات")
            label.setAlignment(Qt.AlignCenter)
            layout.addWidget(label)
            
            info_label = QLabel("النظام يعمل بشكل أساسي\nيمكنك الآن تطوير الواجهات")
            info_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(info_label)
            
            close_btn = QPushButton("إغلاق")
            close_btn.clicked.connect(window.close)
            layout.addWidget(close_btn)
            
            window.setLayout(layout)
        
        window.show()
        
        print("✅ تم تشغيل النظام بنجاح")
        print("💡 إذا لم تظهر النافذة، تحقق من شريط المهام")
        
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ فشل في تشغيل النظام: {e}")
        print("\n📋 تفاصيل الخطأ:")
        traceback.print_exc()
        
        print("\n💡 حلول مقترحة:")
        print("1. تأكد من تثبيت جميع المكتبات")
        print("2. تأكد من وجود ملف main.py")
        print("3. شغل fix_system_crash.py للتشخيص")
        print("4. أعد تشغيل الكمبيوتر")

def show_menu():
    """عرض قائمة الخيارات"""
    print("\n🎯 خيارات التشغيل:")
    print("1. تشغيل النظام مباشرة")
    print("2. فحص وإصلاح المشاكل")
    print("3. إعادة إنشاء قاعدة البيانات")
    print("4. اختبار الكاميرات")
    print("5. تثبيت المكتبات المطلوبة")
    print("6. خروج")
    
    choice = input("\nاختيارك (1-6): ").strip()
    
    if choice == "1":
        launch_system()
    
    elif choice == "2":
        print("\n🔧 تشغيل أداة الإصلاح...")
        try:
            subprocess.run([sys.executable, 'fix_system_crash.py'])
        except:
            print("❌ لم يتم العثور على أداة الإصلاح")
    
    elif choice == "3":
        print("\n🗄️ إعادة إنشاء قاعدة البيانات...")
        if os.path.exists('database.db'):
            backup_name = f'database_backup_{int(time.time())}.db'
            os.rename('database.db', backup_name)
            print(f"📋 تم حفظ نسخة احتياطية: {backup_name}")
        
        create_missing_files(['database.db'])
    
    elif choice == "4":
        print("\n📹 اختبار الكاميرات...")
        try:
            import cv2
            
            # اختبار كاميرا XVR
            rtsp_url = "rtsp://admin:Mnbv@1978@**************:554/cam/realmonitor?channel=1&subtype=0"
            cap = cv2.VideoCapture(rtsp_url)
            
            if cap.isOpened():
                ret, frame = cap.read()
                if ret:
                    print("✅ كاميرا XVR تعمل بشكل صحيح")
                else:
                    print("⚠️ كاميرا XVR متصلة لكن لا تعطي صورة")
                cap.release()
            else:
                print("❌ فشل الاتصال بكاميرا XVR")
                print("💡 تحقق من:")
                print("   - اتصال الشبكة")
                print("   - عنوان IP: **************")
                print("   - اسم المستخدم: admin")
                print("   - كلمة المرور: Mnbv@1978")
        
        except Exception as e:
            print(f"❌ خطأ في اختبار الكاميرات: {e}")
    
    elif choice == "5":
        print("\n📦 تثبيت المكتبات...")
        packages = ['opencv-python', 'numpy', 'PyQt5', 'Pillow', 'requests']
        try:
            subprocess.run([sys.executable, '-m', 'pip', 'install'] + packages)
            print("✅ تم تثبيت المكتبات")
        except Exception as e:
            print(f"❌ فشل التثبيت: {e}")
    
    elif choice == "6":
        print("👋 شكراً لاستخدام النظام!")
        return
    
    else:
        print("❌ خيار غير صحيح")
    
    input("\nاضغط Enter للمتابعة...")
    show_menu()

def main():
    """الوظيفة الرئيسية"""
    print_safe_banner()
    
    print("🔍 فحص النظام...")
    
    # فحص المتطلبات
    if not check_requirements():
        print("❌ فشل فحص المتطلبات")
        input("اضغط Enter للخروج...")
        return
    
    # فحص الملفات
    if not check_files():
        print("❌ فشل فحص الملفات")
        input("اضغط Enter للخروج...")
        return
    
    # اختبار النظام
    if not test_system():
        print("⚠️ فشل اختبار النظام - لكن يمكن المحاولة")
    
    print("\n✅ النظام جاهز للتشغيل!")
    
    # عرض القائمة
    show_menu()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف المشغل")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        traceback.print_exc()
        input("اضغط Enter للخروج...")
