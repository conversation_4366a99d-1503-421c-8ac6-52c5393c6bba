#!/usr/bin/env python3
"""
حل مشكلة إغلاق النظام التلقائي
Fix System Auto-Close Issue
"""

import sys
import os
import traceback
import sqlite3
import subprocess
import time
from datetime import datetime

def print_diagnostic_banner():
    """طباعة شعار التشخيص"""
    print("=" * 70)
    print("🔧 أداة تشخيص وإصلاح مشاكل النظام")
    print("🔧 System Diagnostic and Repair Tool")
    print("=" * 70)
    print()
    print("🎯 المشاكل التي سيتم فحصها:")
    print("❌ أخطاء Python والمكتبات المفقودة")
    print("❌ مشاكل قاعدة البيانات")
    print("❌ ملفات مفقودة أو تالفة")
    print("❌ مشاكل الكاميرات والاتصال")
    print("❌ أخطاء الواجهة الرسومية")
    print("❌ مشاكل الذاكرة والأداء")
    print()

def check_python_environment():
    """فحص بيئة Python"""
    print("🐍 فحص بيئة Python...")
    
    issues = []
    
    # فحص إصدار Python
    if sys.version_info < (3, 6):
        issues.append(f"إصدار Python قديم: {sys.version}")
    else:
        print(f"✅ Python {sys.version.split()[0]}")
    
    # فحص المكتبات الأساسية
    required_modules = [
        ('cv2', 'opencv-python'),
        ('numpy', 'numpy'),
        ('PyQt5', 'PyQt5'),
        ('sqlite3', 'مدمج'),
        ('PIL', 'Pillow'),
        ('requests', 'requests')
    ]
    
    missing_modules = []
    
    for module, package in required_modules:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError:
            print(f"❌ {module} مفقود")
            missing_modules.append(package)
            issues.append(f"مكتبة مفقودة: {module}")
    
    return issues, missing_modules

def check_database():
    """فحص قاعدة البيانات"""
    print("\n🗄️ فحص قاعدة البيانات...")
    
    issues = []
    
    try:
        if not os.path.exists('database.db'):
            issues.append("ملف قاعدة البيانات غير موجود")
            print("❌ database.db غير موجود")
            return issues
        
        # اختبار الاتصال
        conn = sqlite3.connect('database.db')
        cursor = conn.cursor()
        
        # فحص الجداول الأساسية
        required_tables = ['users', 'cameras', 'recordings']
        
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        existing_tables = [row[0] for row in cursor.fetchall()]
        
        for table in required_tables:
            if table in existing_tables:
                print(f"✅ جدول {table}")
            else:
                print(f"❌ جدول {table} مفقود")
                issues.append(f"جدول {table} مفقود")
        
        # فحص المستخدمين
        try:
            cursor.execute("SELECT COUNT(*) FROM users")
            user_count = cursor.fetchone()[0]
            if user_count > 0:
                print(f"✅ {user_count} مستخدم في النظام")
            else:
                print("⚠️ لا يوجد مستخدمين")
                issues.append("لا يوجد مستخدمين في النظام")
        except:
            issues.append("مشكلة في جدول المستخدمين")
        
        # فحص الكاميرات
        try:
            cursor.execute("SELECT COUNT(*) FROM cameras")
            camera_count = cursor.fetchone()[0]
            if camera_count > 0:
                print(f"✅ {camera_count} كاميرا مضافة")
            else:
                print("⚠️ لا توجد كاميرات")
                issues.append("لا توجد كاميرات مضافة")
        except:
            issues.append("مشكلة في جدول الكاميرات")
        
        conn.close()
        
    except Exception as e:
        issues.append(f"خطأ في قاعدة البيانات: {str(e)}")
        print(f"❌ خطأ في قاعدة البيانات: {e}")
    
    return issues

def check_files():
    """فحص الملفات المطلوبة"""
    print("\n📁 فحص الملفات المطلوبة...")
    
    issues = []
    
    required_files = [
        'main.py',
        'config.json',
        'ui/main_window.py',
        'ui/login_window.py',
        'core/database.py',
        'core/user_manager.py',
        'utils/config.py'
    ]
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} مفقود")
            issues.append(f"ملف مفقود: {file_path}")
    
    # فحص المجلدات
    required_dirs = ['ui', 'core', 'utils', 'recordings', 'assets']
    
    for dir_path in required_dirs:
        if os.path.exists(dir_path):
            print(f"✅ {dir_path}/")
        else:
            print(f"❌ {dir_path}/ مفقود")
            issues.append(f"مجلد مفقود: {dir_path}")
    
    return issues

def check_config():
    """فحص ملف الإعدادات"""
    print("\n⚙️ فحص ملف الإعدادات...")
    
    issues = []
    
    try:
        if os.path.exists('config.json'):
            import json
            with open('config.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
            print("✅ ملف config.json صحيح")
        else:
            print("⚠️ ملف config.json غير موجود")
            issues.append("ملف الإعدادات غير موجود")
    except Exception as e:
        print(f"❌ خطأ في ملف الإعدادات: {e}")
        issues.append(f"ملف الإعدادات تالف: {str(e)}")
    
    return issues

def test_gui():
    """اختبار الواجهة الرسومية"""
    print("\n🖥️ اختبار الواجهة الرسومية...")
    
    issues = []
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import QTimer
        
        # إنشاء تطبيق مؤقت
        app = QApplication([])
        
        # اختبار إنشاء نافذة بسيطة
        from PyQt5.QtWidgets import QWidget, QLabel
        
        test_window = QWidget()
        test_window.setWindowTitle("اختبار")
        test_window.resize(200, 100)
        
        label = QLabel("اختبار الواجهة", test_window)
        
        # إغلاق تلقائي بعد ثانية واحدة
        timer = QTimer()
        timer.timeout.connect(app.quit)
        timer.start(1000)
        
        test_window.show()
        app.exec_()
        
        print("✅ الواجهة الرسومية تعمل")
        
    except Exception as e:
        print(f"❌ مشكلة في الواجهة الرسومية: {e}")
        issues.append(f"مشكلة في PyQt5: {str(e)}")
    
    return issues

def test_camera_connection():
    """اختبار اتصال الكاميرات"""
    print("\n📹 اختبار اتصال الكاميرات...")
    
    issues = []
    
    try:
        import cv2
        
        # اختبار كاميرا ويب محلية
        cap = cv2.VideoCapture(0)
        if cap.isOpened():
            ret, frame = cap.read()
            if ret:
                print("✅ كاميرا ويب محلية تعمل")
            else:
                print("⚠️ كاميرا ويب محلية متصلة لكن لا تعطي صورة")
            cap.release()
        else:
            print("⚠️ لا توجد كاميرا ويب محلية")
        
        # اختبار كاميرا XVR
        xvr_url = "rtsp://admin:Mnbv@1978@192.168.18.108:554/cam/realmonitor?channel=1&subtype=0"
        cap = cv2.VideoCapture(xvr_url)
        
        if cap.isOpened():
            ret, frame = cap.read()
            if ret:
                print("✅ كاميرا XVR تعمل")
            else:
                print("⚠️ كاميرا XVR متصلة لكن لا تعطي صورة")
                issues.append("كاميرا XVR لا تعطي صورة")
            cap.release()
        else:
            print("❌ فشل الاتصال بكاميرا XVR")
            issues.append("فشل الاتصال بكاميرا XVR")
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الكاميرات: {e}")
        issues.append(f"خطأ في OpenCV: {str(e)}")
    
    return issues

def run_safe_test():
    """تشغيل اختبار آمن للنظام"""
    print("\n🧪 تشغيل اختبار آمن للنظام...")
    
    try:
        # إضافة المسار الحالي
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        # اختبار استيراد الوحدات الأساسية
        print("📦 اختبار استيراد الوحدات...")
        
        try:
            from core.database import DatabaseManager
            print("✅ DatabaseManager")
        except Exception as e:
            print(f"❌ DatabaseManager: {e}")
            return False
        
        try:
            from core.user_manager import UserManager
            print("✅ UserManager")
        except Exception as e:
            print(f"❌ UserManager: {e}")
            return False
        
        try:
            from utils.config import ConfigManager
            print("✅ ConfigManager")
        except Exception as e:
            print(f"❌ ConfigManager: {e}")
            return False
        
        # اختبار قاعدة البيانات
        print("\n🗄️ اختبار قاعدة البيانات...")
        try:
            db = DatabaseManager()
            print("✅ اتصال قاعدة البيانات")
        except Exception as e:
            print(f"❌ قاعدة البيانات: {e}")
            return False
        
        print("✅ الاختبار الآمن نجح")
        return True
        
    except Exception as e:
        print(f"❌ فشل الاختبار الآمن: {e}")
        traceback.print_exc()
        return False

def create_safe_launcher():
    """إنشاء مشغل آمن للنظام"""
    print("\n🛡️ إنشاء مشغل آمن...")
    
    safe_launcher_content = '''#!/usr/bin/env python3
"""
مشغل آمن لنظام مراقبة الكاميرات
Safe Launcher for Camera Monitoring System
"""

import sys
import os
import traceback
import time

def safe_main():
    """تشغيل آمن للنظام الرئيسي"""
    try:
        print("🚀 بدء تشغيل النظام الآمن...")
        
        # إضافة المسار الحالي
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        # فحص المتطلبات الأساسية
        print("🔍 فحص المتطلبات...")
        
        required_modules = ['cv2', 'numpy', 'PyQt5', 'sqlite3']
        missing = []
        
        for module in required_modules:
            try:
                __import__(module)
                print(f"✅ {module}")
            except ImportError:
                print(f"❌ {module} مفقود")
                missing.append(module)
        
        if missing:
            print(f"❌ مكتبات مفقودة: {', '.join(missing)}")
            print("💡 قم بتثبيتها: pip install opencv-python numpy PyQt5")
            input("اضغط Enter للخروج...")
            return
        
        # فحص الملفات الأساسية
        required_files = ['main.py', 'database.db']
        missing_files = []
        
        for file in required_files:
            if not os.path.exists(file):
                missing_files.append(file)
        
        if missing_files:
            print(f"❌ ملفات مفقودة: {', '.join(missing_files)}")
            
            if 'database.db' in missing_files:
                print("🔧 إنشاء قاعدة بيانات جديدة...")
                create_database()
            
            if 'main.py' in missing_files:
                print("❌ ملف main.py مفقود - لا يمكن المتابعة")
                input("اضغط Enter للخروج...")
                return
        
        # تشغيل النظام الرئيسي
        print("🚀 تشغيل النظام الرئيسي...")
        
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        app.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        
        # استيراد النافذة الرئيسية
        from ui.login_window import LoginWindow
        
        # إنشاء نافذة تسجيل الدخول
        login_window = LoginWindow()
        login_window.show()
        
        # تشغيل التطبيق
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ خطأ في التشغيل: {e}")
        print("📋 تفاصيل الخطأ:")
        traceback.print_exc()
        
        print("\\n💡 حلول مقترحة:")
        print("1. تأكد من تثبيت جميع المكتبات المطلوبة")
        print("2. تأكد من وجود جميع الملفات")
        print("3. شغل fix_system_crash.py لتشخيص المشاكل")
        print("4. أعد تشغيل النظام")
        
        input("\\naضغط Enter للخروج...")

def create_database():
    """إنشاء قاعدة بيانات جديدة"""
    try:
        import sqlite3
        
        conn = sqlite3.connect('database.db')
        cursor = conn.cursor()
        
        # جدول المستخدمين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password TEXT NOT NULL,
                role TEXT DEFAULT 'user',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول الكاميرات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS cameras (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                rtsp_url TEXT NOT NULL,
                username TEXT,
                password TEXT,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول التسجيلات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS recordings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                camera_id INTEGER,
                filename TEXT NOT NULL,
                start_time TIMESTAMP,
                end_time TIMESTAMP,
                file_size INTEGER,
                FOREIGN KEY (camera_id) REFERENCES cameras (id)
            )
        ''')
        
        # إضافة مستخدم افتراضي
        cursor.execute('''
            INSERT OR IGNORE INTO users (username, password, role)
            VALUES ('admin', 'admin123', 'admin')
        ''')
        
        conn.commit()
        conn.close()
        
        print("✅ تم إنشاء قاعدة البيانات")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")

if __name__ == "__main__":
    safe_main()
'''
    
    try:
        with open('safe_launcher.py', 'w', encoding='utf-8') as f:
            f.write(safe_launcher_content)
        print("✅ تم إنشاء المشغل الآمن: safe_launcher.py")
        return True
    except Exception as e:
        print(f"❌ فشل في إنشاء المشغل الآمن: {e}")
        return False

def fix_common_issues():
    """إصلاح المشاكل الشائعة"""
    print("\n🔧 إصلاح المشاكل الشائعة...")
    
    fixes_applied = 0
    
    # 1. إنشاء قاعدة بيانات إذا كانت مفقودة
    if not os.path.exists('database.db'):
        print("🔧 إنشاء قاعدة بيانات جديدة...")
        try:
            conn = sqlite3.connect('database.db')
            cursor = conn.cursor()
            
            # الجداول الأساسية
            cursor.execute('''
                CREATE TABLE users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    password TEXT NOT NULL,
                    role TEXT DEFAULT 'user'
                )
            ''')
            
            cursor.execute('''
                CREATE TABLE cameras (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    rtsp_url TEXT NOT NULL,
                    username TEXT,
                    password TEXT,
                    is_active BOOLEAN DEFAULT 1
                )
            ''')
            
            cursor.execute('''
                CREATE TABLE recordings (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    camera_id INTEGER,
                    filename TEXT NOT NULL,
                    start_time TIMESTAMP,
                    end_time TIMESTAMP
                )
            ''')
            
            # مستخدم افتراضي
            cursor.execute('''
                INSERT INTO users (username, password, role)
                VALUES ('admin', 'admin123', 'admin')
            ''')
            
            conn.commit()
            conn.close()
            
            print("✅ تم إنشاء قاعدة البيانات")
            fixes_applied += 1
            
        except Exception as e:
            print(f"❌ فشل في إنشاء قاعدة البيانات: {e}")
    
    # 2. إنشاء ملف الإعدادات
    if not os.path.exists('config.json'):
        print("🔧 إنشاء ملف الإعدادات...")
        try:
            import json
            
            default_config = {
                "app_name": "نظام مراقبة الكاميرات",
                "version": "1.0",
                "language": "ar",
                "theme": "dark",
                "auto_save": True,
                "recording_path": "recordings",
                "max_recording_size_mb": 1000
            }
            
            with open('config.json', 'w', encoding='utf-8') as f:
                json.dump(default_config, f, indent=2, ensure_ascii=False)
            
            print("✅ تم إنشاء ملف الإعدادات")
            fixes_applied += 1
            
        except Exception as e:
            print(f"❌ فشل في إنشاء ملف الإعدادات: {e}")
    
    # 3. إنشاء المجلدات المطلوبة
    required_dirs = ['recordings', 'assets', 'logs']
    for directory in required_dirs:
        if not os.path.exists(directory):
            try:
                os.makedirs(directory)
                print(f"✅ تم إنشاء مجلد {directory}")
                fixes_applied += 1
            except Exception as e:
                print(f"❌ فشل في إنشاء مجلد {directory}: {e}")
    
    return fixes_applied

def main():
    """الوظيفة الرئيسية"""
    print_diagnostic_banner()
    
    all_issues = []
    
    # فحص شامل
    print("🔍 بدء الفحص الشامل...")
    
    # 1. فحص Python
    python_issues, missing_modules = check_python_environment()
    all_issues.extend(python_issues)
    
    # 2. فحص قاعدة البيانات
    db_issues = check_database()
    all_issues.extend(db_issues)
    
    # 3. فحص الملفات
    file_issues = check_files()
    all_issues.extend(file_issues)
    
    # 4. فحص الإعدادات
    config_issues = check_config()
    all_issues.extend(config_issues)
    
    # 5. اختبار الواجهة الرسومية
    gui_issues = test_gui()
    all_issues.extend(gui_issues)
    
    # 6. اختبار الكاميرات
    camera_issues = test_camera_connection()
    all_issues.extend(camera_issues)
    
    # 7. اختبار آمن للنظام
    safe_test_passed = run_safe_test()
    
    # النتائج
    print("\n" + "=" * 70)
    print("📊 نتائج التشخيص:")
    print("=" * 70)
    
    if all_issues:
        print(f"❌ تم العثور على {len(all_issues)} مشكلة:")
        for i, issue in enumerate(all_issues, 1):
            print(f"   {i}. {issue}")
    else:
        print("✅ لم يتم العثور على مشاكل واضحة")
    
    if not safe_test_passed:
        print("❌ فشل الاختبار الآمن للنظام")
    
    # الحلول
    print("\n🔧 الحلول المقترحة:")
    
    if missing_modules:
        print(f"📦 تثبيت المكتبات المفقودة:")
        modules_to_install = [m for m in missing_modules if m != 'مدمج']
        if modules_to_install:
            print(f"   pip install {' '.join(modules_to_install)}")
    
    # تطبيق الإصلاحات
    print("\n🔧 تطبيق الإصلاحات التلقائية...")
    fixes_applied = fix_common_issues()
    
    # إنشاء المشغل الآمن
    create_safe_launcher()
    
    print(f"\n📊 تم تطبيق {fixes_applied} إصلاح")
    
    # خيارات التشغيل
    print("\n🎯 خيارات التشغيل:")
    print("1. تشغيل المشغل الآمن (safe_launcher.py)")
    print("2. تثبيت المكتبات المفقودة")
    print("3. إعادة إنشاء قاعدة البيانات")
    print("4. اختبار الكاميرات")
    print("5. خروج")
    
    choice = input("\nاختيارك (1-5): ").strip()
    
    if choice == "1":
        print("\n🚀 تشغيل المشغل الآمن...")
        try:
            subprocess.run([sys.executable, 'safe_launcher.py'])
        except Exception as e:
            print(f"❌ فشل التشغيل: {e}")
    
    elif choice == "2":
        if missing_modules:
            modules_to_install = [m for m in missing_modules if m != 'مدمج']
            if modules_to_install:
                print(f"\n📦 تثبيت {' '.join(modules_to_install)}...")
                try:
                    subprocess.run([sys.executable, '-m', 'pip', 'install'] + modules_to_install)
                    print("✅ تم التثبيت")
                except Exception as e:
                    print(f"❌ فشل التثبيت: {e}")
        else:
            print("✅ جميع المكتبات متوفرة")
    
    elif choice == "3":
        print("\n🗄️ إعادة إنشاء قاعدة البيانات...")
        if os.path.exists('database.db'):
            os.rename('database.db', f'database_backup_{int(time.time())}.db')
        fix_common_issues()
    
    elif choice == "4":
        print("\n📹 اختبار الكاميرات...")
        test_camera_connection()
    
    print("\n💡 نصائح إضافية:")
    print("• استخدم safe_launcher.py للتشغيل الآمن")
    print("• تأكد من اتصال الشبكة للكاميرات")
    print("• أعد تشغيل الكمبيوتر إذا استمرت المشاكل")
    print("• تأكد من صلاحيات الكتابة في المجلد")

if __name__ == "__main__":
    try:
        main()
        input("\nاضغط Enter للخروج...")
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف التشخيص")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        traceback.print_exc()
        input("اضغط Enter للخروج...")
