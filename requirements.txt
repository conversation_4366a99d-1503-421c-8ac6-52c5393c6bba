# Camera Monitoring System v2.0 - Advanced Edition Requirements

# Core GUI Framework
PyQt5==5.15.10

# Computer Vision and Image Processing
opencv-python==********
numpy==1.24.3
Pillow==10.0.1

# AI and Machine Learning
face-recognition==1.3.0
# dlib==19.24.2  # Uncomment if face-recognition installation fails
# tensorflow==2.13.0  # Uncomment for advanced AI features
# torch==2.0.1  # Uncomment for PyTorch models
# torchvision==0.15.2
scikit-learn==1.3.0
# mediapipe==0.10.3  # Uncomment for advanced pose detection

# Cloud Storage and APIs
requests==2.31.0
# boto3==1.28.25  # Uncomment for AWS S3
# google-cloud-storage==2.10.0  # Uncomment for Google Cloud
# dropbox==11.36.2  # Uncomment for Dropbox API

# Notifications
# twilio==8.5.0  # Uncomment for SMS notifications
# win10toast==0.9  # Uncomment for Windows notifications
# plyer==2.1.0  # Uncomment for cross-platform notifications

# Database and Storage
sqlite3  # Built-in with Python
# sqlalchemy==2.0.20  # Uncomment for advanced database features
# redis==4.6.0  # Uncomment for caching

# System Monitoring and Utilities
psutil==5.9.5
configparser==6.0.0
pyinstaller==6.1.0
pyttsx3==2.90

# Data Processing (Optional)
# pandas==2.0.3  # Uncomment for data analysis
# matplotlib==3.7.2  # Uncomment for charts
# plotly==5.15.0  # Uncomment for interactive charts

# Security (Optional)
# cryptography==41.0.4  # Uncomment for encryption
# bcrypt==4.0.1  # Uncomment for password hashing

# Development and Testing (Optional)
# pytest==7.4.0  # Uncomment for testing
# black==23.7.0  # Uncomment for code formatting

# Note: Some packages are commented out to reduce installation time
# Uncomment packages as needed for specific features
