import cv2
import os
import threading
import time
from datetime import datetime, timedelta

class VideoRecorder:
    def __init__(self, output_dir="recordings"):
        self.output_dir = output_dir
        self.is_recording = False
        self.video_writer = None
        self.recording_thread = None
        self.frame_queue = []
        self.max_queue_size = 100
        self.fps = 30
        self.codec = cv2.VideoWriter_fourcc(*'mp4v')
        self.current_filename = None
        self.recording_start_time = None
        self.max_duration = 3600  # 1 hour in seconds
        self.file_size_limit = 1024 * 1024 * 1024  # 1GB
        
        # Create output directory if it doesn't exist
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
    
    def start_recording(self, camera_id, camera_name, frame_size, fps=30, duration_minutes=60):
        """Start video recording"""
        if self.is_recording:
            return False, "Already recording"
        
        try:
            # Generate filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            self.current_filename = f"camera_{camera_id}_{camera_name}_{timestamp}.mp4"
            output_path = os.path.join(self.output_dir, self.current_filename)
            
            # Initialize video writer
            self.video_writer = cv2.VideoWriter(
                output_path, self.codec, fps, frame_size
            )
            
            if not self.video_writer.isOpened():
                return False, "Failed to initialize video writer"
            
            self.is_recording = True
            self.fps = fps
            self.recording_start_time = datetime.now()
            self.max_duration = duration_minutes * 60
            self.frame_queue = []
            
            # Start recording thread
            self.recording_thread = threading.Thread(target=self._recording_loop)
            self.recording_thread.daemon = True
            self.recording_thread.start()
            
            return True, f"Recording started: {self.current_filename}"
            
        except Exception as e:
            return False, f"Error starting recording: {str(e)}"
    
    def stop_recording(self):
        """Stop video recording"""
        if not self.is_recording:
            return False, "Not recording"
        
        try:
            self.is_recording = False
            
            # Wait for recording thread to finish
            if self.recording_thread and self.recording_thread.is_alive():
                self.recording_thread.join(timeout=5)
            
            # Release video writer
            if self.video_writer:
                self.video_writer.release()
                self.video_writer = None
            
            recording_info = {
                'filename': self.current_filename,
                'start_time': self.recording_start_time,
                'end_time': datetime.now(),
                'file_path': os.path.join(self.output_dir, self.current_filename)
            }
            
            self.current_filename = None
            self.recording_start_time = None
            
            return True, recording_info
            
        except Exception as e:
            return False, f"Error stopping recording: {str(e)}"
    
    def add_frame(self, frame):
        """Add frame to recording queue"""
        if not self.is_recording or frame is None:
            return
        
        # Limit queue size to prevent memory issues
        if len(self.frame_queue) < self.max_queue_size:
            self.frame_queue.append(frame.copy())
    
    def _recording_loop(self):
        """Main recording loop running in separate thread"""
        frame_interval = 1.0 / self.fps
        last_frame_time = time.time()
        
        while self.is_recording:
            current_time = time.time()
            
            # Check duration limit
            if (self.recording_start_time and 
                (datetime.now() - self.recording_start_time).seconds > self.max_duration):
                break
            
            # Check file size limit
            if (self.current_filename and 
                os.path.exists(os.path.join(self.output_dir, self.current_filename))):
                file_size = os.path.getsize(os.path.join(self.output_dir, self.current_filename))
                if file_size > self.file_size_limit:
                    break
            
            # Process frames from queue
            if self.frame_queue and current_time - last_frame_time >= frame_interval:
                frame = self.frame_queue.pop(0)
                
                if self.video_writer and self.video_writer.isOpened():
                    self.video_writer.write(frame)
                    last_frame_time = current_time
            
            time.sleep(0.01)  # Small delay to prevent high CPU usage
        
        # Stop recording if loop exits
        if self.is_recording:
            self.is_recording = False
    
    def get_recording_status(self):
        """Get current recording status"""
        if not self.is_recording:
            return {
                'is_recording': False,
                'filename': None,
                'duration': 0,
                'file_size': 0
            }
        
        duration = 0
        file_size = 0
        
        if self.recording_start_time:
            duration = (datetime.now() - self.recording_start_time).seconds
        
        if (self.current_filename and 
            os.path.exists(os.path.join(self.output_dir, self.current_filename))):
            file_size = os.path.getsize(os.path.join(self.output_dir, self.current_filename))
        
        return {
            'is_recording': True,
            'filename': self.current_filename,
            'duration': duration,
            'file_size': file_size,
            'start_time': self.recording_start_time
        }
    
    def set_recording_params(self, fps=None, codec=None, max_duration=None, file_size_limit=None):
        """Update recording parameters"""
        if fps:
            self.fps = fps
        if codec:
            self.codec = codec
        if max_duration:
            self.max_duration = max_duration
        if file_size_limit:
            self.file_size_limit = file_size_limit
    
    def cleanup_old_recordings(self, days_to_keep=30):
        """Clean up old recording files"""
        if not os.path.exists(self.output_dir):
            return
        
        cutoff_date = datetime.now() - timedelta(days=days_to_keep)
        deleted_count = 0
        
        for filename in os.listdir(self.output_dir):
            file_path = os.path.join(self.output_dir, filename)
            
            if os.path.isfile(file_path):
                file_time = datetime.fromtimestamp(os.path.getctime(file_path))
                
                if file_time < cutoff_date:
                    try:
                        os.remove(file_path)
                        deleted_count += 1
                    except Exception as e:
                        print(f"Error deleting file {filename}: {e}")
        
        return deleted_count
