@echo off
title Camera Monitoring System
echo ================================================
echo Camera Monitoring System
echo ================================================
echo.
echo Starting the application...
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.7 or higher
    pause
    exit /b 1
)

REM Install requirements if needed
echo Installing/checking requirements...
pip install -r requirements.txt

REM Run the application
echo.
echo Starting Camera Monitoring System...
python main.py

REM Keep window open if there's an error
if errorlevel 1 (
    echo.
    echo Application exited with an error.
    pause
)
