# دليل استكشاف الأخطاء وإصلاحها
# Troubleshooting Guide

## 🚨 مشكلة: النظام يفتح ويغلق فوراً

### ✅ **الحل الشامل - تم إنشاؤه:**

#### **1. استخدم المشغل الآمن:**
```bash
python safe_start.py
```
أو انقر نقرتين على:
```
start_camera_system.bat
```

#### **2. إذا استمرت المشكلة:**
```bash
python quick_crash_fix.py
```

#### **3. لمشاكل قاعدة البيانات:**
```bash
python database_fix.py
```

---

## 🔍 **تشخيص المشاكل الشائعة:**

### **المشكلة 1: مكتبات مفقودة**
**الأعراض:**
- رسالة "ModuleNotFoundError"
- النظام يغلق فوراً

**الحل:**
```bash
pip install -r requirements.txt
```

### **المشكلة 2: قاعدة البيانات مقفلة**
**الأعراض:**
- رسالة "database is locked"
- فشل في تسجيل الدخول

**الحل:**
```bash
python database_fix.py
```

### **المشكلة 3: مشاكل PyQt5**
**الأعراض:**
- النظام لا يظهر واجهة
- رسائل خطأ Qt

**الحل:**
```bash
pip uninstall PyQt5
pip install PyQt5==5.15.10
```

### **المشكلة 4: مشاكل OpenCV**
**الأعراض:**
- خطأ في استيراد cv2
- مشاكل الكاميرات

**الحل:**
```bash
pip uninstall opencv-python
pip install opencv-python==********
```

---

## 🛠️ **أدوات الإصلاح المتاحة:**

### **1. المشغل الآمن** (`safe_start.py`)
- فحص شامل قبل التشغيل
- إصلاح تلقائي للمشاكل
- رسائل خطأ واضحة

### **2. إصلاح سريع** (`quick_crash_fix.py`)
- تشخيص شامل للنظام
- إصلاح المكتبات
- تنظيف الملفات المؤقتة

### **3. إصلاح قاعدة البيانات** (`database_fix.py`)
- حل مشاكل القفل
- إعادة إنشاء قاعدة البيانات
- إنهاء العمليات المتداخلة

### **4. اختبار الكاميرات** (`camera_link_tester.py`)
- اختبار روابط الكاميرات
- معاينة مباشرة
- تشخيص مشاكل الاتصال

### **5. مولد XVR** (`xvr_camera_generator.py`)
- توليد روابط DVR/NVR
- اختبار الاتصال
- إضافة تلقائية للنظام

---

## 📋 **خطوات الإصلاح المرتبة:**

### **الخطوة 1: التشخيص الأولي**
```bash
python quick_crash_fix.py
```

### **الخطوة 2: إصلاح قاعدة البيانات**
```bash
python database_fix.py
```

### **الخطوة 3: التشغيل الآمن**
```bash
python safe_start.py
```

### **الخطوة 4: إذا فشل كل شيء**
```bash
# إعادة تثبيت كاملة
pip uninstall opencv-python PyQt5 numpy
pip install -r requirements.txt

# حذف قاعدة البيانات
del database.db

# تشغيل الإصلاح الشامل
python quick_crash_fix.py
```

---

## 🎯 **حلول للمشاكل المحددة:**

### **خطأ: "Python is not recognized"**
**الحل:**
1. تثبيت Python من python.org
2. تأكد من تحديد "Add to PATH"
3. إعادة تشغيل Command Prompt

### **خطأ: "Access Denied"**
**الحل:**
1. تشغيل Command Prompt كمدير
2. أو تشغيل البرنامج كمدير

### **خطأ: "Port already in use"**
**الحل:**
```bash
# إنهاء عمليات Python
taskkill /F /IM python.exe
```

### **خطأ: "Camera connection failed"**
**الحل:**
1. تحقق من رابط الكاميرا
2. اختبر بـ VLC Media Player
3. تحقق من إعدادات الشبكة

---

## 🔧 **إعدادات النظام المطلوبة:**

### **متطلبات النظام:**
- Windows 10 أو أحدث
- Python 3.7+
- 4GB RAM (8GB مفضل)
- 2GB مساحة قرص

### **المكتبات المطلوبة:**
- opencv-python==********
- PyQt5==5.15.10
- numpy==1.24.3
- Pillow==10.0.1

### **إعدادات الشبكة:**
- فتح المنافذ: 554 (RTSP), 80/8080 (HTTP)
- إضافة استثناء في الجدار الناري
- تعطيل مكافح الفيروسات مؤقتاً للاختبار

---

## 📞 **الحصول على المساعدة:**

### **معلومات مفيدة عند طلب المساعدة:**
1. إصدار Windows
2. إصدار Python (`python --version`)
3. رسالة الخطأ الكاملة
4. خطوات إعادة إنتاج المشكلة

### **ملفات السجلات:**
- تشغيل `debug_launcher.py` للحصول على سجل مفصل
- نسخ رسائل الخطأ من Command Prompt
- لقطة شاشة لرسالة الخطأ

---

## ✅ **التحقق من نجاح الإصلاح:**

### **علامات النجاح:**
1. ظهور نافذة تسجيل الدخول
2. إمكانية تسجيل الدخول بـ admin/admin123
3. ظهور النافذة الرئيسية
4. إمكانية إضافة كاميرات

### **اختبار شامل:**
```bash
# تشغيل جميع الاختبارات
python test_system.py
```

---

## 🎉 **بعد حل المشكلة:**

### **للاستخدام اليومي:**
- استخدم `start_camera_system.bat`
- أو `python safe_start.py`

### **لإضافة كاميرات XVR:**
```bash
python xvr_camera_generator.py
```

### **لاختبار كاميرات جديدة:**
```bash
python camera_link_tester.py
```

---

## 🔄 **صيانة دورية:**

### **أسبوعياً:**
- تنظيف ملفات التسجيل القديمة
- فحص اتصال الكاميرات

### **شهرياً:**
- تحديث المكتبات
- نسخ احتياطية لقاعدة البيانات

### **عند الحاجة:**
- تشغيل `quick_crash_fix.py` للصيانة
- إعادة تشغيل النظام إذا كان بطيئاً

---

**💡 نصيحة:** احتفظ بهذا الدليل في مكان سهل الوصول للرجوع إليه عند الحاجة!
