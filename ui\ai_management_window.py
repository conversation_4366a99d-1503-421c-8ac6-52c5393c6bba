#!/usr/bin/env python3
"""
واجهة إدارة الذكاء الاصطناعي المتقدمة
Advanced AI Management Interface
"""

import sys
import os
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

# Add current directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ai_engine.face_recognition_engine import FaceRecognitionEngine
from ai_engine.object_detection_engine import ObjectDetectionEngine
from ai_engine.behavior_analysis_engine import BehaviorAnalysisEngine

class AIManagementWindow(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("🤖 إدارة الذكاء الاصطناعي المتقدم")
        self.setFixedSize(1000, 700)
        
        # Initialize AI engines
        self.face_engine = FaceRecognitionEngine()
        self.object_engine = ObjectDetectionEngine()
        self.behavior_engine = BehaviorAnalysisEngine()
        
        self.setup_ui()
        self.apply_style()
        self.load_data()
    
    def setup_ui(self):
        """Setup user interface"""
        layout = QVBoxLayout()
        
        # Header
        header = QLabel("🤖 إدارة الذكاء الاصطناعي المتقدم")
        header.setAlignment(Qt.AlignCenter)
        header.setStyleSheet("font-size: 20px; font-weight: bold; margin: 15px; color: #2c3e50;")
        layout.addWidget(header)
        
        # Tab widget
        self.tab_widget = QTabWidget()
        
        # Face Recognition Tab
        self.face_tab = self.create_face_recognition_tab()
        self.tab_widget.addTab(self.face_tab, "👤 التعرف على الوجوه")
        
        # Object Detection Tab
        self.object_tab = self.create_object_detection_tab()
        self.tab_widget.addTab(self.object_tab, "🎯 كشف الأجسام")
        
        # Behavior Analysis Tab
        self.behavior_tab = self.create_behavior_analysis_tab()
        self.tab_widget.addTab(self.behavior_tab, "🧠 تحليل السلوك")
        
        # AI Settings Tab
        self.settings_tab = self.create_ai_settings_tab()
        self.tab_widget.addTab(self.settings_tab, "⚙️ إعدادات الذكاء الاصطناعي")
        
        layout.addWidget(self.tab_widget)
        
        # Control buttons
        button_layout = QHBoxLayout()
        
        self.refresh_btn = QPushButton("🔄 تحديث البيانات")
        self.refresh_btn.clicked.connect(self.load_data)
        button_layout.addWidget(self.refresh_btn)
        
        button_layout.addStretch()
        
        self.close_btn = QPushButton("❌ إغلاق")
        self.close_btn.clicked.connect(self.accept)
        button_layout.addWidget(self.close_btn)
        
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
    
    def create_face_recognition_tab(self):
        """Create face recognition management tab"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # Known persons section
        persons_group = QGroupBox("الأشخاص المعروفون")
        persons_layout = QVBoxLayout()
        
        # Persons list
        self.persons_table = QTableWidget()
        self.persons_table.setColumnCount(5)
        self.persons_table.setHorizontalHeaderLabels([
            "الاسم", "الوصف", "آخر ظهور", "عدد المرات", "الإجراءات"
        ])
        self.persons_table.horizontalHeader().setStretchLastSection(True)
        persons_layout.addWidget(self.persons_table)
        
        # Add person controls
        add_person_layout = QHBoxLayout()
        
        self.person_name_input = QLineEdit()
        self.person_name_input.setPlaceholderText("اسم الشخص")
        add_person_layout.addWidget(QLabel("الاسم:"))
        add_person_layout.addWidget(self.person_name_input)
        
        self.person_desc_input = QLineEdit()
        self.person_desc_input.setPlaceholderText("وصف اختياري")
        add_person_layout.addWidget(QLabel("الوصف:"))
        add_person_layout.addWidget(self.person_desc_input)
        
        self.select_photo_btn = QPushButton("📷 اختيار صورة")
        self.select_photo_btn.clicked.connect(self.select_person_photo)
        add_person_layout.addWidget(self.select_photo_btn)
        
        self.add_person_btn = QPushButton("➕ إضافة شخص")
        self.add_person_btn.clicked.connect(self.add_person)
        add_person_layout.addWidget(self.add_person_btn)
        
        persons_layout.addLayout(add_person_layout)
        persons_group.setLayout(persons_layout)
        layout.addWidget(persons_group)
        
        # Face detection history
        history_group = QGroupBox("سجل التعرف على الوجوه")
        history_layout = QVBoxLayout()
        
        self.face_history_table = QTableWidget()
        self.face_history_table.setColumnCount(4)
        self.face_history_table.setHorizontalHeaderLabels([
            "الشخص", "الكاميرا", "الوقت", "الثقة"
        ])
        history_layout.addWidget(self.face_history_table)
        
        history_group.setLayout(history_layout)
        layout.addWidget(history_group)
        
        widget.setLayout(layout)
        return widget
    
    def create_object_detection_tab(self):
        """Create object detection management tab"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # Object settings
        settings_group = QGroupBox("إعدادات كشف الأجسام")
        settings_layout = QGridLayout()
        
        # Target objects
        settings_layout.addWidget(QLabel("الأجسام المستهدفة:"), 0, 0)
        
        self.object_checkboxes = {}
        row = 1
        col = 0
        
        for obj_class, settings in self.object_engine.target_objects.items():
            checkbox = QCheckBox(obj_class)
            checkbox.setChecked(settings['enabled'])
            checkbox.stateChanged.connect(lambda state, obj=obj_class: self.toggle_object_detection(obj, state))
            
            alert_checkbox = QCheckBox("تنبيه")
            alert_checkbox.setChecked(settings['alert'])
            alert_checkbox.stateChanged.connect(lambda state, obj=obj_class: self.toggle_object_alert(obj, state))
            
            self.object_checkboxes[obj_class] = (checkbox, alert_checkbox)
            
            settings_layout.addWidget(checkbox, row, col)
            settings_layout.addWidget(alert_checkbox, row, col + 1)
            
            col += 2
            if col >= 6:
                col = 0
                row += 1
        
        settings_group.setLayout(settings_layout)
        layout.addWidget(settings_group)
        
        # Detection statistics
        stats_group = QGroupBox("إحصائيات الكشف")
        stats_layout = QVBoxLayout()
        
        self.object_stats_table = QTableWidget()
        self.object_stats_table.setColumnCount(3)
        self.object_stats_table.setHorizontalHeaderLabels([
            "نوع الجسم", "عدد المرات", "متوسط الثقة"
        ])
        stats_layout.addWidget(self.object_stats_table)
        
        stats_group.setLayout(stats_layout)
        layout.addWidget(stats_group)
        
        # Recent detections
        recent_group = QGroupBox("الكشوفات الأخيرة")
        recent_layout = QVBoxLayout()
        
        self.recent_detections_table = QTableWidget()
        self.recent_detections_table.setColumnCount(4)
        self.recent_detections_table.setHorizontalHeaderLabels([
            "نوع الجسم", "الثقة", "الوقت", "تنبيه"
        ])
        recent_layout.addWidget(self.recent_detections_table)
        
        recent_group.setLayout(recent_layout)
        layout.addWidget(recent_group)
        
        widget.setLayout(layout)
        return widget
    
    def create_behavior_analysis_tab(self):
        """Create behavior analysis management tab"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # Behavior events
        events_group = QGroupBox("أحداث السلوك")
        events_layout = QVBoxLayout()
        
        # Filter controls
        filter_layout = QHBoxLayout()
        
        filter_layout.addWidget(QLabel("الفترة الزمنية:"))
        self.time_filter = QComboBox()
        self.time_filter.addItems(["آخر ساعة", "آخر 6 ساعات", "آخر 24 ساعة", "آخر أسبوع"])
        self.time_filter.currentTextChanged.connect(self.filter_behavior_events)
        filter_layout.addWidget(self.time_filter)
        
        filter_layout.addWidget(QLabel("مستوى الخطورة:"))
        self.severity_filter = QComboBox()
        self.severity_filter.addItems(["الكل", "منخفض", "متوسط", "عالي"])
        self.severity_filter.currentTextChanged.connect(self.filter_behavior_events)
        filter_layout.addWidget(self.severity_filter)
        
        filter_layout.addStretch()
        events_layout.addLayout(filter_layout)
        
        # Events table
        self.behavior_events_table = QTableWidget()
        self.behavior_events_table.setColumnCount(5)
        self.behavior_events_table.setHorizontalHeaderLabels([
            "نوع الحدث", "الوصف", "الخطورة", "الوقت", "الحالة"
        ])
        events_layout.addWidget(self.behavior_events_table)
        
        events_group.setLayout(events_layout)
        layout.addWidget(events_group)
        
        # Zone management
        zones_group = QGroupBox("إدارة المناطق")
        zones_layout = QVBoxLayout()
        
        zone_controls_layout = QHBoxLayout()
        
        self.zone_name_input = QLineEdit()
        self.zone_name_input.setPlaceholderText("اسم المنطقة")
        zone_controls_layout.addWidget(QLabel("اسم المنطقة:"))
        zone_controls_layout.addWidget(self.zone_name_input)
        
        self.zone_type_combo = QComboBox()
        self.zone_type_combo.addItems(["عادية", "محظورة", "مراقبة خاصة"])
        zone_controls_layout.addWidget(QLabel("نوع المنطقة:"))
        zone_controls_layout.addWidget(self.zone_type_combo)
        
        self.add_zone_btn = QPushButton("➕ إضافة منطقة")
        self.add_zone_btn.clicked.connect(self.add_analysis_zone)
        zone_controls_layout.addWidget(self.add_zone_btn)
        
        zones_layout.addLayout(zone_controls_layout)
        zones_group.setLayout(zones_layout)
        layout.addWidget(zones_group)
        
        widget.setLayout(layout)
        return widget
    
    def create_ai_settings_tab(self):
        """Create AI settings tab"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # Performance settings
        performance_group = QGroupBox("إعدادات الأداء")
        performance_layout = QFormLayout()
        
        # Face recognition settings
        self.face_model_combo = QComboBox()
        self.face_model_combo.addItems(["HOG (سريع)", "CNN (دقيق)"])
        performance_layout.addRow("نموذج التعرف على الوجوه:", self.face_model_combo)
        
        self.face_confidence_slider = QSlider(Qt.Horizontal)
        self.face_confidence_slider.setRange(0, 100)
        self.face_confidence_slider.setValue(60)
        self.face_confidence_label = QLabel("60%")
        self.face_confidence_slider.valueChanged.connect(
            lambda v: self.face_confidence_label.setText(f"{v}%")
        )
        confidence_layout = QHBoxLayout()
        confidence_layout.addWidget(self.face_confidence_slider)
        confidence_layout.addWidget(self.face_confidence_label)
        performance_layout.addRow("عتبة الثقة للوجوه:", confidence_layout)
        
        # Object detection settings
        self.object_confidence_slider = QSlider(Qt.Horizontal)
        self.object_confidence_slider.setRange(0, 100)
        self.object_confidence_slider.setValue(50)
        self.object_confidence_label = QLabel("50%")
        self.object_confidence_slider.valueChanged.connect(
            lambda v: self.object_confidence_label.setText(f"{v}%")
        )
        obj_confidence_layout = QHBoxLayout()
        obj_confidence_layout.addWidget(self.object_confidence_slider)
        obj_confidence_layout.addWidget(self.object_confidence_label)
        performance_layout.addRow("عتبة الثقة للأجسام:", obj_confidence_layout)
        
        # Frame processing
        self.frame_skip_spin = QSpinBox()
        self.frame_skip_spin.setRange(1, 10)
        self.frame_skip_spin.setValue(2)
        performance_layout.addRow("تخطي الإطارات:", self.frame_skip_spin)
        
        performance_group.setLayout(performance_layout)
        layout.addWidget(performance_group)
        
        # Behavior analysis settings
        behavior_group = QGroupBox("إعدادات تحليل السلوك")
        behavior_layout = QFormLayout()
        
        self.loitering_time_spin = QSpinBox()
        self.loitering_time_spin.setRange(60, 1800)
        self.loitering_time_spin.setValue(300)
        self.loitering_time_spin.setSuffix(" ثانية")
        behavior_layout.addRow("وقت التسكع:", self.loitering_time_spin)
        
        self.running_speed_spin = QDoubleSpinBox()
        self.running_speed_spin.setRange(1.0, 20.0)
        self.running_speed_spin.setValue(5.0)
        self.running_speed_spin.setSuffix(" بكسل/إطار")
        behavior_layout.addRow("سرعة الجري:", self.running_speed_spin)
        
        self.crowd_threshold_spin = QSpinBox()
        self.crowd_threshold_spin.setRange(2, 20)
        self.crowd_threshold_spin.setValue(5)
        self.crowd_threshold_spin.setSuffix(" أشخاص")
        behavior_layout.addRow("عتبة الحشد:", self.crowd_threshold_spin)
        
        behavior_group.setLayout(behavior_layout)
        layout.addWidget(behavior_group)
        
        # Apply settings button
        self.apply_settings_btn = QPushButton("✅ تطبيق الإعدادات")
        self.apply_settings_btn.clicked.connect(self.apply_ai_settings)
        layout.addWidget(self.apply_settings_btn)
        
        layout.addStretch()
        
        widget.setLayout(layout)
        return widget
    
    def load_data(self):
        """Load data into tables"""
        self.load_known_persons()
        self.load_face_history()
        self.load_object_statistics()
        self.load_recent_detections()
        self.load_behavior_events()
    
    def load_known_persons(self):
        """Load known persons into table"""
        persons = self.face_engine.get_known_persons()
        
        self.persons_table.setRowCount(len(persons))
        
        for row, person in enumerate(persons):
            self.persons_table.setItem(row, 0, QTableWidgetItem(person['name']))
            self.persons_table.setItem(row, 1, QTableWidgetItem(person['description'] or ""))
            self.persons_table.setItem(row, 2, QTableWidgetItem(person['last_seen'] or "لم يُرى بعد"))
            self.persons_table.setItem(row, 3, QTableWidgetItem(str(person['total_detections'])))
            
            # Action buttons
            action_widget = QWidget()
            action_layout = QHBoxLayout()
            action_layout.setContentsMargins(5, 2, 5, 2)
            
            edit_btn = QPushButton("✏️")
            edit_btn.setFixedSize(30, 25)
            edit_btn.clicked.connect(lambda checked, p_id=person['id']: self.edit_person(p_id))
            
            delete_btn = QPushButton("🗑️")
            delete_btn.setFixedSize(30, 25)
            delete_btn.clicked.connect(lambda checked, p_id=person['id']: self.delete_person(p_id))
            
            action_layout.addWidget(edit_btn)
            action_layout.addWidget(delete_btn)
            action_layout.addStretch()
            action_widget.setLayout(action_layout)
            
            self.persons_table.setCellWidget(row, 4, action_widget)
    
    def select_person_photo(self):
        """Select photo for new person"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "اختيار صورة الشخص", "", 
            "Image Files (*.png *.jpg *.jpeg *.bmp)"
        )
        
        if file_path:
            self.selected_photo_path = file_path
            self.select_photo_btn.setText(f"📷 {os.path.basename(file_path)}")
    
    def add_person(self):
        """Add new person"""
        name = self.person_name_input.text().strip()
        description = self.person_desc_input.text().strip()
        
        if not name:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم الشخص")
            return
        
        if not hasattr(self, 'selected_photo_path'):
            QMessageBox.warning(self, "خطأ", "يرجى اختيار صورة للشخص")
            return
        
        success, message = self.face_engine.add_known_person(name, self.selected_photo_path, description)
        
        if success:
            QMessageBox.information(self, "نجح", message)
            self.person_name_input.clear()
            self.person_desc_input.clear()
            self.select_photo_btn.setText("📷 اختيار صورة")
            delattr(self, 'selected_photo_path')
            self.load_known_persons()
        else:
            QMessageBox.warning(self, "خطأ", message)
    
    def toggle_object_detection(self, obj_class, state):
        """Toggle object detection for specific class"""
        enabled = state == Qt.Checked
        self.object_engine.configure_target_object(obj_class, enabled=enabled)
    
    def toggle_object_alert(self, obj_class, state):
        """Toggle alert for specific object class"""
        alert = state == Qt.Checked
        current_settings = self.object_engine.target_objects[obj_class]
        self.object_engine.configure_target_object(obj_class, enabled=current_settings['enabled'], alert=alert)
    
    def apply_ai_settings(self):
        """Apply AI settings"""
        # Face recognition settings
        face_model = "hog" if self.face_model_combo.currentIndex() == 0 else "cnn"
        self.face_engine.set_detection_model(face_model)
        
        face_confidence = self.face_confidence_slider.value() / 100.0
        self.face_engine.set_confidence_threshold(face_confidence)
        
        # Object detection settings
        obj_confidence = self.object_confidence_slider.value() / 100.0
        self.object_engine.set_confidence_threshold(obj_confidence)
        
        # Behavior analysis settings
        self.behavior_engine.loitering_time_threshold = self.loitering_time_spin.value()
        self.behavior_engine.running_speed_threshold = self.running_speed_spin.value()
        self.behavior_engine.crowd_density_threshold = self.crowd_threshold_spin.value()
        
        QMessageBox.information(self, "تم", "تم تطبيق الإعدادات بنجاح")
    
    def apply_style(self):
        """Apply custom styling"""
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #2c3e50;
            }
            QTableWidget {
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                background-color: white;
                gridline-color: #ecf0f1;
            }
            QTableWidget::item {
                padding: 8px;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
            QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox {
                padding: 8px;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                background-color: white;
            }
            QLineEdit:focus, QComboBox:focus, QSpinBox:focus, QDoubleSpinBox:focus {
                border-color: #3498db;
            }
            QSlider::groove:horizontal {
                border: 1px solid #bdc3c7;
                height: 8px;
                background: #ecf0f1;
                border-radius: 4px;
            }
            QSlider::handle:horizontal {
                background: #3498db;
                border: 1px solid #2980b9;
                width: 18px;
                margin: -5px 0;
                border-radius: 9px;
            }
            QTabWidget::pane {
                border: 1px solid #bdc3c7;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #ecf0f1;
                padding: 10px 15px;
                margin-right: 2px;
                border-top-left-radius: 5px;
                border-top-right-radius: 5px;
            }
            QTabBar::tab:selected {
                background-color: #3498db;
                color: white;
            }
        """)
    
    # Additional methods for loading data and handling events
    def load_face_history(self):
        """Load face detection history"""
        history = self.face_engine.get_detection_history(limit=50)
        self.face_history_table.setRowCount(len(history))
        
        for row, detection in enumerate(history):
            self.face_history_table.setItem(row, 0, QTableWidgetItem(detection['person_name']))
            self.face_history_table.setItem(row, 1, QTableWidgetItem(f"Camera {detection['camera_id']}"))
            self.face_history_table.setItem(row, 2, QTableWidgetItem(detection['timestamp']))
            self.face_history_table.setItem(row, 3, QTableWidgetItem(f"{detection['confidence']:.2f}"))
    
    def load_object_statistics(self):
        """Load object detection statistics"""
        stats = self.object_engine.get_detection_statistics()
        self.object_stats_table.setRowCount(len(stats))
        
        for row, stat in enumerate(stats):
            self.object_stats_table.setItem(row, 0, QTableWidgetItem(stat['object_class']))
            self.object_stats_table.setItem(row, 1, QTableWidgetItem(str(stat['count'])))
            self.object_stats_table.setItem(row, 2, QTableWidgetItem(f"{stat['avg_confidence']:.2f}"))
    
    def load_recent_detections(self):
        """Load recent object detections"""
        detections = self.object_engine.get_recent_detections(limit=50)
        self.recent_detections_table.setRowCount(len(detections))
        
        for row, detection in enumerate(detections):
            self.recent_detections_table.setItem(row, 0, QTableWidgetItem(detection['object_class']))
            self.recent_detections_table.setItem(row, 1, QTableWidgetItem(f"{detection['confidence']:.2f}"))
            self.recent_detections_table.setItem(row, 2, QTableWidgetItem(detection['timestamp']))
            self.recent_detections_table.setItem(row, 3, QTableWidgetItem("نعم" if detection['alert_triggered'] else "لا"))
    
    def load_behavior_events(self):
        """Load behavior events"""
        events = self.behavior_engine.get_behavior_events(hours=24)
        self.behavior_events_table.setRowCount(len(events))
        
        for row, event in enumerate(events):
            self.behavior_events_table.setItem(row, 0, QTableWidgetItem(event['event_type']))
            self.behavior_events_table.setItem(row, 1, QTableWidgetItem(event['description']))
            
            severity_text = ["منخفض", "متوسط", "عالي"][min(event['severity'] - 1, 2)]
            self.behavior_events_table.setItem(row, 2, QTableWidgetItem(severity_text))
            
            self.behavior_events_table.setItem(row, 3, QTableWidgetItem(event['timestamp']))
            self.behavior_events_table.setItem(row, 4, QTableWidgetItem("محلول" if event['resolved'] else "جديد"))
    
    def filter_behavior_events(self):
        """Filter behavior events based on selected criteria"""
        # Implementation for filtering
        pass
    
    def edit_person(self, person_id):
        """Edit person details"""
        # Implementation for editing person
        pass
    
    def delete_person(self, person_id):
        """Delete person"""
        reply = QMessageBox.question(self, "تأكيد الحذف", 
                                   "هل أنت متأكد من حذف هذا الشخص؟",
                                   QMessageBox.Yes | QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            success, message = self.face_engine.remove_known_person(person_id)
            if success:
                QMessageBox.information(self, "تم", message)
                self.load_known_persons()
            else:
                QMessageBox.warning(self, "خطأ", message)
    
    def add_analysis_zone(self):
        """Add analysis zone"""
        zone_name = self.zone_name_input.text().strip()
        if not zone_name:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم المنطقة")
            return
        
        # This would typically open a zone drawing interface
        QMessageBox.information(self, "معلومات", 
                              "سيتم فتح واجهة رسم المنطقة في الإصدار القادم")


def main():
    """Test the AI management window"""
    app = QApplication(sys.argv)
    
    # Set Arabic font
    font = QFont("Arial", 10)
    app.setFont(font)
    
    window = AIManagementWindow()
    window.show()
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
