#!/usr/bin/env python3
"""
أداة إصلاح مشكلة إغلاق النظام
System Crash Fixer Tool
"""

import sys
import os
import traceback
import subprocess
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class SystemCrashFixer(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.run_initial_diagnosis()

    def setup_ui(self):
        """إعداد واجهة أداة الإصلاح"""
        self.setWindowTitle("🔧 أداة إصلاح مشكلة إغلاق النظام")
        self.setGeometry(100, 100, 1000, 700)

        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        central_widget.setLayout(main_layout)

        # العنوان
        title = QLabel("🔧 أداة إصلاح مشكلة إغلاق النظام")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 20px; font-weight: bold; margin: 15px; color: #e74c3c;")
        main_layout.addWidget(title)

        # شريط التبويبات
        tab_widget = QTabWidget()

        # تبويب التشخيص السريع
        diagnosis_tab = self.create_diagnosis_tab()
        tab_widget.addTab(diagnosis_tab, "🔍 تشخيص سريع")

        # تبويب الحلول
        solutions_tab = self.create_solutions_tab()
        tab_widget.addTab(solutions_tab, "🛠️ الحلول")

        # تبويب السجلات
        logs_tab = self.create_logs_tab()
        tab_widget.addTab(logs_tab, "📋 السجلات")

        # تبويب الاختبار الآمن
        safe_test_tab = self.create_safe_test_tab()
        tab_widget.addTab(safe_test_tab, "🧪 اختبار آمن")

        main_layout.addWidget(tab_widget)

        # شريط الحالة
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("جاري التشخيص...")

        # تطبيق النمط
        self.apply_style()

    def create_diagnosis_tab(self):
        """إنشاء تبويب التشخيص"""
        widget = QWidget()
        layout = QVBoxLayout()

        # منطقة نتائج التشخيص
        self.diagnosis_text = QTextEdit()
        self.diagnosis_text.setReadOnly(True)
        layout.addWidget(self.diagnosis_text)

        # أزرار التشخيص
        button_layout = QHBoxLayout()

        quick_diag_btn = QPushButton("🔍 تشخيص سريع")
        quick_diag_btn.clicked.connect(self.run_quick_diagnosis)
        button_layout.addWidget(quick_diag_btn)

        full_diag_btn = QPushButton("🔬 تشخيص شامل")
        full_diag_btn.clicked.connect(self.run_full_diagnosis)
        button_layout.addWidget(full_diag_btn)

        test_imports_btn = QPushButton("📦 اختبار المكتبات")
        test_imports_btn.clicked.connect(self.test_imports)
        button_layout.addWidget(test_imports_btn)

        layout.addLayout(button_layout)

        widget.setLayout(layout)
        return widget

    def create_solutions_tab(self):
        """إنشاء تبويب الحلول"""
        widget = QWidget()
        layout = QVBoxLayout()

        # قائمة الحلول
        solutions_list = QListWidget()

        solutions = [
            "🔄 إعادة تثبيت المكتبات المطلوبة",
            "🧹 تنظيف ملفات Python المؤقتة",
            "🔧 إصلاح متغيرات البيئة",
            "📱 تشغيل في وضع التوافق",
            "🛡️ إضافة استثناء في مكافح الفيروسات",
            "🔒 تشغيل كمدير",
            "📊 فحص ذاكرة النظام",
            "🌐 إصلاح إعدادات الشبكة",
            "🗄️ إعادة إنشاء قاعدة البيانات",
            "⚙️ إعادة تعيين الإعدادات"
        ]

        for solution in solutions:
            item = QListWidgetItem(solution)
            solutions_list.addItem(item)

        solutions_list.itemDoubleClicked.connect(self.apply_solution)

        layout.addWidget(QLabel("اختر حل وانقر نقرتين لتطبيقه:"))
        layout.addWidget(solutions_list)

        # زر تطبيق الحل
        apply_btn = QPushButton("✅ تطبيق الحل المحدد")
        apply_btn.clicked.connect(lambda: self.apply_solution(solutions_list.currentItem()))
        layout.addWidget(apply_btn)

        widget.setLayout(layout)
        return widget

    def create_logs_tab(self):
        """إنشاء تبويب السجلات"""
        widget = QWidget()
        layout = QVBoxLayout()

        # منطقة عرض السجلات
        self.logs_text = QTextEdit()
        self.logs_text.setReadOnly(True)
        self.logs_text.setPlaceholderText("سجلات الأخطاء ستظهر هنا...")
        layout.addWidget(self.logs_text)

        # أزرار السجلات
        logs_button_layout = QHBoxLayout()

        read_logs_btn = QPushButton("📖 قراءة سجلات النظام")
        read_logs_btn.clicked.connect(self.read_system_logs)
        logs_button_layout.addWidget(read_logs_btn)

        clear_logs_btn = QPushButton("🧹 مسح السجلات")
        clear_logs_btn.clicked.connect(self.logs_text.clear)
        logs_button_layout.addWidget(clear_logs_btn)

        export_logs_btn = QPushButton("💾 تصدير السجلات")
        export_logs_btn.clicked.connect(self.export_logs)
        logs_button_layout.addWidget(export_logs_btn)

        layout.addLayout(logs_button_layout)

        widget.setLayout(layout)
        return widget

    def create_safe_test_tab(self):
        """إنشاء تبويب الاختبار الآمن"""
        widget = QWidget()
        layout = QVBoxLayout()

        # تعليمات الاختبار الآمن
        instructions = QLabel("""
        🧪 الاختبار الآمن للنظام

        هذا القسم يسمح لك بتشغيل النظام خطوة بخطوة لتحديد نقطة الفشل:

        1. اختبار استيراد المكتبات
        2. اختبار إنشاء قاعدة البيانات
        3. اختبار واجهة تسجيل الدخول
        4. اختبار النافذة الرئيسية
        5. اختبار الكاميرات
        """)
        instructions.setWordWrap(True)
        instructions.setStyleSheet("background-color: #f8f9fa; padding: 15px; border-radius: 5px;")
        layout.addWidget(instructions)

        # أزرار الاختبار التدريجي
        test_buttons = [
            ("1️⃣ اختبار المكتبات", self.test_step_1_imports),
            ("2️⃣ اختبار قاعدة البيانات", self.test_step_2_database),
            ("3️⃣ اختبار تسجيل الدخول", self.test_step_3_login),
            ("4️⃣ اختبار النافذة الرئيسية", self.test_step_4_main_window),
            ("5️⃣ اختبار الكاميرات", self.test_step_5_cameras)
        ]

        for button_text, button_function in test_buttons:
            btn = QPushButton(button_text)
            btn.clicked.connect(button_function)
            btn.setMinimumHeight(40)
            layout.addWidget(btn)

        # منطقة نتائج الاختبار
        self.test_results = QTextEdit()
        self.test_results.setMaximumHeight(200)
        self.test_results.setPlaceholderText("نتائج الاختبار التدريجي...")
        layout.addWidget(self.test_results)

        widget.setLayout(layout)
        return widget

    def log_message(self, message, msg_type="info"):
        """إضافة رسالة إلى السجل"""
        timestamp = QTime.currentTime().toString("hh:mm:ss")

        color_map = {
            "info": "#2c3e50",
            "success": "#27ae60",
            "warning": "#f39c12",
            "error": "#e74c3c"
        }

        color = color_map.get(msg_type, "#2c3e50")
        formatted_message = f'<span style="color: {color};">[{timestamp}] {message}</span>'

        # إضافة للتشخيص
        self.diagnosis_text.append(formatted_message)

        # إضافة للسجلات
        self.logs_text.append(formatted_message)

        # تحديث شريط الحالة
        self.status_bar.showMessage(message)

        QApplication.processEvents()

    def run_initial_diagnosis(self):
        """تشغيل التشخيص الأولي"""
        self.log_message("🔍 بدء التشخيص الأولي للنظام...", "info")

        try:
            # فحص Python
            python_version = sys.version.split()[0]
            self.log_message(f"🐍 إصدار Python: {python_version}", "success")

            # فحص المسار الحالي
            current_dir = os.getcwd()
            self.log_message(f"📁 المجلد الحالي: {current_dir}", "info")

            # فحص الملفات المطلوبة
            required_files = ['main.py', 'config.json', 'requirements.txt']
            missing_files = []

            for file in required_files:
                if os.path.exists(file):
                    self.log_message(f"✅ الملف موجود: {file}", "success")
                else:
                    missing_files.append(file)
                    self.log_message(f"❌ الملف مفقود: {file}", "error")

            if missing_files:
                self.log_message(f"⚠️ ملفات مفقودة: {', '.join(missing_files)}", "warning")

        except Exception as e:
            self.log_message(f"❌ خطأ في التشخيص الأولي: {str(e)}", "error")

    def run_quick_diagnosis(self):
        """تشغيل التشخيص السريع"""
        self.diagnosis_text.clear()
        self.log_message("🔍 بدء التشخيص السريع...", "info")

        try:
            # اختبار استيراد المكتبات الأساسية
            self.log_message("📦 اختبار المكتبات الأساسية...", "info")

            try:
                import cv2
                self.log_message(f"✅ OpenCV: {cv2.__version__}", "success")
            except ImportError as e:
                self.log_message(f"❌ OpenCV: {str(e)}", "error")

            try:
                from PyQt5.QtWidgets import QApplication
                self.log_message("✅ PyQt5: متوفر", "success")
            except ImportError as e:
                self.log_message(f"❌ PyQt5: {str(e)}", "error")

            try:
                import numpy as np
                self.log_message(f"✅ NumPy: {np.__version__}", "success")
            except ImportError as e:
                self.log_message(f"❌ NumPy: {str(e)}", "error")

            try:
                import sqlite3
                self.log_message("✅ SQLite3: متوفر", "success")
            except ImportError as e:
                self.log_message(f"❌ SQLite3: {str(e)}", "error")

            # اختبار الملفات المحلية
            self.log_message("📁 اختبار الملفات المحلية...", "info")

            try:
                from core.database import DatabaseManager
                self.log_message("✅ DatabaseManager: متوفر", "success")
            except Exception as e:
                self.log_message(f"❌ DatabaseManager: {str(e)}", "error")

            try:
                from ui.login_window import LoginWindow
                self.log_message("✅ LoginWindow: متوفر", "success")
            except Exception as e:
                self.log_message(f"❌ LoginWindow: {str(e)}", "error")

            self.log_message("✅ التشخيص السريع مكتمل", "success")

        except Exception as e:
            self.log_message(f"❌ خطأ في التشخيص السريع: {str(e)}", "error")

    def run_full_diagnosis(self):
        """تشغيل التشخيص الشامل"""
        self.diagnosis_text.clear()
        self.log_message("🔬 بدء التشخيص الشامل...", "info")

        # تشغيل التشخيص السريع أولاً
        self.run_quick_diagnosis()

        # فحوصات إضافية
        self.log_message("🔍 فحوصات إضافية...", "info")

        try:
            # فحص مساحة القرص
            import shutil
            total, used, free = shutil.disk_usage(".")
            free_gb = free / (1024**3)
            self.log_message(f"💾 مساحة القرص المتاحة: {free_gb:.2f} GB", "info")

            if free_gb < 1:
                self.log_message("⚠️ تحذير: مساحة القرص منخفضة", "warning")

            # فحص الذاكرة
            try:
                import psutil
                memory = psutil.virtual_memory()
                self.log_message(f"🧠 الذاكرة المتاحة: {memory.available / (1024**3):.2f} GB", "info")

                if memory.percent > 90:
                    self.log_message("⚠️ تحذير: استخدام الذاكرة مرتفع", "warning")
            except ImportError:
                self.log_message("ℹ️ psutil غير متوفر لفحص الذاكرة", "info")

            # فحص متغيرات البيئة
            python_path = os.environ.get('PYTHONPATH', 'غير محدد')
            self.log_message(f"🔧 PYTHONPATH: {python_path}", "info")

            # فحص قاعدة البيانات
            if os.path.exists('database.db'):
                db_size = os.path.getsize('database.db')
                self.log_message(f"🗄️ حجم قاعدة البيانات: {db_size} bytes", "info")
            else:
                self.log_message("⚠️ قاعدة البيانات غير موجودة", "warning")

            self.log_message("✅ التشخيص الشامل مكتمل", "success")

        except Exception as e:
            self.log_message(f"❌ خطأ في التشخيص الشامل: {str(e)}", "error")

    def test_imports(self):
        """اختبار تفصيلي للمكتبات"""
        self.diagnosis_text.clear()
        self.log_message("📦 اختبار تفصيلي للمكتبات...", "info")

        # قائمة المكتبات المطلوبة
        required_modules = {
            'sys': 'Python System',
            'os': 'Operating System',
            'cv2': 'OpenCV',
            'numpy': 'NumPy',
            'PyQt5.QtWidgets': 'PyQt5 Widgets',
            'PyQt5.QtCore': 'PyQt5 Core',
            'PyQt5.QtGui': 'PyQt5 GUI',
            'sqlite3': 'SQLite3',
            'threading': 'Threading',
            'time': 'Time',
            'datetime': 'DateTime',
            'json': 'JSON',
            'hashlib': 'Hash Library'
        }

        success_count = 0
        total_count = len(required_modules)

        for module, description in required_modules.items():
            try:
                __import__(module)
                self.log_message(f"✅ {description} ({module}): متوفر", "success")
                success_count += 1
            except ImportError as e:
                self.log_message(f"❌ {description} ({module}): {str(e)}", "error")
            except Exception as e:
                self.log_message(f"⚠️ {description} ({module}): خطأ غير متوقع - {str(e)}", "warning")

        # النتيجة النهائية
        if success_count == total_count:
            self.log_message(f"🎉 جميع المكتبات متوفرة ({success_count}/{total_count})", "success")
        else:
            missing_count = total_count - success_count
            self.log_message(f"⚠️ {missing_count} مكتبة مفقودة من أصل {total_count}", "warning")

    def apply_solution(self, item):
        """تطبيق الحل المحدد"""
        if not item:
            return

        solution = item.text()
        self.log_message(f"🛠️ تطبيق الحل: {solution}", "info")

        try:
            if "إعادة تثبيت المكتبات" in solution:
                self.reinstall_libraries()
            elif "تنظيف ملفات Python" in solution:
                self.clean_python_cache()
            elif "إصلاح متغيرات البيئة" in solution:
                self.fix_environment_variables()
            elif "وضع التوافق" in solution:
                self.run_compatibility_mode()
            elif "مكافح الفيروسات" in solution:
                self.add_antivirus_exception()
            elif "تشغيل كمدير" in solution:
                self.run_as_administrator()
            elif "فحص ذاكرة النظام" in solution:
                self.check_system_memory()
            elif "إعدادات الشبكة" in solution:
                self.fix_network_settings()
            elif "قاعدة البيانات" in solution:
                self.recreate_database()
            elif "إعادة تعيين الإعدادات" in solution:
                self.reset_settings()
            else:
                self.log_message("⚠️ حل غير معروف", "warning")

        except Exception as e:
            self.log_message(f"❌ خطأ في تطبيق الحل: {str(e)}", "error")

    def reinstall_libraries(self):
        """إعادة تثبيت المكتبات"""
        self.log_message("🔄 إعادة تثبيت المكتبات...", "info")

        try:
            # قراءة requirements.txt
            if os.path.exists('requirements.txt'):
                result = subprocess.run([
                    sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt', '--upgrade'
                ], capture_output=True, text=True, timeout=300)

                if result.returncode == 0:
                    self.log_message("✅ تم إعادة تثبيت المكتبات بنجاح", "success")
                else:
                    self.log_message(f"❌ فشل في إعادة التثبيت: {result.stderr}", "error")
            else:
                self.log_message("❌ ملف requirements.txt غير موجود", "error")

        except subprocess.TimeoutExpired:
            self.log_message("⏰ انتهت مهلة إعادة التثبيت", "warning")
        except Exception as e:
            self.log_message(f"❌ خطأ في إعادة التثبيت: {str(e)}", "error")

    def clean_python_cache(self):
        """تنظيف ملفات Python المؤقتة"""
        self.log_message("🧹 تنظيف ملفات Python المؤقتة...", "info")

        try:
            deleted_count = 0

            # حذف ملفات __pycache__
            for root, dirs, files in os.walk('.'):
                if '__pycache__' in dirs:
                    pycache_path = os.path.join(root, '__pycache__')
                    try:
                        import shutil
                        shutil.rmtree(pycache_path)
                        deleted_count += 1
                        self.log_message(f"🗑️ حذف: {pycache_path}", "info")
                    except Exception as e:
                        self.log_message(f"⚠️ فشل حذف {pycache_path}: {str(e)}", "warning")

                # حذف ملفات .pyc
                for file in files:
                    if file.endswith('.pyc'):
                        pyc_path = os.path.join(root, file)
                        try:
                            os.remove(pyc_path)
                            deleted_count += 1
                        except Exception as e:
                            self.log_message(f"⚠️ فشل حذف {pyc_path}: {str(e)}", "warning")

            self.log_message(f"✅ تم حذف {deleted_count} ملف مؤقت", "success")

        except Exception as e:
            self.log_message(f"❌ خطأ في التنظيف: {str(e)}", "error")