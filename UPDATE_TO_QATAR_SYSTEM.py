#!/usr/bin/env python3
"""
تحديث النظام لدعم أرقام السيارات القطرية
Update System for Qatar License Plates
"""

import sys
import os
import subprocess
import sqlite3
import json
from datetime import datetime

def print_update_banner():
    """طباعة شعار التحديث"""
    print("=" * 70)
    print("🇶🇦 تحديث النظام لدعم أرقام السيارات القطرية")
    print("🇶🇦 Update System for Qatar License Plates")
    print("=" * 70)
    print()
    print("🎯 التحديثات التي سيتم تطبيقها:")
    print("✅ تحديث أنماط أرقام السيارات للنظام القطري")
    print("✅ إضافة دعم اللوحات المميزة VIP")
    print("✅ إضافة دعم اللوحات الحكومية والدبلوماسية")
    print("✅ تحسين كشف اللوحات البيضاء القطرية")
    print("✅ إضافة قاعدة بيانات متخصصة للوحات القطرية")
    print("✅ إضافة أرقام سيارات قطرية تجريبية")
    print("✅ تحسين OCR للغة العربية")
    print()

def update_license_plate_patterns():
    """تحديث أنماط أرقام السيارات في الملف الأساسي"""
    print("🔄 تحديث أنماط أرقام السيارات...")
    
    try:
        # قراءة الملف الحالي
        if not os.path.exists('license_plate_detector.py'):
            print("⚠️ ملف license_plate_detector.py غير موجود")
            return False
        
        with open('license_plate_detector.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # البحث عن القسم المراد تحديثه
        old_patterns = '''        # أنماط أرقام السيارات
        self.plate_patterns = [
            r'[أ-ي]\\s*[أ-ي]\\s*[أ-ي]\\s*\\d{1,4}',  # نمط عربي
            r'[A-Z]\\s*[A-Z]\\s*[A-Z]\\s*\\d{1,4}',   # نمط إنجليزي
            r'\\d{1,4}\\s*[أ-ي]\\s*[أ-ي]\\s*[أ-ي]',  # نمط عربي معكوس
            r'\\d{1,4}\\s*[A-Z]\\s*[A-Z]\\s*[A-Z]'    # نمط إنجليزي معكوس
        ]'''
        
        new_patterns = '''        # أنماط أرقام السيارات القطرية
        self.plate_patterns = [
            # الأنماط القطرية الحديثة
            r'\\d{6}',                              # 6 أرقام (النمط الجديد)
            r'\\d{5}',                              # 5 أرقام
            r'\\d{1,3}\\s*[أ-ي]\\s*\\d{1,4}',        # رقم + حرف عربي + أرقام
            r'\\d{1,3}\\s*[A-Z]\\s*\\d{1,4}',        # رقم + حرف إنجليزي + أرقام
            r'[أ-ي]\\s*\\d{1,6}',                   # حرف عربي + أرقام
            r'[A-Z]\\s*\\d{1,6}',                   # حرف إنجليزي + أرقام
            
            # الأنماط القطرية التقليدية
            r'\\d{1,4}\\s*[أ-ي]',                   # أرقام + حرف عربي
            r'\\d{1,4}\\s*[A-Z]',                   # أرقام + حرف إنجليزي
            r'[أ-ي]\\s*[أ-ي]\\s*\\d{1,4}',          # حرفين عربي + أرقام
            r'[A-Z]\\s*[A-Z]\\s*\\d{1,4}',          # حرفين إنجليزي + أرقام
            
            # أنماط خاصة قطرية
            r'QAT\\s*\\d{1,6}',                     # QAT + أرقام
            r'QATAR\\s*\\d{1,6}',                   # QATAR + أرقام
            r'قطر\\s*\\d{1,6}',                     # قطر + أرقام
        ]'''
        
        if old_patterns in content:
            content = content.replace(old_patterns, new_patterns)
            
            # حفظ الملف المحدث
            with open('license_plate_detector.py', 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("✅ تم تحديث أنماط أرقام السيارات")
            return True
        else:
            print("⚠️ لم يتم العثور على الأنماط القديمة - ربما تم التحديث مسبقاً")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في تحديث الأنماط: {e}")
        return False

def setup_qatar_database():
    """إعداد قاعدة البيانات القطرية"""
    print("🗄️ إعداد قاعدة البيانات القطرية...")
    
    try:
        conn = sqlite3.connect('database.db')
        cursor = conn.cursor()
        
        # جدول أرقام السيارات القطرية
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS qatar_license_plates (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                camera_id INTEGER,
                plate_number TEXT,
                plate_type TEXT,
                confidence REAL,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                image_path TEXT,
                is_vip BOOLEAN DEFAULT 0,
                is_government BOOLEAN DEFAULT 0,
                is_diplomatic BOOLEAN DEFAULT 0,
                is_whitelisted BOOLEAN DEFAULT 0,
                is_blacklisted BOOLEAN DEFAULT 0,
                owner_info TEXT,
                FOREIGN KEY (camera_id) REFERENCES cameras (id)
            )
        ''')
        
        # جدول قوائم السيارات القطرية
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS qatar_plate_lists (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                plate_number TEXT UNIQUE NOT NULL,
                list_type TEXT CHECK(list_type IN ('whitelist', 'blacklist', 'vip', 'government')),
                owner_name TEXT,
                owner_id TEXT,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # تحديث جدول اللوحات الأساسي لدعم الأنواع القطرية
        cursor.execute('''
            ALTER TABLE license_plates ADD COLUMN plate_type TEXT DEFAULT 'standard'
        ''')
        
        cursor.execute('''
            ALTER TABLE license_plates ADD COLUMN is_vip BOOLEAN DEFAULT 0
        ''')
        
        cursor.execute('''
            ALTER TABLE license_plates ADD COLUMN is_government BOOLEAN DEFAULT 0
        ''')
        
        conn.commit()
        conn.close()
        
        print("✅ تم إعداد قاعدة البيانات القطرية")
        return True
        
    except sqlite3.OperationalError as e:
        if "duplicate column name" in str(e):
            print("✅ قاعدة البيانات محدثة مسبقاً")
            return True
        else:
            print(f"❌ خطأ في قاعدة البيانات: {e}")
            return False
    except Exception as e:
        print(f"❌ خطأ في إعداد قاعدة البيانات: {e}")
        return False

def add_qatar_sample_plates():
    """إضافة أرقام سيارات قطرية تجريبية"""
    print("🚗 إضافة أرقام سيارات قطرية تجريبية...")
    
    try:
        conn = sqlite3.connect('database.db')
        cursor = conn.cursor()
        
        # أرقام سيارات قطرية تجريبية
        qatar_plates = [
            # لوحات VIP قطرية
            ('1', 'vip', 'أمير قطر', '12345678901', 'لوحة رقم 1 - أمير البلاد'),
            ('7', 'vip', 'ولي العهد', '12345678902', 'لوحة رقم 7 - ولي العهد'),
            ('123', 'vip', 'شخصية مهمة', '12345678903', 'لوحة مميزة ثلاثة أرقام'),
            
            # لوحات حكومية قطرية
            ('POLICE1', 'government', 'شرطة قطر', 'POLICE001', 'مركبة شرطة'),
            ('ARMY1', 'government', 'الجيش القطري', 'ARMY001', 'مركبة عسكرية'),
            ('123أ', 'government', 'وزارة الداخلية', 'GOV001', 'مركبة حكومية'),
            
            # لوحات دبلوماسية
            ('CD1', 'government', 'السفارة الأمريكية', 'DIPL001', 'مركبة دبلوماسية'),
            ('CD2', 'government', 'السفارة البريطانية', 'DIPL002', 'مركبة دبلوماسية'),
            
            # لوحات حديثة - قائمة بيضاء
            ('123456', 'whitelist', 'مدير الشركة', '12345678904', 'لوحة حديثة 6 أرقام'),
            ('98765', 'whitelist', 'زائر معتمد', '12345678905', 'لوحة حديثة 5 أرقام'),
            ('456ب', 'whitelist', 'مقاول معتمد', '12345678906', 'لوحة تقليدية'),
            
            # لوحات - قائمة سوداء
            ('666666', 'blacklist', 'مركبة محظورة', '12345678907', 'مركبة غير مرغوب فيها'),
            ('13579', 'blacklist', 'شخص محظور', '12345678908', 'شخص محظور من الدخول'),
            ('789ج', 'blacklist', 'مركبة مشبوهة', '12345678909', 'مركبة تحت المراقبة'),
        ]
        
        # إضافة للجدول القطري
        for plate_number, list_type, owner_name, owner_id, description in qatar_plates:
            cursor.execute('''
                INSERT OR REPLACE INTO qatar_plate_lists 
                (plate_number, list_type, owner_name, owner_id, description)
                VALUES (?, ?, ?, ?, ?)
            ''', (plate_number, list_type, owner_name, owner_id, description))
        
        # إضافة للجدول الأساسي أيضاً
        for plate_number, list_type, owner_name, owner_id, description in qatar_plates:
            if list_type in ['whitelist', 'blacklist']:
                cursor.execute('''
                    INSERT OR REPLACE INTO plate_lists 
                    (plate_number, list_type, owner_name, description)
                    VALUES (?, ?, ?, ?)
                ''', (plate_number, list_type, owner_name, description))
        
        conn.commit()
        conn.close()
        
        print(f"✅ تم إضافة {len(qatar_plates)} لوحة قطرية تجريبية")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إضافة اللوحات: {e}")
        return False

def create_qatar_directories():
    """إنشاء مجلدات النظام القطري"""
    print("📁 إنشاء مجلدات النظام القطري...")
    
    directories = [
        'qatar_plates_database',
        'qatar_plates_database/vip',
        'qatar_plates_database/government',
        'qatar_plates_database/diplomatic',
        'qatar_plates_database/standard',
        'qatar_reports',
        'qatar_statistics'
    ]
    
    created_count = 0
    
    for directory in directories:
        try:
            os.makedirs(directory, exist_ok=True)
            print(f"✅ {directory}/")
            created_count += 1
        except Exception as e:
            print(f"❌ فشل في إنشاء {directory}: {e}")
    
    print(f"📊 تم إنشاء {created_count}/{len(directories)} مجلد")
    return created_count > 0

def update_config_files():
    """تحديث ملفات الإعدادات"""
    print("⚙️ تحديث ملفات الإعدادات...")
    
    try:
        # تحديث config.json
        config_updates = {
            "country": "Qatar",
            "license_plate_system": "qatar",
            "supported_patterns": [
                "modern_6_digits",
                "modern_5_digits", 
                "traditional_arabic",
                "vip_plates",
                "government_plates",
                "diplomatic_plates"
            ],
            "qatar_specific": {
                "vip_detection": True,
                "government_detection": True,
                "diplomatic_detection": True,
                "arabic_ocr": True
            }
        }
        
        # قراءة الإعدادات الحالية
        if os.path.exists('config.json'):
            with open('config.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
        else:
            config = {}
        
        # دمج التحديثات
        config.update(config_updates)
        
        # حفظ الإعدادات المحدثة
        with open('config.json', 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print("✅ تم تحديث config.json")
        
        # إنشاء ملف إعدادات قطري منفصل
        qatar_config = {
            "country_info": {
                "name": "دولة قطر",
                "name_en": "State of Qatar",
                "code": "QA",
                "flag": "🇶🇦"
            },
            "plate_detection": {
                "confidence_threshold": 0.75,
                "min_width": 100,
                "min_height": 30,
                "aspect_ratio_min": 2.5,
                "aspect_ratio_max": 5.5
            },
            "special_plates": {
                "vip_enabled": True,
                "government_enabled": True,
                "diplomatic_enabled": True,
                "police_enabled": True
            },
            "alerts": {
                "vip_alert": "تم كشف لوحة مميزة VIP",
                "government_alert": "تم كشف لوحة حكومية",
                "diplomatic_alert": "تم كشف لوحة دبلوماسية",
                "blacklist_alert": "تحذير: لوحة محظورة"
            }
        }
        
        with open('qatar_config.json', 'w', encoding='utf-8') as f:
            json.dump(qatar_config, f, indent=2, ensure_ascii=False)
        
        print("✅ تم إنشاء qatar_config.json")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تحديث الإعدادات: {e}")
        return False

def test_qatar_update():
    """اختبار التحديث القطري"""
    print("🧪 اختبار التحديث القطري...")
    
    try:
        # اختبار قاعدة البيانات
        conn = sqlite3.connect('database.db')
        cursor = conn.cursor()
        
        # فحص الجداول القطرية
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE 'qatar_%'")
        qatar_tables = cursor.fetchall()
        print(f"✅ {len(qatar_tables)} جدول قطري")
        
        # فحص اللوحات القطرية
        cursor.execute("SELECT COUNT(*) FROM qatar_plate_lists")
        qatar_plates = cursor.fetchone()[0]
        print(f"✅ {qatar_plates} لوحة قطرية")
        
        # فحص أنواع اللوحات
        cursor.execute("SELECT COUNT(*) FROM qatar_plate_lists WHERE list_type = 'vip'")
        vip_plates = cursor.fetchone()[0]
        print(f"✅ {vip_plates} لوحة VIP")
        
        cursor.execute("SELECT COUNT(*) FROM qatar_plate_lists WHERE list_type = 'government'")
        gov_plates = cursor.fetchone()[0]
        print(f"✅ {gov_plates} لوحة حكومية")
        
        conn.close()
        
        # فحص الملفات
        files_check = [
            ('qatar_license_plate_detector.py', 'محرك كشف اللوحات القطرية'),
            ('qatar_config.json', 'إعدادات النظام القطري'),
            ('setup_qatar_plates.py', 'أداة إعداد النظام القطري')
        ]
        
        for filename, description in files_check:
            if os.path.exists(filename):
                print(f"✅ {description}")
            else:
                print(f"⚠️ {description} غير موجود")
        
        # فحص المجلدات
        if os.path.exists('qatar_plates_database'):
            print("✅ مجلدات النظام القطري")
        
        print("✅ التحديث القطري مكتمل")
        return True
        
    except Exception as e:
        print(f"❌ فشل اختبار التحديث: {e}")
        return False

def main():
    """الوظيفة الرئيسية"""
    print_update_banner()
    
    confirm = input("هل تريد تحديث النظام لدعم أرقام السيارات القطرية؟ (y/n): ").lower()
    if confirm not in ['y', 'yes', 'نعم']:
        print("❌ تم إلغاء التحديث")
        return
    
    print("\n🚀 بدء تحديث النظام للنظام القطري...")
    
    # خطوات التحديث
    steps = [
        ("تحديث أنماط أرقام السيارات", update_license_plate_patterns),
        ("إعداد قاعدة البيانات القطرية", setup_qatar_database),
        ("إضافة أرقام سيارات قطرية تجريبية", add_qatar_sample_plates),
        ("إنشاء مجلدات النظام القطري", create_qatar_directories),
        ("تحديث ملفات الإعدادات", update_config_files),
        ("اختبار التحديث", test_qatar_update)
    ]
    
    completed_steps = 0
    
    for step_name, step_function in steps:
        print(f"\n🔄 {step_name}...")
        try:
            if step_function():
                print(f"✅ {step_name}: مكتمل")
                completed_steps += 1
            else:
                print(f"⚠️ {step_name}: فشل جزئي")
        except Exception as e:
            print(f"❌ {step_name}: فشل - {e}")
    
    # النتائج
    print("\n" + "=" * 70)
    print("📊 نتائج تحديث النظام القطري:")
    print("=" * 70)
    print(f"✅ تم إكمال {completed_steps}/{len(steps)} خطوة")
    
    if completed_steps >= 4:
        print("🎉 تم تحديث النظام للنظام القطري بنجاح!")
        print("\n🇶🇦 الميزات الجديدة:")
        print("✅ كشف اللوحات القطرية الحديثة (6 و 5 أرقام)")
        print("✅ كشف اللوحات القطرية التقليدية (أرقام + أحرف)")
        print("✅ كشف اللوحات المميزة VIP")
        print("✅ كشف اللوحات الحكومية والدبلوماسية")
        print("✅ دعم محسن للغة العربية")
        print("✅ قاعدة بيانات متخصصة للوحات القطرية")
        print("✅ إحصائيات مفصلة للأنواع المختلفة")
        
        print("\n📋 ملفات جديدة:")
        print("• qatar_license_plate_detector.py - محرك كشف اللوحات القطرية")
        print("• qatar_config.json - إعدادات النظام القطري")
        print("• setup_qatar_plates.py - أداة إعداد النظام القطري")
        print("• qatar_plates_database/ - مجلد صور اللوحات القطرية")
        
        print("\n🚀 للاستخدام:")
        print("• python qatar_license_plate_detector.py - لاختبار النظام القطري")
        print("• python setup_qatar_plates.py - لإعداد إضافي")
        print("• python main.py - لتشغيل النظام الكامل")
        
        # خيار تشغيل الإعداد القطري
        setup_now = input("\nهل تريد تشغيل إعداد النظام القطري الكامل؟ (y/n): ").lower()
        if setup_now in ['y', 'yes', 'نعم']:
            print("\n🇶🇦 تشغيل إعداد النظام القطري...")
            try:
                subprocess.run([sys.executable, 'setup_qatar_plates.py'])
            except Exception as e:
                print(f"❌ فشل تشغيل الإعداد: {e}")
        
    else:
        print("⚠️ التحديث غير مكتمل")
        print("💡 راجع الأخطاء أعلاه وأعد المحاولة")

if __name__ == "__main__":
    try:
        main()
        input("\nاضغط Enter للخروج...")
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف التحديث")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        input("اضغط Enter للخروج...")
