#!/usr/bin/env python3
"""
نظام قراءة أرقام السيارات القطرية المتخصص
Qatar License Plate Recognition System
"""

import cv2
import numpy as np
import sqlite3
import os
import re
from datetime import datetime
import threading

class QatarLicensePlateDetector:
    def __init__(self, database_path="database.db"):
        self.database_path = database_path

        # إعدادات كشف اللوحات القطرية
        self.min_plate_width = 100
        self.min_plate_height = 30
        self.max_plate_width = 400
        self.max_plate_height = 120
        self.confidence_threshold = 0.75

        # إعدادات OCR للغة العربية
        self.ocr_available = False
        self.init_ocr()

        # إعدادات الحفظ
        self.save_plate_images = True
        self.plates_path = "qatar_plates_database"

        # أنماط أرقام السيارات القطرية المحدثة
        self.qatar_plate_patterns = {
            # الأنماط الحديثة (2018+)
            'modern_6_digits': r'^\d{6}$',                    # 123456
            'modern_5_digits': r'^\d{5}$',                    # 12345

            # الأنماط التقليدية
            'traditional_number_letter': r'^\d{1,4}[أ-ي]$',   # 123أ
            'traditional_letter_number': r'^[أ-ي]\d{1,5}$',   # أ12345
            'traditional_english': r'^\d{1,4}[A-Z]$',        # 123A

            # الأنماط الخاصة
            'vip_single': r'^\d{1,3}$',                      # 1, 12, 123 (VIP)
            'government': r'^[1-9]\d{0,2}[أ-ي]{1,2}$',      # حكومية
            'diplomatic': r'^CD\d{1,4}$',                    # دبلوماسية
            'police': r'^POLICE\d{1,4}$',                    # شرطة
            'military': r'^ARMY\d{1,4}$',                    # عسكرية

            # أنماط مع فواصل
            'spaced_modern': r'^\d{3}\s+\d{3}$',            # 123 456
            'spaced_traditional': r'^\d{1,3}\s+[أ-ي]\s+\d{1,3}$',  # 12 أ 34
        }

        # قاموس الأحرف العربية للتحويل
        self.arabic_letters = {
            'أ': 'A', 'ب': 'B', 'ت': 'T', 'ث': 'TH', 'ج': 'J',
            'ح': 'H', 'خ': 'KH', 'د': 'D', 'ذ': 'TH', 'ر': 'R',
            'ز': 'Z', 'س': 'S', 'ش': 'SH', 'ص': 'S', 'ض': 'D',
            'ط': 'T', 'ظ': 'Z', 'ع': 'A', 'غ': 'GH', 'ف': 'F',
            'ق': 'Q', 'ك': 'K', 'ل': 'L', 'م': 'M', 'ن': 'N',
            'ه': 'H', 'و': 'W', 'ي': 'Y'
        }

        # قاموس الأرقام العربية
        self.arabic_numbers = {
            '٠': '0', '١': '1', '٢': '2', '٣': '3', '٤': '4',
            '٥': '5', '٦': '6', '٧': '7', '٨': '8', '٩': '9'
        }

        # إنشاء مجلد الحفظ
        os.makedirs(self.plates_path, exist_ok=True)

        # تهيئة قاعدة البيانات
        self.init_qatar_database()

    def init_ocr(self):
        """تهيئة محرك OCR للغة العربية"""
        try:
            import pytesseract
            self.pytesseract = pytesseract
            self.ocr_available = True
            print("✅ محرك OCR متوفر للغة العربية")
        except ImportError:
            print("⚠️ pytesseract غير متوفر - سيتم استخدام كشف بسيط")
            self.ocr_available = False

    def init_qatar_database(self):
        """تهيئة قاعدة البيانات للوحات القطرية"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()

            # جدول أرقام السيارات القطرية
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS qatar_license_plates (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    camera_id INTEGER,
                    plate_number TEXT,
                    plate_type TEXT,
                    confidence REAL,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    image_path TEXT,
                    is_vip BOOLEAN DEFAULT 0,
                    is_government BOOLEAN DEFAULT 0,
                    is_diplomatic BOOLEAN DEFAULT 0,
                    is_whitelisted BOOLEAN DEFAULT 0,
                    is_blacklisted BOOLEAN DEFAULT 0,
                    owner_info TEXT,
                    FOREIGN KEY (camera_id) REFERENCES cameras (id)
                )
            ''')

            # جدول قوائم السيارات القطرية
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS qatar_plate_lists (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    plate_number TEXT UNIQUE NOT NULL,
                    list_type TEXT CHECK(list_type IN ('whitelist', 'blacklist', 'vip', 'government')),
                    owner_name TEXT,
                    owner_id TEXT,
                    description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # جدول إحصائيات اللوحات القطرية
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS qatar_plate_stats (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    date DATE,
                    total_detections INTEGER DEFAULT 0,
                    vip_detections INTEGER DEFAULT 0,
                    government_detections INTEGER DEFAULT 0,
                    diplomatic_detections INTEGER DEFAULT 0,
                    blacklist_detections INTEGER DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            conn.commit()
            conn.close()

        except Exception as e:
            print(f"خطأ في تهيئة قاعدة بيانات اللوحات القطرية: {e}")

    def normalize_arabic_text(self, text):
        """تطبيع النص العربي"""
        if not text:
            return ""

        # تحويل الأرقام العربية إلى إنجليزية
        for arabic, english in self.arabic_numbers.items():
            text = text.replace(arabic, english)

        # إزالة التشكيل والرموز غير المرغوبة
        text = re.sub(r'[ًٌٍَُِّْ]', '', text)  # إزالة التشكيل
        text = re.sub(r'[^\w\s]', '', text)      # إزالة الرموز
        text = re.sub(r'\s+', ' ', text).strip() # تنظيف المسافات

        return text

    def detect_qatar_plate_type(self, plate_text):
        """تحديد نوع اللوحة القطرية"""
        if not plate_text:
            return "unknown"

        plate_text = self.normalize_arabic_text(plate_text)

        # فحص الأنماط المختلفة
        for pattern_name, pattern in self.qatar_plate_patterns.items():
            if re.match(pattern, plate_text):
                if 'modern' in pattern_name:
                    return "modern"
                elif 'traditional' in pattern_name:
                    return "traditional"
                elif 'vip' in pattern_name:
                    return "vip"
                elif 'government' in pattern_name:
                    return "government"
                elif 'diplomatic' in pattern_name:
                    return "diplomatic"
                elif 'police' in pattern_name:
                    return "police"
                elif 'military' in pattern_name:
                    return "military"

        # فحص إضافي للأنماط الخاصة
        if len(plate_text.replace(' ', '')) <= 3 and plate_text.isdigit():
            return "vip"

        if any(keyword in plate_text.upper() for keyword in ['CD', 'POLICE', 'ARMY']):
            return "special"

        return "standard"

    def validate_qatar_plate(self, text):
        """التحقق من صحة رقم اللوحة القطرية"""
        if not text or len(text) < 1:
            return False, 0.0

        text = self.normalize_arabic_text(text)

        # فحص الأنماط القطرية
        for pattern_name, pattern in self.qatar_plate_patterns.items():
            if re.match(pattern, text):
                # تحديد مستوى الثقة حسب النمط
                if 'modern' in pattern_name:
                    return True, 0.95
                elif 'traditional' in pattern_name:
                    return True, 0.90
                elif 'vip' in pattern_name:
                    return True, 0.98
                elif 'government' in pattern_name:
                    return True, 0.92
                else:
                    return True, 0.85

        # فحص بسيط للأرقام والأحرف
        has_numbers = bool(re.search(r'\d', text))
        has_arabic = bool(re.search(r'[أ-ي]', text))
        has_english = bool(re.search(r'[A-Z]', text))

        if has_numbers and (has_arabic or has_english):
            return True, 0.75

        if text.isdigit() and 1 <= len(text) <= 6:
            return True, 0.80

        return False, 0.3

    def extract_text_qatar_ocr(self, plate_roi):
        """استخراج النص باستخدام OCR محسن للوحات القطرية"""
        if not self.ocr_available:
            return ""

        try:
            # تحسين جودة الصورة للوحات القطرية
            gray = cv2.cvtColor(plate_roi, cv2.COLOR_BGR2GRAY)

            # تطبيق فلاتر خاصة للوحات القطرية
            # 1. تحسين التباين
            clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
            gray = clahe.apply(gray)

            # 2. تطبيق Gaussian blur خفيف
            gray = cv2.GaussianBlur(gray, (3, 3), 0)

            # 3. تطبيق threshold متكيف
            thresh = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                         cv2.THRESH_BINARY, 11, 2)

            # 4. تكبير الصورة لتحسين دقة OCR
            scale_factor = 4
            height, width = thresh.shape
            resized = cv2.resize(thresh, (width * scale_factor, height * scale_factor))

            # إعدادات OCR محسنة للغة العربية والإنجليزية
            config = '--oem 3 --psm 8 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZأبتثجحخدذرزسشصضطظعغفقكلمنهوي'

            # استخراج النص بالعربية والإنجليزية
            text_ara = self.pytesseract.image_to_string(resized, lang='ara', config=config)
            text_eng = self.pytesseract.image_to_string(resized, lang='eng', config=config)

            # اختيار أفضل نتيجة
            text_ara = self.normalize_arabic_text(text_ara)
            text_eng = self.normalize_arabic_text(text_eng)

            # تقييم النتائج
            ara_valid, ara_conf = self.validate_qatar_plate(text_ara)
            eng_valid, eng_conf = self.validate_qatar_plate(text_eng)

            if ara_valid and eng_valid:
                return text_ara if ara_conf > eng_conf else text_eng
            elif ara_valid:
                return text_ara
            elif eng_valid:
                return text_eng
            else:
                # إرجاع النص الأطول إذا لم يكن أي منهما صالح
                return text_ara if len(text_ara) > len(text_eng) else text_eng

        except Exception as e:
            print(f"خطأ في OCR القطري: {e}")
            return ""

    def detect_qatar_license_plates(self, frame, camera_id=None):
        """الوظيفة الرئيسية لكشف أرقام السيارات القطرية"""
        detected_plates = []

        # كشف اللوحات باستخدام طرق مختلفة
        contour_plates = self.detect_plates_contour_qatar(frame)

        for plate_data in contour_plates:
            bbox = plate_data['bbox']
            plate_roi = plate_data['roi']
            detection_confidence = plate_data['confidence']

            # استخراج النص
            if self.ocr_available:
                plate_text = self.extract_text_qatar_ocr(plate_roi)
            else:
                plate_text = self.extract_text_simple_qatar(plate_roi)

            # التحقق من صحة النص
            is_valid, text_confidence = self.validate_qatar_plate(plate_text)

            if is_valid:
                # تحديد نوع اللوحة
                plate_type = self.detect_qatar_plate_type(plate_text)

                # حساب الثقة الإجمالية
                overall_confidence = (detection_confidence + text_confidence) / 2

                # فحص القوائم والحالات الخاصة
                is_vip = plate_type == "vip"
                is_government = plate_type in ["government", "police", "military"]
                is_diplomatic = plate_type == "diplomatic"
                is_whitelisted, is_blacklisted = self.check_qatar_plate_lists(plate_text)

                plate_info = {
                    'bbox': bbox,
                    'text': plate_text,
                    'type': plate_type,
                    'confidence': overall_confidence,
                    'is_vip': is_vip,
                    'is_government': is_government,
                    'is_diplomatic': is_diplomatic,
                    'is_whitelisted': is_whitelisted,
                    'is_blacklisted': is_blacklisted,
                    'roi': plate_roi
                }

                detected_plates.append(plate_info)

                # تسجيل في قاعدة البيانات
                if camera_id:
                    self.log_qatar_plate_detection(camera_id, plate_info)

                # حفظ صورة اللوحة
                if self.save_plate_images:
                    self.save_qatar_plate_image(plate_roi, plate_text, plate_type, camera_id)

                print(f"🇶🇦 كشف لوحة قطرية: {plate_text} ({plate_type}) - ثقة: {overall_confidence:.2f}")

        return detected_plates

    def detect_plates_contour_qatar(self, frame):
        """كشف اللوحات القطرية باستخدام تحليل الكونتور المحسن"""
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

        # تطبيق فلاتر محسنة للوحات القطرية
        gray = cv2.bilateralFilter(gray, 11, 17, 17)

        # تحسين التباين للوحات البيضاء القطرية
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
        gray = clahe.apply(gray)

        # كشف الحواف
        edged = cv2.Canny(gray, 30, 200)

        # العثور على الكونتورات
        contours, _ = cv2.findContours(edged, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)
        contours = sorted(contours, key=cv2.contourArea, reverse=True)[:15]

        detected_plates = []

        for contour in contours:
            # تقريب الكونتور
            epsilon = 0.018 * cv2.arcLength(contour, True)
            approx = cv2.approxPolyDP(contour, epsilon, True)

            # البحث عن مستطيلات (4 نقاط)
            if len(approx) == 4:
                x, y, w, h = cv2.boundingRect(approx)

                # فحص نسبة العرض إلى الارتفاع للوحات القطرية
                aspect_ratio = w / h

                # اللوحات القطرية لها نسب محددة
                if (2.5 <= aspect_ratio <= 5.5 and
                    w >= self.min_plate_width and h >= self.min_plate_height and
                    w <= self.max_plate_width and h <= self.max_plate_height):

                    plate_roi = frame[y:y+h, x:x+w]

                    # حساب الثقة بناءً على خصائص اللوحة القطرية
                    confidence = self.calculate_qatar_plate_confidence(plate_roi, aspect_ratio)

                    if confidence > self.confidence_threshold:
                        detected_plates.append({
                            'bbox': (x, y, w, h),
                            'roi': plate_roi,
                            'confidence': confidence
                        })

        return detected_plates

    def calculate_qatar_plate_confidence(self, plate_roi, aspect_ratio):
        """حساب ثقة كشف اللوحة القطرية"""
        try:
            gray_plate = cv2.cvtColor(plate_roi, cv2.COLOR_BGR2GRAY)

            # فحص التباين (اللوحات القطرية لها تباين عالي)
            contrast = gray_plate.std()

            # فحص نسبة العرض إلى الارتفاع للوحات القطرية
            aspect_score = 1.0 if 3.0 <= aspect_ratio <= 4.5 else 0.7

            # فحص اللون الأبيض المهيمن (اللوحات القطرية بيضاء)
            mean_brightness = gray_plate.mean()
            brightness_score = 1.0 if mean_brightness > 150 else 0.6

            # فحص وجود حواف (النصوص لها حواف كثيرة)
            edges = cv2.Canny(gray_plate, 50, 150)
            edge_density = np.sum(edges > 0) / edges.size

            # فحص التوزيع الأفقي للبكسل (النصوص موزعة أفقياً)
            horizontal_projection = np.sum(gray_plate < 100, axis=0)
            horizontal_variance = np.var(horizontal_projection)
            horizontal_score = min(horizontal_variance / 1000, 1.0)

            # حساب الثقة الإجمالية
            confidence = (
                (contrast / 80.0) * 0.3 +
                aspect_score * 0.2 +
                brightness_score * 0.2 +
                (edge_density * 15) * 0.2 +
                horizontal_score * 0.1
            )

            return min(confidence, 1.0)

        except:
            return 0.5

    def extract_text_simple_qatar(self, plate_roi):
        """استخراج النص بطريقة بسيطة للوحات القطرية"""
        try:
            gray = cv2.cvtColor(plate_roi, cv2.COLOR_BGR2GRAY)

            # تطبيق threshold محسن للوحات القطرية البيضاء
            _, thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

            # العثور على الكونتورات (الأحرف والأرقام)
            contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            # فرز الكونتورات من اليسار إلى اليمين (للنص الإنجليزي)
            # أو من اليمين إلى اليسار (للنص العربي)
            contours = sorted(contours, key=lambda c: cv2.boundingRect(c)[0])

            # عد الأحرف المحتملة
            char_count = 0
            char_areas = []

            for contour in contours:
                x, y, w, h = cv2.boundingRect(contour)
                area = w * h

                # فحص حجم معقول للحرف في اللوحة القطرية
                if (w > 8 and h > 15 and w < plate_roi.shape[1]/2 and
                    h < plate_roi.shape[0] and area > 100):
                    char_count += 1
                    char_areas.append(area)

            # تحليل نمط الأحرف لتحديد نوع اللوحة
            if 4 <= char_count <= 8:
                avg_area = np.mean(char_areas) if char_areas else 0

                # تقدير نوع اللوحة بناءً على عدد الأحرف
                if char_count == 6:
                    return f"QATAR_{char_count}_MODERN"
                elif char_count == 5:
                    return f"QATAR_{char_count}_STANDARD"
                elif char_count <= 3:
                    return f"QATAR_{char_count}_VIP"
                else:
                    return f"QATAR_{char_count}_TRADITIONAL"
            else:
                return ""

        except:
            return ""

    def check_qatar_plate_lists(self, plate_number):
        """فحص القوائم القطرية للوحات"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT list_type FROM qatar_plate_lists
                WHERE plate_number = ?
            ''', (plate_number,))

            result = cursor.fetchone()
            conn.close()

            if result:
                list_type = result[0]
                return list_type == 'whitelist', list_type == 'blacklist'

            return False, False

        except Exception as e:
            print(f"خطأ في فحص قوائم اللوحات القطرية: {e}")
            return False, False

    def log_qatar_plate_detection(self, camera_id, plate_info):
        """تسجيل كشف اللوحة القطرية في قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO qatar_license_plates
                (camera_id, plate_number, plate_type, confidence,
                 is_vip, is_government, is_diplomatic, is_whitelisted, is_blacklisted)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                camera_id,
                plate_info['text'],
                plate_info['type'],
                plate_info['confidence'],
                plate_info['is_vip'],
                plate_info['is_government'],
                plate_info['is_diplomatic'],
                plate_info['is_whitelisted'],
                plate_info['is_blacklisted']
            ))

            conn.commit()
            conn.close()

            # تحديث الإحصائيات اليومية
            self.update_daily_stats(plate_info)

        except Exception as e:
            print(f"خطأ في تسجيل كشف اللوحة القطرية: {e}")

    def update_daily_stats(self, plate_info):
        """تحديث الإحصائيات اليومية"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()

            today = datetime.now().date()

            # البحث عن سجل اليوم
            cursor.execute('''
                SELECT id FROM qatar_plate_stats WHERE date = ?
            ''', (today,))

            if cursor.fetchone():
                # تحديث السجل الموجود
                update_query = "UPDATE qatar_plate_stats SET total_detections = total_detections + 1"

                if plate_info['is_vip']:
                    update_query += ", vip_detections = vip_detections + 1"
                if plate_info['is_government']:
                    update_query += ", government_detections = government_detections + 1"
                if plate_info['is_diplomatic']:
                    update_query += ", diplomatic_detections = diplomatic_detections + 1"
                if plate_info['is_blacklisted']:
                    update_query += ", blacklist_detections = blacklist_detections + 1"

                update_query += " WHERE date = ?"
                cursor.execute(update_query, (today,))
            else:
                # إنشاء سجل جديد
                cursor.execute('''
                    INSERT INTO qatar_plate_stats
                    (date, total_detections, vip_detections, government_detections,
                     diplomatic_detections, blacklist_detections)
                    VALUES (?, 1, ?, ?, ?, ?)
                ''', (
                    today,
                    1 if plate_info['is_vip'] else 0,
                    1 if plate_info['is_government'] else 0,
                    1 if plate_info['is_diplomatic'] else 0,
                    1 if plate_info['is_blacklisted'] else 0
                ))

            conn.commit()
            conn.close()

        except Exception as e:
            print(f"خطأ في تحديث الإحصائيات: {e}")

    def save_qatar_plate_image(self, plate_roi, plate_text, plate_type, camera_id):
        """حفظ صورة اللوحة القطرية"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            camera_str = f"cam{camera_id}" if camera_id else "unknown"
            safe_text = re.sub(r'[^\w]', '_', plate_text)
            filename = f"qatar_{plate_type}_{camera_str}_{safe_text}_{timestamp}.jpg"
            filepath = os.path.join(self.plates_path, filename)

            cv2.imwrite(filepath, plate_roi)
            return filepath

        except Exception as e:
            print(f"خطأ في حفظ صورة اللوحة القطرية: {e}")
            return None