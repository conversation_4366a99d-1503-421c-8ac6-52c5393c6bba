#!/usr/bin/env python3
"""
إصلاح سريع لمشكلة إغلاق النظام
Quick System Crash Fix
"""

import sys
import os
import subprocess
import traceback

def print_header():
    """طباعة رأس البرنامج"""
    print("=" * 60)
    print("🔧 إصلاح سريع لمشكلة إغلاق النظام")
    print("Quick System Crash Fix")
    print("=" * 60)
    print()

def test_basic_imports():
    """اختبار المكتبات الأساسية"""
    print("📦 اختبار المكتبات الأساسية...")
    
    required_modules = {
        'sys': 'Python System',
        'os': 'Operating System', 
        'cv2': 'OpenCV',
        'numpy': 'NumPy',
        'PyQt5': 'PyQt5',
        'sqlite3': 'SQLite3'
    }
    
    missing_modules = []
    
    for module, description in required_modules.items():
        try:
            __import__(module)
            print(f"✅ {description}: متوفر")
        except ImportError:
            print(f"❌ {description}: مفقود")
            missing_modules.append(module)
        except Exception as e:
            print(f"⚠️ {description}: خطأ - {str(e)}")
            missing_modules.append(module)
    
    return missing_modules

def install_missing_modules(missing_modules):
    """تثبيت المكتبات المفقودة"""
    if not missing_modules:
        print("✅ جميع المكتبات متوفرة")
        return True
    
    print(f"\n🔄 تثبيت المكتبات المفقودة: {', '.join(missing_modules)}")
    
    try:
        # تحديث pip أولاً
        print("🔄 تحديث pip...")
        subprocess.run([sys.executable, '-m', 'pip', 'install', '--upgrade', 'pip'], 
                      check=True, timeout=60)
        
        # تثبيت المكتبات من requirements.txt
        if os.path.exists('requirements.txt'):
            print("📦 تثبيت من requirements.txt...")
            subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'], 
                          check=True, timeout=300)
        else:
            # تثبيت المكتبات الأساسية
            basic_packages = ['opencv-python', 'PyQt5', 'numpy']
            for package in basic_packages:
                print(f"📦 تثبيت {package}...")
                subprocess.run([sys.executable, '-m', 'pip', 'install', package], 
                              check=True, timeout=120)
        
        print("✅ تم تثبيت المكتبات بنجاح")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في تثبيت المكتبات: {e}")
        return False
    except subprocess.TimeoutExpired:
        print("⏰ انتهت مهلة التثبيت")
        return False
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
        return False

def test_local_imports():
    """اختبار استيراد الملفات المحلية"""
    print("\n📁 اختبار الملفات المحلية...")
    
    local_modules = [
        ('core.database', 'DatabaseManager'),
        ('core.user_manager', 'UserManager'),
        ('utils.config', 'ConfigManager'),
        ('utils.arabic_support', 'ArabicText')
    ]
    
    errors = []
    
    for module_path, class_name in local_modules:
        try:
            module = __import__(module_path, fromlist=[class_name])
            getattr(module, class_name)
            print(f"✅ {module_path}.{class_name}: متوفر")
        except ImportError as e:
            error_msg = f"❌ {module_path}: {str(e)}"
            print(error_msg)
            errors.append(error_msg)
        except AttributeError as e:
            error_msg = f"⚠️ {module_path}.{class_name}: {str(e)}"
            print(error_msg)
            errors.append(error_msg)
        except Exception as e:
            error_msg = f"❌ {module_path}: خطأ غير متوقع - {str(e)}"
            print(error_msg)
            errors.append(error_msg)
    
    return errors

def test_database_creation():
    """اختبار إنشاء قاعدة البيانات"""
    print("\n🗄️ اختبار قاعدة البيانات...")
    
    try:
        # حذف قاعدة البيانات القديمة إذا كانت موجودة
        if os.path.exists('database.db'):
            os.remove('database.db')
            print("🗑️ تم حذف قاعدة البيانات القديمة")
        
        # إنشاء قاعدة بيانات جديدة
        from core.database import DatabaseManager
        db = DatabaseManager()
        print("✅ تم إنشاء قاعدة البيانات بنجاح")
        
        # اختبار المصادقة
        result = db.authenticate_user('admin', 'admin123')
        if result:
            print("✅ اختبار المصادقة نجح")
            return True
        else:
            print("❌ اختبار المصادقة فشل")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {str(e)}")
        print(f"📋 تفاصيل الخطأ: {traceback.format_exc()}")
        return False

def test_ui_components():
    """اختبار مكونات الواجهة"""
    print("\n🖥️ اختبار مكونات الواجهة...")
    
    try:
        # اختبار PyQt5
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import QTimer
        
        # إنشاء تطبيق مؤقت
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        print("✅ PyQt5 Application: يعمل")
        
        # اختبار نافذة تسجيل الدخول
        try:
            from ui.login_window import LoginWindow
            print("✅ LoginWindow: متوفر")
        except Exception as e:
            print(f"❌ LoginWindow: {str(e)}")
            return False
        
        # اختبار النافذة الرئيسية
        try:
            from ui.main_window import MainWindow
            print("✅ MainWindow: متوفر")
        except Exception as e:
            print(f"❌ MainWindow: {str(e)}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في مكونات الواجهة: {str(e)}")
        return False

def create_safe_launcher():
    """إنشاء مشغل آمن"""
    print("\n🛡️ إنشاء مشغل آمن...")
    
    safe_launcher_content = '''#!/usr/bin/env python3
"""
مشغل آمن لنظام مراقبة الكاميرات
Safe Launcher for Camera Monitoring System
"""

import sys
import os
import traceback
from PyQt5.QtWidgets import QApplication, QMessageBox

def safe_main():
    """تشغيل آمن للنظام"""
    try:
        # إضافة المسار الحالي
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        
        # تشغيل نافذة تسجيل الدخول
        from ui.login_window import LoginWindow
        
        login_window = LoginWindow()
        
        if login_window.exec_() == LoginWindow.Accepted:
            # تشغيل النافذة الرئيسية
            from ui.main_window import MainWindow
            
            main_window = MainWindow()
            user_info = login_window.user_manager.get_current_user()
            if user_info:
                main_window.set_current_user(user_info)
            
            main_window.show()
            return app.exec_()
        else:
            return 0
            
    except ImportError as e:
        QMessageBox.critical(None, "خطأ في الاستيراد", 
                           f"فشل في استيراد المكتبات المطلوبة:\\n{str(e)}\\n\\n"
                           f"يرجى تشغيل: pip install -r requirements.txt")
        return 1
        
    except Exception as e:
        error_msg = f"خطأ غير متوقع:\\n{str(e)}\\n\\nتفاصيل الخطأ:\\n{traceback.format_exc()}"
        QMessageBox.critical(None, "خطأ في النظام", error_msg)
        return 1

if __name__ == "__main__":
    sys.exit(safe_main())
'''
    
    try:
        with open('safe_launcher.py', 'w', encoding='utf-8') as f:
            f.write(safe_launcher_content)
        
        print("✅ تم إنشاء المشغل الآمن: safe_launcher.py")
        return True
        
    except Exception as e:
        print(f"❌ فشل في إنشاء المشغل الآمن: {str(e)}")
        return False

def create_debug_launcher():
    """إنشاء مشغل التشخيص"""
    print("🔍 إنشاء مشغل التشخيص...")
    
    debug_launcher_content = '''#!/usr/bin/env python3
"""
مشغل التشخيص لنظام مراقبة الكاميرات
Debug Launcher for Camera Monitoring System
"""

import sys
import os
import traceback

def debug_main():
    """تشغيل مع تشخيص مفصل"""
    print("🔍 بدء التشخيص المفصل...")
    
    try:
        # إضافة المسار الحالي
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        print("📦 اختبار المكتبات...")
        
        # اختبار المكتبات الأساسية
        import cv2
        print(f"✅ OpenCV: {cv2.__version__}")
        
        from PyQt5.QtWidgets import QApplication
        print("✅ PyQt5: متوفر")
        
        import numpy as np
        print(f"✅ NumPy: {np.__version__}")
        
        print("📁 اختبار الملفات المحلية...")
        
        # اختبار الملفات المحلية
        from core.database import DatabaseManager
        print("✅ DatabaseManager")
        
        from ui.login_window import LoginWindow
        print("✅ LoginWindow")
        
        from ui.main_window import MainWindow
        print("✅ MainWindow")
        
        print("🗄️ اختبار قاعدة البيانات...")
        
        # اختبار قاعدة البيانات
        db = DatabaseManager()
        result = db.authenticate_user('admin', 'admin123')
        if result:
            print("✅ قاعدة البيانات تعمل")
        else:
            print("⚠️ مشكلة في المصادقة")
        
        print("🖥️ تشغيل الواجهة...")
        
        # تشغيل التطبيق
        app = QApplication(sys.argv)
        
        login_window = LoginWindow()
        login_window.show()
        
        return app.exec_()
        
    except Exception as e:
        print(f"❌ خطأ: {str(e)}")
        print(f"📋 تفاصيل الخطأ:")
        print(traceback.format_exc())
        input("اضغط Enter للخروج...")
        return 1

if __name__ == "__main__":
    sys.exit(debug_main())
'''
    
    try:
        with open('debug_launcher.py', 'w', encoding='utf-8') as f:
            f.write(debug_launcher_content)
        
        print("✅ تم إنشاء مشغل التشخيص: debug_launcher.py")
        return True
        
    except Exception as e:
        print(f"❌ فشل في إنشاء مشغل التشخيص: {str(e)}")
        return False

def main():
    """الوظيفة الرئيسية"""
    print_header()
    
    try:
        # الخطوة 1: اختبار المكتبات الأساسية
        missing_modules = test_basic_imports()
        
        # الخطوة 2: تثبيت المكتبات المفقودة
        if missing_modules:
            if not install_missing_modules(missing_modules):
                print("❌ فشل في تثبيت المكتبات. يرجى تثبيتها يدوياً.")
                return 1
            
            # إعادة اختبار بعد التثبيت
            missing_modules = test_basic_imports()
            if missing_modules:
                print(f"❌ لا تزال هناك مكتبات مفقودة: {missing_modules}")
                return 1
        
        # الخطوة 3: اختبار الملفات المحلية
        local_errors = test_local_imports()
        if local_errors:
            print("⚠️ هناك مشاكل في الملفات المحلية:")
            for error in local_errors:
                print(f"  {error}")
        
        # الخطوة 4: اختبار قاعدة البيانات
        if not test_database_creation():
            print("❌ مشكلة في قاعدة البيانات")
            return 1
        
        # الخطوة 5: اختبار مكونات الواجهة
        if not test_ui_components():
            print("❌ مشكلة في مكونات الواجهة")
            return 1
        
        # الخطوة 6: إنشاء المشغلات الآمنة
        create_safe_launcher()
        create_debug_launcher()
        
        print("\n" + "=" * 60)
        print("✅ تم إصلاح جميع المشاكل المكتشفة!")
        print("=" * 60)
        print()
        print("🚀 طرق تشغيل النظام:")
        print("1. التشغيل العادي: python main.py")
        print("2. التشغيل الآمن: python safe_launcher.py")
        print("3. التشغيل مع التشخيص: python debug_launcher.py")
        print()
        print("💡 إذا استمرت المشكلة، استخدم debug_launcher.py لمعرفة السبب")
        
        return 0
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف العملية بواسطة المستخدم")
        return 1
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {str(e)}")
        print(f"📋 تفاصيل الخطأ: {traceback.format_exc()}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    input(f"\nاضغط Enter للخروج... (Exit Code: {exit_code})")
    sys.exit(exit_code)
