#!/usr/bin/env python3
"""
Camera Monitoring System v2.0 - Advanced Edition
Main application entry point

Features:
- Multi-camera support (RTSP, IP, USB)
- Real-time video streaming with AI
- Advanced motion detection
- Video recording with cloud backup
- User management with roles
- Arabic language support
- Dark theme interface
- AI-powered face recognition
- Object detection and tracking
- Behavior analysis
- Smart notifications
- Cloud storage integration
- Advanced analytics and reporting
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMessageBox, QSplashScreen
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QPixmap, QFont

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ui.login_window import LoginWindow
from ui.main_window import MainWindow
from utils.config import ConfigManager
from core.database import DatabaseManager

# Import new AI and cloud services
try:
    from ai_engine.face_recognition_engine import FaceRecognitionEngine
    from ai_engine.object_detection_engine import ObjectDetectionEngine
    from ai_engine.behavior_analysis_engine import BehaviorAnalysisEngine
    from cloud_services.cloud_storage_manager import CloudStorageManager
    from notification_system.smart_notification_engine import SmartNotificationEngine
    AI_FEATURES_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ Advanced features not available: {e}")
    AI_FEATURES_AVAILABLE = False

class CameraMonitoringApp:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.config_manager = ConfigManager()
        self.db_manager = DatabaseManager()
        self.main_window = None
        self.login_window = None
        
        # Set application properties
        self.app.setApplicationName("Camera Monitoring System")
        self.app.setApplicationVersion("1.0")
        self.app.setOrganizationName("Camera Systems")
        
        # Set application font for Arabic support
        font = QFont("Arial", 10)
        self.app.setFont(font)
        
        # Apply global dark theme
        self.apply_global_theme()
    
    def apply_global_theme(self):
        """Apply global dark theme"""
        dark_stylesheet = """
            QApplication {
                background-color: #2c3e50;
                color: #ecf0f1;
            }
            
            QMessageBox {
                background-color: #2c3e50;
                color: #ecf0f1;
            }
            
            QMessageBox QPushButton {
                background-color: #3498db;
                border: none;
                border-radius: 5px;
                color: white;
                padding: 8px 16px;
                font-weight: bold;
                min-width: 80px;
            }
            
            QMessageBox QPushButton:hover {
                background-color: #5dade2;
            }
            
            QMessageBox QPushButton:pressed {
                background-color: #2980b9;
            }
        """
        self.app.setStyleSheet(dark_stylesheet)
    
    def show_splash_screen(self):
        """Show splash screen during startup"""
        # Create a simple splash screen
        splash_pixmap = QPixmap(400, 300)
        splash_pixmap.fill(Qt.darkBlue)
        
        splash = QSplashScreen(splash_pixmap)
        splash.setStyleSheet("""
            QSplashScreen {
                background-color: #2c3e50;
                color: #ecf0f1;
                font-size: 16px;
                font-weight: bold;
            }
        """)
        
        splash.show()
        splash.showMessage("Loading Camera Monitoring System...", Qt.AlignCenter | Qt.AlignBottom, Qt.white)
        
        # Process events to show splash
        self.app.processEvents()
        
        # Simulate loading time
        QTimer.singleShot(2000, splash.close)
        
        return splash
    
    def show_login(self):
        """Show login window"""
        self.login_window = LoginWindow()
        self.login_window.login_successful.connect(self.on_login_successful)
        
        # Show login window
        if self.login_window.exec_() == LoginWindow.Accepted:
            return True
        else:
            return False
    
    def on_login_successful(self, user_info):
        """Handle successful login"""
        # Create and show main window
        self.main_window = MainWindow()
        self.main_window.set_current_user(user_info)
        
        # Configure window size
        window_width = self.config_manager.get('app_settings.window_width', 1280)
        window_height = self.config_manager.get('app_settings.window_height', 800)
        self.main_window.resize(window_width, window_height)
        
        # Center window
        self.center_window(self.main_window)
        
        # Show window
        if self.config_manager.get('app_settings.fullscreen', False):
            self.main_window.showFullScreen()
        else:
            self.main_window.show()
    
    def center_window(self, window):
        """Center window on screen"""
        screen = self.app.desktop().screenGeometry()
        size = window.geometry()
        window.move(
            (screen.width() - size.width()) // 2,
            (screen.height() - size.height()) // 2
        )
    
    def check_system_requirements(self):
        """Check system requirements"""
        try:
            import cv2
            import numpy
            from PyQt5 import QtCore
            
            # Check OpenCV version
            cv_version = cv2.__version__
            if cv_version < "4.0":
                QMessageBox.warning(
                    None, "System Requirements",
                    f"OpenCV version {cv_version} detected.\n"
                    "OpenCV 4.0 or higher is recommended for better performance."
                )
            
            return True
            
        except ImportError as e:
            QMessageBox.critical(
                None, "System Requirements",
                f"Missing required dependency: {str(e)}\n\n"
                "Please install all required packages:\n"
                "pip install -r requirements.txt"
            )
            return False
    
    def initialize_database(self):
        """Initialize database"""
        try:
            self.db_manager.init_database()
            return True
        except Exception as e:
            QMessageBox.critical(
                None, "Database Error",
                f"Failed to initialize database: {str(e)}"
            )
            return False
    
    def create_directories(self):
        """Create necessary directories"""
        directories = [
            "recordings",
            "assets",
            "logs"
        ]
        
        for directory in directories:
            if not os.path.exists(directory):
                try:
                    os.makedirs(directory)
                except Exception as e:
                    QMessageBox.warning(
                        None, "Directory Creation",
                        f"Failed to create directory '{directory}': {str(e)}"
                    )
    
    def run(self):
        """Run the application"""
        try:
            # Show splash screen
            splash = self.show_splash_screen()
            
            # Check system requirements
            if not self.check_system_requirements():
                return 1
            
            # Initialize database
            if not self.initialize_database():
                return 1
            
            # Create necessary directories
            self.create_directories()
            
            # Close splash screen
            if splash:
                splash.close()
            
            # Show login window
            if not self.show_login():
                return 0  # User cancelled login
            
            # Run main application loop
            return self.app.exec_()
            
        except Exception as e:
            QMessageBox.critical(
                None, "Application Error",
                f"An unexpected error occurred: {str(e)}\n\n"
                "The application will now exit."
            )
            return 1
    
    def cleanup(self):
        """Cleanup resources"""
        if self.main_window:
            self.main_window.close()
        
        if self.login_window:
            self.login_window.close()


def main():
    """Main entry point"""
    # Set high DPI support
    QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    
    # Create and run application
    app = CameraMonitoringApp()
    exit_code = app.run()
    
    # Cleanup
    app.cleanup()
    
    return exit_code


if __name__ == "__main__":
    sys.exit(main())
