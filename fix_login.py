#!/usr/bin/env python3
"""
Fix login issue by resetting database and testing authentication
"""

import sys
import os
import hashlib

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.database import DatabaseManager

def reset_database():
    """Reset database and create admin user"""
    print("Resetting database...")
    
    # Remove existing database
    if os.path.exists("database.db"):
        os.remove("database.db")
        print("✓ Removed old database")
    
    # Create new database
    db = DatabaseManager()
    print("✓ Created new database with admin user")
    
    return db

def test_authentication():
    """Test authentication with default credentials"""
    print("\nTesting authentication...")
    
    db = DatabaseManager()
    
    # Test authentication
    result = db.authenticate_user("admin", "admin123")
    
    if result:
        user_id, role = result
        print(f"✓ Authentication successful!")
        print(f"  User ID: {user_id}")
        print(f"  Role: {role}")
        return True
    else:
        print("✗ Authentication failed!")
        return False

def verify_user_in_database():
    """Verify admin user exists in database"""
    print("\nVerifying user in database...")
    
    import sqlite3
    conn = sqlite3.connect("database.db")
    cursor = conn.cursor()
    
    cursor.execute("SELECT username, password, role FROM users WHERE username = 'admin'")
    result = cursor.fetchone()
    
    if result:
        username, password_hash, role = result
        print(f"✓ Admin user found:")
        print(f"  Username: {username}")
        print(f"  Password Hash: {password_hash[:20]}...")
        print(f"  Role: {role}")
        
        # Verify hash
        expected_hash = hashlib.sha256('admin123'.encode()).hexdigest()
        if password_hash == expected_hash:
            print("✓ Password hash is correct")
        else:
            print("✗ Password hash is incorrect")
            print(f"  Expected: {expected_hash[:20]}...")
            print(f"  Actual:   {password_hash[:20]}...")
    else:
        print("✗ Admin user not found!")
    
    conn.close()

def main():
    """Main fix process"""
    print("=" * 50)
    print("Camera System - Login Fix")
    print("=" * 50)
    
    try:
        # Reset database
        db = reset_database()
        
        # Verify user in database
        verify_user_in_database()
        
        # Test authentication
        if test_authentication():
            print("\n" + "=" * 50)
            print("✅ LOGIN FIX SUCCESSFUL!")
            print("=" * 50)
            print("You can now login with:")
            print("Username: admin")
            print("Password: admin123")
            print("=" * 50)
        else:
            print("\n" + "=" * 50)
            print("❌ LOGIN FIX FAILED!")
            print("=" * 50)
            
        return 0
        
    except Exception as e:
        print(f"\n❌ Error during fix: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
