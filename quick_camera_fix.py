#!/usr/bin/env python3
"""
إصلاح سريع لمشاكل الكاميرات
Quick Camera Fix Tool
"""

import sys
import os
import cv2
import socket
import subprocess
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.database import DatabaseManager

class QuickCameraFix(QDialog):
    def __init__(self):
        super().__init__()
        self.db_manager = DatabaseManager()
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة الإصلاح السريع"""
        self.setWindowTitle("🔧 إصلاح سريع لمشاكل الكاميرات")
        self.setFixedSize(600, 500)
        
        layout = QVBoxLayout()
        
        # العنوان
        title = QLabel("🔧 إصلاح سريع لمشاكل الكاميرات")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 18px; font-weight: bold; margin: 15px; color: #2c3e50;")
        layout.addWidget(title)
        
        # قسم المشاكل الشائعة
        problems_group = QGroupBox("المشاكل الشائعة وحلولها السريعة")
        problems_layout = QVBoxLayout()
        
        # أزرار الحلول السريعة
        fix_buttons = [
            ("🔄 إعادة تعيين إعدادات OpenCV", self.reset_opencv_settings),
            ("🌐 اختبار الاتصال بالشبكة", self.test_network_connection),
            ("📹 اختبار كاميرا USB", self.test_usb_camera),
            ("🔗 إنشاء روابط DVR تلقائياً", self.generate_dvr_urls),
            ("🗄️ إصلاح قاعدة بيانات الكاميرات", self.fix_camera_database),
            ("🧹 تنظيف ملفات التسجيل القديمة", self.cleanup_old_recordings),
            ("⚙️ فحص متطلبات النظام", self.check_system_requirements),
            ("📊 تشخيص شامل للنظام", self.full_system_diagnosis)
        ]
        
        for button_text, button_function in fix_buttons:
            btn = QPushButton(button_text)
            btn.clicked.connect(button_function)
            btn.setMinimumHeight(40)
            problems_layout.addWidget(btn)
        
        problems_group.setLayout(problems_layout)
        layout.addWidget(problems_group)
        
        # منطقة النتائج
        self.results_text = QTextEdit()
        self.results_text.setMaximumHeight(150)
        self.results_text.setPlaceholderText("نتائج الإصلاح ستظهر هنا...")
        layout.addWidget(self.results_text)
        
        # أزرار التحكم
        button_layout = QHBoxLayout()
        
        clear_btn = QPushButton("🧹 مسح النتائج")
        clear_btn.clicked.connect(self.results_text.clear)
        button_layout.addWidget(clear_btn)
        
        button_layout.addStretch()
        
        close_btn = QPushButton("❌ إغلاق")
        close_btn.clicked.connect(self.accept)
        button_layout.addWidget(close_btn)
        
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
        
        # تطبيق النمط
        self.apply_style()
    
    def log_result(self, message):
        """إضافة نتيجة إلى منطقة النتائج"""
        timestamp = QTime.currentTime().toString("hh:mm:ss")
        self.results_text.append(f"[{timestamp}] {message}")
        QApplication.processEvents()
    
    def reset_opencv_settings(self):
        """إعادة تعيين إعدادات OpenCV"""
        self.log_result("🔄 إعادة تعيين إعدادات OpenCV...")
        
        try:
            # اختبار OpenCV
            cv_version = cv2.__version__
            self.log_result(f"✅ إصدار OpenCV: {cv_version}")
            
            # اختبار إنشاء VideoCapture
            cap = cv2.VideoCapture()
            if cap is not None:
                self.log_result("✅ تم إنشاء VideoCapture بنجاح")
                cap.release()
            else:
                self.log_result("❌ فشل في إنشاء VideoCapture")
            
            # تنظيف ذاكرة OpenCV
            cv2.destroyAllWindows()
            self.log_result("✅ تم تنظيف ذاكرة OpenCV")
            
        except Exception as e:
            self.log_result(f"❌ خطأ في OpenCV: {str(e)}")
    
    def test_network_connection(self):
        """اختبار الاتصال بالشبكة"""
        self.log_result("🌐 اختبار الاتصال بالشبكة...")
        
        # اختبار عناوين شائعة للكاميرات
        test_ips = ["***********", "***********00", "************", "*******"]
        
        for ip in test_ips:
            try:
                # اختبار ping
                result = subprocess.run(['ping', '-n', '1', '-w', '1000', ip], 
                                      capture_output=True, text=True, timeout=5)
                
                if result.returncode == 0:
                    self.log_result(f"✅ الاتصال ناجح مع {ip}")
                else:
                    self.log_result(f"❌ فشل الاتصال مع {ip}")
                    
            except Exception as e:
                self.log_result(f"❌ خطأ في اختبار {ip}: {str(e)}")
    
    def test_usb_camera(self):
        """اختبار كاميرا USB"""
        self.log_result("📹 اختبار كاميرات USB...")
        
        found_cameras = []
        
        # اختبار أول 5 كاميرات USB
        for i in range(5):
            try:
                cap = cv2.VideoCapture(i)
                if cap.isOpened():
                    ret, frame = cap.read()
                    if ret and frame is not None:
                        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                        self.log_result(f"✅ كاميرا USB {i}: {width}x{height}")
                        found_cameras.append(i)
                    else:
                        self.log_result(f"⚠️ كاميرا USB {i}: متصلة لكن لا تعمل")
                cap.release()
                
            except Exception as e:
                self.log_result(f"❌ خطأ في كاميرا USB {i}: {str(e)}")
        
        if found_cameras:
            self.log_result(f"🎉 تم العثور على {len(found_cameras)} كاميرا USB تعمل")
        else:
            self.log_result("❌ لم يتم العثور على كاميرات USB")
    
    def generate_dvr_urls(self):
        """إنشاء روابط DVR تلقائياً"""
        self.log_result("🔗 إنشاء روابط DVR...")
        
        # طلب معلومات DVR من المستخدم
        dvr_ip, ok1 = QInputDialog.getText(self, "معلومات DVR", "أدخل عنوان IP للـ DVR:")
        if not ok1 or not dvr_ip:
            return
        
        username, ok2 = QInputDialog.getText(self, "معلومات DVR", "أدخل اسم المستخدم:")
        if not ok2 or not username:
            return
        
        password, ok3 = QInputDialog.getText(self, "معلومات DVR", "أدخل كلمة المرور:")
        if not ok3 or not password:
            return
        
        # إنشاء روابط للقنوات
        channels = ["101", "201", "301", "401", "501", "601", "701", "801"]
        base_url = f"rtsp://{username}:{password}@{dvr_ip}:554/Streaming/Channels/{{}}"
        
        self.log_result(f"📡 إنشاء روابط لـ DVR {dvr_ip}:")
        
        for i, channel in enumerate(channels, 1):
            url = base_url.format(channel)
            self.log_result(f"📹 كاميرا {i}: {url}")
            
            # إضافة الكاميرا إلى قاعدة البيانات
            try:
                camera_id = self.db_manager.add_camera(f"DVR Camera {i}", url, "rtsp")
                self.log_result(f"✅ تم إضافة الكاميرا {i} إلى قاعدة البيانات (ID: {camera_id})")
            except Exception as e:
                self.log_result(f"❌ فشل في إضافة الكاميرا {i}: {str(e)}")
    
    def fix_camera_database(self):
        """إصلاح قاعدة بيانات الكاميرات"""
        self.log_result("🗄️ فحص وإصلاح قاعدة بيانات الكاميرات...")
        
        try:
            # فحص الكاميرات الموجودة
            cameras = self.db_manager.get_cameras()
            self.log_result(f"📊 عدد الكاميرات في قاعدة البيانات: {len(cameras)}")
            
            # فحص كل كاميرا
            working_cameras = 0
            broken_cameras = 0
            
            for camera in cameras:
                camera_id, name, url, camera_type, pos_x, pos_y, enabled, created_at = camera
                
                if camera_type == "usb":
                    # اختبار كاميرا USB
                    try:
                        cap = cv2.VideoCapture(int(url))
                        if cap.isOpened():
                            ret, frame = cap.read()
                            if ret:
                                working_cameras += 1
                                self.log_result(f"✅ {name}: تعمل بشكل صحيح")
                            else:
                                broken_cameras += 1
                                self.log_result(f"❌ {name}: لا تستجيب")
                        else:
                            broken_cameras += 1
                            self.log_result(f"❌ {name}: فشل في الفتح")
                        cap.release()
                    except:
                        broken_cameras += 1
                        self.log_result(f"❌ {name}: خطأ في الاختبار")
                
                else:
                    # اختبار كاميرات الشبكة (اختبار سريع)
                    if "192.168." in url or "10." in url or "172." in url:
                        # استخراج IP من الرابط
                        try:
                            ip = url.split("@")[1].split(":")[0] if "@" in url else url.split("//")[1].split(":")[0]
                            
                            # اختبار ping سريع
                            result = subprocess.run(['ping', '-n', '1', '-w', '500', ip], 
                                                  capture_output=True, timeout=2)
                            if result.returncode == 0:
                                working_cameras += 1
                                self.log_result(f"✅ {name}: الشبكة متاحة")
                            else:
                                broken_cameras += 1
                                self.log_result(f"❌ {name}: الشبكة غير متاحة")
                        except:
                            broken_cameras += 1
                            self.log_result(f"⚠️ {name}: لا يمكن اختبار الشبكة")
                    else:
                        self.log_result(f"ℹ️ {name}: تم تخطي الاختبار")
            
            self.log_result(f"📈 النتيجة النهائية: {working_cameras} تعمل، {broken_cameras} لا تعمل")
            
        except Exception as e:
            self.log_result(f"❌ خطأ في فحص قاعدة البيانات: {str(e)}")
    
    def cleanup_old_recordings(self):
        """تنظيف ملفات التسجيل القديمة"""
        self.log_result("🧹 تنظيف ملفات التسجيل القديمة...")
        
        recordings_dir = "recordings"
        if not os.path.exists(recordings_dir):
            self.log_result("ℹ️ مجلد التسجيلات غير موجود")
            return
        
        try:
            files = os.listdir(recordings_dir)
            total_size = 0
            file_count = 0
            
            for file in files:
                file_path = os.path.join(recordings_dir, file)
                if os.path.isfile(file_path):
                    size = os.path.getsize(file_path)
                    total_size += size
                    file_count += 1
            
            # تحويل الحجم إلى MB
            total_size_mb = total_size / (1024 * 1024)
            
            self.log_result(f"📊 إجمالي الملفات: {file_count}")
            self.log_result(f"📊 إجمالي الحجم: {total_size_mb:.2f} MB")
            
            if total_size_mb > 1000:  # أكثر من 1 GB
                reply = QMessageBox.question(self, "تنظيف الملفات", 
                                           f"حجم ملفات التسجيل {total_size_mb:.2f} MB\n"
                                           "هل تريد حذف الملفات الأقدم من 30 يوم؟")
                
                if reply == QMessageBox.Yes:
                    # حذف الملفات القديمة (أكثر من 30 يوم)
                    import time
                    current_time = time.time()
                    deleted_count = 0
                    
                    for file in files:
                        file_path = os.path.join(recordings_dir, file)
                        if os.path.isfile(file_path):
                            file_age = current_time - os.path.getctime(file_path)
                            if file_age > (30 * 24 * 60 * 60):  # 30 يوم
                                os.remove(file_path)
                                deleted_count += 1
                    
                    self.log_result(f"✅ تم حذف {deleted_count} ملف قديم")
            else:
                self.log_result("✅ حجم الملفات مقبول، لا حاجة للتنظيف")
                
        except Exception as e:
            self.log_result(f"❌ خطأ في تنظيف الملفات: {str(e)}")
    
    def check_system_requirements(self):
        """فحص متطلبات النظام"""
        self.log_result("⚙️ فحص متطلبات النظام...")
        
        try:
            # فحص Python
            python_version = sys.version.split()[0]
            self.log_result(f"🐍 إصدار Python: {python_version}")
            
            # فحص المكتبات المطلوبة
            required_modules = {
                'cv2': 'OpenCV',
                'PyQt5': 'PyQt5',
                'numpy': 'NumPy',
                'sqlite3': 'SQLite3'
            }
            
            for module, name in required_modules.items():
                try:
                    __import__(module)
                    if module == 'cv2':
                        version = cv2.__version__
                        self.log_result(f"✅ {name}: {version}")
                    else:
                        self.log_result(f"✅ {name}: متوفر")
                except ImportError:
                    self.log_result(f"❌ {name}: غير متوفر")
            
            # فحص مساحة القرص
            import shutil
            total, used, free = shutil.disk_usage(".")
            free_gb = free / (1024**3)
            self.log_result(f"💾 مساحة القرص المتاحة: {free_gb:.2f} GB")
            
            if free_gb < 1:
                self.log_result("⚠️ تحذير: مساحة القرص منخفضة")
            
        except Exception as e:
            self.log_result(f"❌ خطأ في فحص النظام: {str(e)}")
    
    def full_system_diagnosis(self):
        """تشخيص شامل للنظام"""
        self.log_result("🔍 بدء التشخيص الشامل للنظام...")
        
        # تشغيل جميع الفحوصات
        self.check_system_requirements()
        self.reset_opencv_settings()
        self.test_usb_camera()
        self.fix_camera_database()
        
        self.log_result("✅ انتهى التشخيص الشامل")
    
    def apply_style(self):
        """تطبيق نمط الواجهة"""
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #2c3e50;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 15px;
                border-radius: 5px;
                font-weight: bold;
                text-align: left;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
            QTextEdit {
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                background-color: white;
                font-family: 'Courier New', monospace;
                font-size: 11px;
            }
        """)


def main():
    """تشغيل أداة الإصلاح السريع"""
    app = QApplication(sys.argv)
    
    # تعيين خط يدعم العربية
    font = QFont("Arial", 10)
    app.setFont(font)
    
    # إنشاء النافذة
    window = QuickCameraFix()
    window.exec_()
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
