import sys
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
import cv2
import numpy as np
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.arabic_support import ArabicText
from utils.config import ConfigManager
from core.database import DatabaseManager
from core.camera_handler import CameraHandler
from core.user_manager import UserManager
from .camera_manager import CameraManagerDialog

class CameraWidget(QLabel):
    """Custom widget for displaying camera feed"""
    
    def __init__(self, camera_id=None, camera_name="Camera"):
        super().__init__()
        self.camera_id = camera_id
        self.camera_name = camera_name
        self.camera_handler = None
        self.is_recording = False
        self.motion_detected = False
        
        self.setMinimumSize(320, 240)
        self.setStyleSheet("""
            QLabel {
                border: 2px solid #5d6d7e;
                border-radius: 8px;
                background-color: #34495e;
                color: #ecf0f1;
            }
        """)
        self.setAlignment(Qt.AlignCenter)
        self.setText(f"{camera_name}\nNo Signal")
        
        # Context menu
        self.setContextMenuPolicy(Qt.CustomContextMenu)
        self.customContextMenuRequested.connect(self.show_context_menu)
        
        # Timer for updating display
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_display)
        self.update_timer.start(33)  # ~30 FPS
    
    def set_camera_handler(self, camera_handler):
        """Set camera handler for this widget"""
        self.camera_handler = camera_handler
        if camera_handler:
            self.camera_name = camera_handler.name
            camera_handler.start_streaming()
    
    def update_display(self):
        """Update camera display"""
        if not self.camera_handler:
            return
        
        frame = self.camera_handler.get_current_frame()
        if frame is not None:
            # Convert frame to QPixmap
            height, width, channel = frame.shape
            bytes_per_line = 3 * width
            q_image = QImage(frame.data, width, height, bytes_per_line, QImage.Format_RGB888).rgbSwapped()
            
            # Scale to fit widget
            pixmap = QPixmap.fromImage(q_image)
            scaled_pixmap = pixmap.scaled(self.size(), Qt.KeepAspectRatio, Qt.SmoothTransformation)
            self.setPixmap(scaled_pixmap)
            
            # Update status indicators
            status = self.camera_handler.get_status()
            self.is_recording = status['recording']
            self.motion_detected = status['motion_detected']
            
        else:
            # No frame available
            self.setText(f"{self.camera_name}\n{ArabicText.get('connecting')}")
    
    def show_context_menu(self, position):
        """Show context menu"""
        if not self.camera_handler:
            return
        
        menu = QMenu(self)
        
        # Recording actions
        if self.is_recording:
            stop_action = menu.addAction(ArabicText.get('stop_recording'))
            stop_action.triggered.connect(self.stop_recording)
        else:
            start_action = menu.addAction(ArabicText.get('start_recording'))
            start_action.triggered.connect(self.start_recording)
        
        menu.addSeparator()
        
        # Motion detection
        motion_action = menu.addAction(ArabicText.get('motion_detection'))
        motion_action.setCheckable(True)
        motion_action.setChecked(self.camera_handler.motion_enabled)
        motion_action.triggered.connect(self.toggle_motion_detection)
        
        # Snapshot
        snapshot_action = menu.addAction(ArabicText.get('snapshot'))
        snapshot_action.triggered.connect(self.take_snapshot)
        
        menu.exec_(self.mapToGlobal(position))
    
    def start_recording(self):
        """Start recording"""
        if self.camera_handler:
            success, message = self.camera_handler.start_recording()
            if not success:
                QMessageBox.warning(self, "Recording Error", message)
    
    def stop_recording(self):
        """Stop recording"""
        if self.camera_handler:
            success, info = self.camera_handler.stop_recording()
            if success:
                QMessageBox.information(self, "Recording Stopped", f"Recording saved: {info['filename']}")
    
    def toggle_motion_detection(self):
        """Toggle motion detection"""
        if self.camera_handler:
            current_state = self.camera_handler.motion_enabled
            self.camera_handler.enable_motion_detection(not current_state)
    
    def take_snapshot(self):
        """Take snapshot"""
        if self.camera_handler:
            frame = self.camera_handler.get_current_frame()
            if frame is not None:
                filename = f"snapshot_{self.camera_name}_{QDateTime.currentDateTime().toString('yyyyMMdd_hhmmss')}.jpg"
                filepath = os.path.join("recordings", filename)
                cv2.imwrite(filepath, frame)
                QMessageBox.information(self, "Snapshot", f"Snapshot saved: {filename}")
    
    def paintEvent(self, event):
        """Custom paint event to add status indicators"""
        super().paintEvent(event)
        
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # Recording indicator
        if self.is_recording:
            painter.setBrush(QBrush(QColor(231, 76, 60)))  # Red
            painter.setPen(Qt.NoPen)
            painter.drawEllipse(self.width() - 25, 10, 15, 15)
            
            painter.setPen(QPen(QColor(255, 255, 255)))
            painter.setFont(QFont("Arial", 8, QFont.Bold))
            painter.drawText(self.width() - 35, 35, "REC")
        
        # Motion indicator
        if self.motion_detected:
            painter.setBrush(QBrush(QColor(46, 204, 113)))  # Green
            painter.setPen(Qt.NoPen)
            painter.drawEllipse(10, 10, 15, 15)
            
            painter.setPen(QPen(QColor(255, 255, 255)))
            painter.setFont(QFont("Arial", 8, QFont.Bold))
            painter.drawText(10, 35, "MOT")
        
        # Camera name overlay
        painter.setPen(QPen(QColor(255, 255, 255)))
        painter.setFont(QFont("Arial", 10, QFont.Bold))
        painter.drawText(10, self.height() - 10, self.camera_name)


class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.config_manager = ConfigManager()
        self.db_manager = DatabaseManager()
        self.user_manager = UserManager()
        self.camera_handlers = {}
        self.camera_widgets = []
        self.current_user = None
        self.is_fullscreen = False
        
        self.setup_ui()
        self.apply_dark_theme()
        self.load_cameras()
        
        # Status update timer
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_status)
        self.status_timer.start(1000)  # Update every second

    def setup_ui(self):
        """Setup main user interface"""
        # Window properties
        self.setWindowTitle(ArabicText.get('app_title'))
        self.setMinimumSize(1280, 800)

        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Main layout
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # Toolbar
        toolbar_layout = QHBoxLayout()

        # User info
        self.user_label = QLabel("Not logged in")
        self.user_label.setStyleSheet("color: #ecf0f1; font-weight: bold;")
        toolbar_layout.addWidget(self.user_label)

        toolbar_layout.addStretch()

        # Control buttons
        self.cameras_button = QPushButton(ArabicText.get('cameras'))
        self.cameras_button.clicked.connect(self.show_camera_manager)
        toolbar_layout.addWidget(self.cameras_button)

        self.recordings_button = QPushButton(ArabicText.get('recordings'))
        self.recordings_button.clicked.connect(self.show_recordings)
        toolbar_layout.addWidget(self.recordings_button)

        self.settings_button = QPushButton(ArabicText.get('settings'))
        self.settings_button.clicked.connect(self.show_settings)
        toolbar_layout.addWidget(self.settings_button)

        self.fullscreen_button = QPushButton(ArabicText.get('fullscreen'))
        self.fullscreen_button.clicked.connect(self.toggle_fullscreen)
        toolbar_layout.addWidget(self.fullscreen_button)

        self.logout_button = QPushButton(ArabicText.get('logout'))
        self.logout_button.clicked.connect(self.logout)
        toolbar_layout.addWidget(self.logout_button)

        main_layout.addLayout(toolbar_layout)

        # Camera grid
        self.camera_scroll = QScrollArea()
        self.camera_scroll.setWidgetResizable(True)
        self.camera_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.camera_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        self.camera_container = QWidget()
        self.camera_grid_layout = QGridLayout()
        self.camera_grid_layout.setSpacing(10)
        self.camera_container.setLayout(self.camera_grid_layout)
        self.camera_scroll.setWidget(self.camera_container)

        main_layout.addWidget(self.camera_scroll)

        # Status bar
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)

        self.status_label = QLabel("Ready")
        self.status_bar.addWidget(self.status_label)

        self.connection_label = QLabel("Cameras: 0/0")
        self.status_bar.addPermanentWidget(self.connection_label)

        self.recording_label = QLabel("Recording: 0")
        self.status_bar.addPermanentWidget(self.recording_label)

        central_widget.setLayout(main_layout)

        # Create menu bar
        self.create_menu_bar()

    def create_menu_bar(self):
        """Create application menu bar"""
        menubar = self.menuBar()

        # File menu
        file_menu = menubar.addMenu(ArabicText.get('file'))

        exit_action = QAction('Exit', self)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # View menu
        view_menu = menubar.addMenu(ArabicText.get('view'))

        fullscreen_action = QAction(ArabicText.get('fullscreen'), self)
        fullscreen_action.setShortcut('F11')
        fullscreen_action.triggered.connect(self.toggle_fullscreen)
        view_menu.addAction(fullscreen_action)

        # Tools menu
        tools_menu = menubar.addMenu(ArabicText.get('tools'))

        cameras_action = QAction(ArabicText.get('cameras'), self)
        cameras_action.setShortcut('Ctrl+C')
        cameras_action.triggered.connect(self.show_camera_manager)
        tools_menu.addAction(cameras_action)

        recordings_action = QAction(ArabicText.get('recordings'), self)
        recordings_action.setShortcut('Ctrl+R')
        recordings_action.triggered.connect(self.show_recordings)
        tools_menu.addAction(recordings_action)

        settings_action = QAction(ArabicText.get('settings'), self)
        settings_action.setShortcut('Ctrl+S')
        settings_action.triggered.connect(self.show_settings)
        tools_menu.addAction(settings_action)

        # Help menu
        help_menu = menubar.addMenu(ArabicText.get('help'))

        about_action = QAction(ArabicText.get('about'), self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def apply_dark_theme(self):
        """Apply dark theme to main window"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #2c3e50;
                color: #ecf0f1;
            }

            QWidget {
                background-color: #2c3e50;
                color: #ecf0f1;
            }

            QPushButton {
                background-color: #3498db;
                border: none;
                border-radius: 6px;
                color: white;
                padding: 8px 16px;
                font-weight: bold;
                font-size: 12px;
            }

            QPushButton:hover {
                background-color: #5dade2;
            }

            QPushButton:pressed {
                background-color: #2980b9;
            }

            QScrollArea {
                border: 1px solid #5d6d7e;
                border-radius: 5px;
                background-color: #34495e;
            }

            QStatusBar {
                background-color: #34495e;
                border-top: 1px solid #5d6d7e;
                color: #ecf0f1;
            }

            QMenuBar {
                background-color: #34495e;
                color: #ecf0f1;
                border-bottom: 1px solid #5d6d7e;
            }

            QMenuBar::item {
                background-color: transparent;
                padding: 4px 8px;
            }

            QMenuBar::item:selected {
                background-color: #3498db;
            }

            QMenu {
                background-color: #34495e;
                color: #ecf0f1;
                border: 1px solid #5d6d7e;
            }

            QMenu::item {
                padding: 6px 20px;
            }

            QMenu::item:selected {
                background-color: #3498db;
            }
        """)

    def set_current_user(self, user_info):
        """Set current logged in user"""
        self.current_user = user_info
        self.user_label.setText(f"Welcome, {user_info['username']} ({user_info['role']})")

        # Update UI based on user permissions
        self.update_ui_permissions()

    def update_ui_permissions(self):
        """Update UI based on user permissions"""
        if not self.current_user:
            return

        is_admin = self.current_user.get('role') == 'admin'
        is_operator = self.current_user.get('role') in ['admin', 'operator']

        # Enable/disable buttons based on permissions
        self.cameras_button.setEnabled(is_operator)
        self.settings_button.setEnabled(is_operator)

    def load_cameras(self):
        """Load cameras from database and create widgets"""
        try:
            # Clear existing widgets
            for widget in self.camera_widgets:
                widget.setParent(None)
            self.camera_widgets.clear()

            # Stop existing camera handlers
            for handler in self.camera_handlers.values():
                handler.stop_streaming()
                handler.disconnect()
            self.camera_handlers.clear()

            # Load cameras from database
            cameras = self.db_manager.get_cameras()

            if not cameras:
                # Show no cameras message
                no_cameras_label = QLabel(ArabicText.get('no_cameras_found'))
                no_cameras_label.setAlignment(Qt.AlignCenter)
                no_cameras_label.setStyleSheet("color: #95a5a6; font-size: 16px;")
                self.camera_grid_layout.addWidget(no_cameras_label, 0, 0)
                return

            # Calculate grid layout
            camera_count = len(cameras)
            grid_rows, grid_cols = self.config_manager.get_camera_grid_size(camera_count)

            # Create camera widgets
            for i, camera in enumerate(cameras):
                camera_id, name, url, camera_type, pos_x, pos_y, enabled, created_at = camera

                # Create camera widget
                camera_widget = CameraWidget(camera_id, name)

                # Create camera handler
                camera_handler = CameraHandler(camera_id, name, url, camera_type)
                camera_handler.set_motion_callback(self.on_motion_detected)
                camera_handler.set_auto_record_on_motion(
                    self.config_manager.get('app_settings.auto_record', True)
                )

                # Set handler to widget
                camera_widget.set_camera_handler(camera_handler)

                # Add to grid
                row = i // grid_cols
                col = i % grid_cols
                self.camera_grid_layout.addWidget(camera_widget, row, col)

                # Store references
                self.camera_widgets.append(camera_widget)
                self.camera_handlers[camera_id] = camera_handler

            self.status_label.setText(f"Loaded {len(cameras)} cameras")

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to load cameras: {str(e)}")

    def on_motion_detected(self, camera_id, motion_detected):
        """Handle motion detection event"""
        if motion_detected:
            # Log motion event
            self.db_manager.add_motion_event(camera_id, 75)  # 75% confidence

            # Update status
            camera_handler = self.camera_handlers.get(camera_id)
            if camera_handler:
                self.status_label.setText(f"Motion detected: {camera_handler.name}")

    def show_camera_manager(self):
        """Show camera management dialog"""
        dialog = CameraManagerDialog(self)
        dialog.cameras_updated.connect(self.load_cameras)
        dialog.exec_()

    def show_recordings(self):
        """Show recordings dialog"""
        # This would open a recordings viewer dialog
        QMessageBox.information(self, "Recordings", "Recordings viewer not implemented yet")

    def show_settings(self):
        """Show settings dialog"""
        # This would open a settings dialog
        QMessageBox.information(self, "Settings", "Settings dialog not implemented yet")

    def show_about(self):
        """Show about dialog"""
        QMessageBox.about(self, ArabicText.get('about'),
                         f"{ArabicText.get('app_title')}\n\n"
                         "Python Camera Monitoring System\n"
                         "Version 1.0\n\n"
                         "Features:\n"
                         "• Multi-camera support (RTSP, IP, USB)\n"
                         "• Motion detection\n"
                         "• Video recording\n"
                         "• User management\n"
                         "• Arabic language support")

    def toggle_fullscreen(self):
        """Toggle fullscreen mode"""
        if self.is_fullscreen:
            self.showNormal()
            self.fullscreen_button.setText(ArabicText.get('fullscreen'))
            self.is_fullscreen = False
        else:
            self.showFullScreen()
            self.fullscreen_button.setText(ArabicText.get('exit_fullscreen'))
            self.is_fullscreen = True

    def logout(self):
        """Logout current user"""
        reply = QMessageBox.question(
            self, "Logout",
            "Are you sure you want to logout?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # Stop all camera streams
            for handler in self.camera_handlers.values():
                handler.stop_streaming()
                handler.disconnect()

            self.user_manager.logout()
            self.close()

    def update_status(self):
        """Update status bar information"""
        if not self.camera_handlers:
            return

        connected_count = sum(1 for h in self.camera_handlers.values() if h.is_connected)
        total_count = len(self.camera_handlers)
        recording_count = sum(1 for h in self.camera_handlers.values() if h.recorder.is_recording)

        self.connection_label.setText(f"Cameras: {connected_count}/{total_count}")
        self.recording_label.setText(f"Recording: {recording_count}")

    def closeEvent(self, event):
        """Handle window close event"""
        # Stop all camera streams
        for handler in self.camera_handlers.values():
            handler.stop_streaming()
            handler.disconnect()

        event.accept()

    def keyPressEvent(self, event):
        """Handle key press events"""
        if event.key() == Qt.Key_F11:
            self.toggle_fullscreen()
        elif event.key() == Qt.Key_Escape and self.is_fullscreen:
            self.toggle_fullscreen()
        else:
            super().keyPressEvent(event)
