#!/usr/bin/env python3
"""
حل فوري لمشكلة إغلاق النظام عند إضافة كاميرا
Instant Fix for Camera Addition Crash
"""

import sys
import os
import sqlite3
import cv2
import time
import traceback
from datetime import datetime

def print_instant_fix_banner():
    """طباعة شعار الحل الفوري"""
    print("=" * 50)
    print("⚡ الحل الفوري لمشكلة إضافة الكاميرا")
    print("⚡ Instant Camera Addition Fix")
    print("=" * 50)
    print()

def emergency_database_fix():
    """إصلاح طارئ لقاعدة البيانات"""
    print("🚨 إصلاح طارئ لقاعدة البيانات...")
    
    try:
        # حذف قاعدة البيانات التالفة إذا وجدت
        if os.path.exists('database.db'):
            backup_name = f'database_broken_{int(time.time())}.db'
            os.rename('database.db', backup_name)
            print(f"📋 تم حفظ قاعدة البيانات التالفة: {backup_name}")
        
        # إنشاء قاعدة بيانات جديدة
        conn = sqlite3.connect('database.db')
        cursor = conn.cursor()
        
        # جدول المستخدمين
        cursor.execute('''
            CREATE TABLE users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password TEXT NOT NULL,
                role TEXT DEFAULT 'user',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول الكاميرات المبسط
        cursor.execute('''
            CREATE TABLE cameras (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                rtsp_url TEXT NOT NULL,
                username TEXT DEFAULT '',
                password TEXT DEFAULT '',
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول التسجيلات
        cursor.execute('''
            CREATE TABLE recordings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                camera_id INTEGER,
                filename TEXT NOT NULL,
                start_time TIMESTAMP,
                end_time TIMESTAMP,
                file_size INTEGER,
                FOREIGN KEY (camera_id) REFERENCES cameras (id)
            )
        ''')
        
        # إضافة مستخدم افتراضي
        cursor.execute('''
            INSERT INTO users (username, password, role)
            VALUES ('admin', 'admin123', 'admin')
        ''')
        
        conn.commit()
        conn.close()
        
        print("✅ تم إنشاء قاعدة بيانات جديدة")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الإصلاح الطارئ: {e}")
        return False

def safe_add_camera_no_test(name, rtsp_url, username="", password=""):
    """إضافة كاميرا بدون اختبار (آمن)"""
    try:
        if not name or not rtsp_url:
            return False, "اسم الكاميرا ورابط RTSP مطلوبان"
        
        conn = sqlite3.connect('database.db', timeout=5)
        cursor = conn.cursor()
        
        # التحقق من التكرار
        cursor.execute("SELECT id FROM cameras WHERE name = ?", (name,))
        if cursor.fetchone():
            conn.close()
            return False, "اسم الكاميرا موجود"
        
        # الإضافة بدون اختبار
        cursor.execute('''
            INSERT INTO cameras (name, rtsp_url, username, password, is_active, created_at)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (name, rtsp_url, username, password, 1, datetime.now()))
        
        camera_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        return True, f"تم إضافة الكاميرا (ID: {camera_id})"
        
    except Exception as e:
        return False, f"خطأ: {str(e)}"

def instant_add_xvr_cameras():
    """إضافة فورية لكاميرات XVR"""
    print("📹 إضافة فورية لكاميرات XVR...")
    
    xvr_settings = {
        'ip': '**************',
        'username': 'admin',
        'password': 'Mnbv@1978'
    }
    
    added = 0
    
    for channel in range(1, 9):
        name = f"XVR قناة {channel}"
        rtsp_url = f"rtsp://{xvr_settings['username']}:{xvr_settings['password']}@{xvr_settings['ip']}:554/cam/realmonitor?channel={channel}&subtype=0"
        
        success, message = safe_add_camera_no_test(
            name, rtsp_url, 
            xvr_settings['username'], 
            xvr_settings['password']
        )
        
        if success:
            print(f"✅ {name}")
            added += 1
        else:
            print(f"❌ {name}: {message}")
    
    print(f"📊 تم إضافة {added}/8 كاميرات")
    return added > 0

def create_no_crash_camera_tool():
    """إنشاء أداة إضافة كاميرات بدون crash"""
    tool_content = '''#!/usr/bin/env python3
"""
أداة إضافة كاميرات بدون crash
No-Crash Camera Addition Tool
"""

import sqlite3
import sys
from datetime import datetime

def add_camera_safe(name, rtsp_url, username="", password=""):
    """إضافة كاميرا آمنة بدون اختبار"""
    try:
        if not name or not rtsp_url:
            return False, "البيانات ناقصة"
        
        conn = sqlite3.connect('database.db', timeout=5)
        cursor = conn.cursor()
        
        # فحص التكرار
        cursor.execute("SELECT id FROM cameras WHERE name = ?", (name,))
        if cursor.fetchone():
            conn.close()
            return False, "الاسم موجود"
        
        # الإضافة
        cursor.execute('''
            INSERT INTO cameras (name, rtsp_url, username, password, is_active, created_at)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (name, rtsp_url, username, password, 1, datetime.now()))
        
        camera_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        return True, f"تمت الإضافة (ID: {camera_id})"
        
    except Exception as e:
        return False, f"خطأ: {str(e)}"

def list_cameras():
    """عرض الكاميرات"""
    try:
        conn = sqlite3.connect('database.db')
        cursor = conn.cursor()
        
        cursor.execute("SELECT id, name, rtsp_url FROM cameras ORDER BY id")
        cameras = cursor.fetchall()
        
        conn.close()
        
        if not cameras:
            print("لا توجد كاميرات")
            return
        
        print("\\n📹 الكاميرات المضافة:")
        for camera_id, name, rtsp_url in cameras:
            print(f"{camera_id}. {name}")
            print(f"   {rtsp_url}")
        
    except Exception as e:
        print(f"خطأ: {e}")

def main():
    while True:
        print("\\n" + "=" * 40)
        print("📹 أداة إضافة الكاميرات الآمنة")
        print("=" * 40)
        print("1. إضافة كاميرا واحدة")
        print("2. إضافة كاميرات XVR")
        print("3. عرض الكاميرات")
        print("4. خروج")
        
        choice = input("\\nالاختيار (1-4): ").strip()
        
        if choice == "1":
            name = input("اسم الكاميرا: ").strip()
            if not name:
                print("❌ الاسم مطلوب")
                continue
            
            rtsp_url = input("رابط RTSP: ").strip()
            if not rtsp_url:
                print("❌ الرابط مطلوب")
                continue
            
            username = input("المستخدم (اختياري): ").strip()
            password = input("كلمة المرور (اختياري): ").strip()
            
            success, message = add_camera_safe(name, rtsp_url, username, password)
            print(f"{'✅' if success else '❌'} {message}")
        
        elif choice == "2":
            ip = input("IP الجهاز (افتراضي: **************): ").strip()
            if not ip:
                ip = "**************"
            
            username = input("المستخدم (افتراضي: admin): ").strip()
            if not username:
                username = "admin"
            
            password = input("كلمة المرور (افتراضي: Mnbv@1978): ").strip()
            if not password:
                password = "Mnbv@1978"
            
            added = 0
            for channel in range(1, 9):
                name = f"XVR قناة {channel}"
                rtsp_url = f"rtsp://{username}:{password}@{ip}:554/cam/realmonitor?channel={channel}&subtype=0"
                
                success, message = add_camera_safe(name, rtsp_url, username, password)
                if success:
                    print(f"✅ {name}")
                    added += 1
                else:
                    print(f"❌ {name}: {message}")
            
            print(f"\\n📊 تم إضافة {added}/8 كاميرات")
        
        elif choice == "3":
            list_cameras()
        
        elif choice == "4":
            print("👋 وداعاً")
            break
        
        else:
            print("❌ خيار غير صحيح")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\\n⏹️ تم الإيقاف")
    except Exception as e:
        print(f"\\n❌ خطأ: {e}")
'''
    
    try:
        with open('no_crash_camera_tool.py', 'w', encoding='utf-8') as f:
            f.write(tool_content)
        print("✅ تم إنشاء أداة بدون crash")
        return True
    except Exception as e:
        print(f"❌ فشل الإنشاء: {e}")
        return False

def patch_main_py():
    """تصحيح ملف main.py لمنع الcrash"""
    print("🔧 تصحيح ملف main.py...")
    
    patch_code = '''
# إضافة معالج أخطاء شامل
import sys
import traceback

def safe_exception_handler(exc_type, exc_value, exc_traceback):
    """معالج آمن للأخطاء"""
    if issubclass(exc_type, KeyboardInterrupt):
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return
    
    print("\\n❌ حدث خطأ في النظام:")
    print(f"النوع: {exc_type.__name__}")
    print(f"الرسالة: {str(exc_value)}")
    print("\\n💡 النظام سيستمر في العمل...")
    
    # كتابة الخطأ في ملف log
    try:
        with open('error_log.txt', 'a', encoding='utf-8') as f:
            f.write(f"\\n{datetime.now()}: {exc_type.__name__}: {str(exc_value)}\\n")
    except:
        pass

# تطبيق المعالج
sys.excepthook = safe_exception_handler
'''
    
    try:
        # قراءة main.py إذا وجد
        if os.path.exists('main.py'):
            with open('main.py', 'r', encoding='utf-8') as f:
                content = f.read()
            
            # إضافة المعالج في البداية
            if 'safe_exception_handler' not in content:
                # إضافة import datetime إذا لم يكن موجود
                if 'from datetime import datetime' not in content:
                    patch_code = 'from datetime import datetime\n' + patch_code
                
                # إضافة المعالج بعد الimports
                lines = content.split('\n')
                import_end = 0
                for i, line in enumerate(lines):
                    if line.strip().startswith('import ') or line.strip().startswith('from '):
                        import_end = i + 1
                
                lines.insert(import_end, patch_code)
                
                # حفظ الملف المحدث
                with open('main.py', 'w', encoding='utf-8') as f:
                    f.write('\n'.join(lines))
                
                print("✅ تم تصحيح main.py")
                return True
        
        print("⚠️ ملف main.py غير موجود")
        return False
        
    except Exception as e:
        print(f"❌ فشل التصحيح: {e}")
        return False

def create_crash_free_launcher():
    """إنشاء مشغل خالي من الcrash"""
    launcher_content = '''#!/usr/bin/env python3
"""
مشغل خالي من الcrash
Crash-Free Launcher
"""

import sys
import os
import traceback
from datetime import datetime

def safe_main():
    """تشغيل آمن للنظام"""
    try:
        print("🚀 بدء التشغيل الآمن...")
        
        # فحص الملفات المطلوبة
        required_files = ['database.db', 'main.py']
        missing_files = [f for f in required_files if not os.path.exists(f)]
        
        if missing_files:
            print(f"❌ ملفات مفقودة: {', '.join(missing_files)}")
            
            if 'database.db' in missing_files:
                print("🔧 إنشاء قاعدة بيانات...")
                create_emergency_database()
            
            if 'main.py' in missing_files:
                print("❌ ملف main.py مفقود")
                print("💡 استخدم no_crash_camera_tool.py بدلاً من ذلك")
                return
        
        # تشغيل النظام الرئيسي
        print("✅ تشغيل النظام...")
        
        # إضافة معالج أخطاء
        def exception_handler(exc_type, exc_value, exc_traceback):
            print(f"\\n⚠️ خطأ: {exc_type.__name__}: {str(exc_value)}")
            print("💡 النظام سيستمر...")
            
            # حفظ الخطأ
            try:
                with open('crash_log.txt', 'a', encoding='utf-8') as f:
                    f.write(f"{datetime.now()}: {str(exc_value)}\\n")
            except:
                pass
        
        sys.excepthook = exception_handler
        
        # تشغيل main.py
        if os.path.exists('main.py'):
            exec(open('main.py', encoding='utf-8').read())
        else:
            print("❌ main.py غير موجود")
            print("🔧 استخدم no_crash_camera_tool.py")
            import subprocess
            subprocess.run([sys.executable, 'no_crash_camera_tool.py'])
        
    except Exception as e:
        print(f"❌ خطأ في التشغيل: {e}")
        print("💡 جرب تشغيل no_crash_camera_tool.py مباشرة")

def create_emergency_database():
    """إنشاء قاعدة بيانات طارئة"""
    try:
        import sqlite3
        
        conn = sqlite3.connect('database.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password TEXT NOT NULL,
                role TEXT DEFAULT 'user'
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE cameras (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                rtsp_url TEXT NOT NULL,
                username TEXT DEFAULT '',
                password TEXT DEFAULT '',
                is_active BOOLEAN DEFAULT 1
            )
        ''')
        
        cursor.execute('''
            INSERT INTO users (username, password, role)
            VALUES ('admin', 'admin123', 'admin')
        ''')
        
        conn.commit()
        conn.close()
        
        print("✅ تم إنشاء قاعدة بيانات طارئة")
        
    except Exception as e:
        print(f"❌ فشل إنشاء قاعدة البيانات: {e}")

if __name__ == "__main__":
    try:
        safe_main()
        input("\\nاضغط Enter للخروج...")
    except KeyboardInterrupt:
        print("\\n⏹️ تم الإيقاف")
    except Exception as e:
        print(f"\\n❌ خطأ نهائي: {e}")
        input("اضغط Enter للخروج...")
'''
    
    try:
        with open('crash_free_launcher.py', 'w', encoding='utf-8') as f:
            f.write(launcher_content)
        print("✅ تم إنشاء المشغل الآمن")
        return True
    except Exception as e:
        print(f"❌ فشل إنشاء المشغل: {e}")
        return False

def test_instant_fix():
    """اختبار الحل الفوري"""
    print("🧪 اختبار الحل الفوري...")
    
    try:
        # اختبار قاعدة البيانات
        conn = sqlite3.connect('database.db')
        cursor = conn.cursor()
        
        cursor.execute("SELECT COUNT(*) FROM cameras")
        camera_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM users")
        user_count = cursor.fetchone()[0]
        
        conn.close()
        
        print(f"✅ قاعدة البيانات: {camera_count} كاميرا، {user_count} مستخدم")
        
        # اختبار الملفات
        files_created = [
            'no_crash_camera_tool.py',
            'crash_free_launcher.py'
        ]
        
        for file in files_created:
            if os.path.exists(file):
                print(f"✅ {file}")
            else:
                print(f"❌ {file} مفقود")
        
        return True
        
    except Exception as e:
        print(f"❌ فشل الاختبار: {e}")
        return False

def main():
    """الوظيفة الرئيسية"""
    print_instant_fix_banner()
    
    print("🚨 هذا حل فوري وطارئ لمشكلة إغلاق النظام")
    print("🎯 سيتم:")
    print("1. إصلاح طارئ لقاعدة البيانات")
    print("2. إضافة كاميرات XVR فوراً")
    print("3. إنشاء أداة آمنة بدون crash")
    print("4. إنشاء مشغل آمن")
    print("5. تصحيح main.py")
    print()
    
    confirm = input("تطبيق الحل الفوري؟ (y/n): ").lower()
    if confirm not in ['y', 'yes', 'نعم']:
        print("❌ تم الإلغاء")
        return
    
    print("\n⚡ تطبيق الحل الفوري...")
    
    # الخطوات الفورية
    steps = [
        ("إصلاح طارئ لقاعدة البيانات", emergency_database_fix),
        ("إضافة كاميرات XVR فوراً", instant_add_xvr_cameras),
        ("إنشاء أداة بدون crash", create_no_crash_camera_tool),
        ("إنشاء مشغل آمن", create_crash_free_launcher),
        ("تصحيح main.py", patch_main_py),
        ("اختبار الحل", test_instant_fix)
    ]
    
    completed = 0
    
    for step_name, step_function in steps:
        print(f"\n⚡ {step_name}...")
        try:
            if step_function():
                print(f"✅ {step_name}: نجح")
                completed += 1
            else:
                print(f"⚠️ {step_name}: فشل جزئي")
        except Exception as e:
            print(f"❌ {step_name}: خطأ - {e}")
            traceback.print_exc()
    
    # النتائج
    print("\n" + "=" * 50)
    print("⚡ نتائج الحل الفوري:")
    print("=" * 50)
    print(f"✅ تم إكمال {completed}/{len(steps)} خطوات")
    
    if completed >= 4:
        print("\n🎉 تم الحل الفوري بنجاح!")
        
        print("\n🛡️ الأدوات الآمنة الجديدة:")
        print("• no_crash_camera_tool.py - أداة إضافة بدون crash")
        print("• crash_free_launcher.py - مشغل آمن")
        
        print("\n🚀 طرق التشغيل الآمنة:")
        print("1. python no_crash_camera_tool.py - لإضافة كاميرات")
        print("2. python crash_free_launcher.py - لتشغيل النظام")
        print("3. python main.py - النظام الأساسي (مُصحح)")
        
        # خيار التشغيل الفوري
        run_now = input("\nتشغيل أداة الكاميرات الآمنة الآن؟ (y/n): ").lower()
        if run_now in ['y', 'yes', 'نعم']:
            print("\n🚀 تشغيل الأداة الآمنة...")
            try:
                import subprocess
                subprocess.run([sys.executable, 'no_crash_camera_tool.py'])
            except Exception as e:
                print(f"❌ فشل التشغيل: {e}")
        
    else:
        print("\n⚠️ الحل الفوري غير مكتمل")
        print("💡 جرب تشغيل الأدوات المتوفرة يدوياً")

if __name__ == "__main__":
    try:
        main()
        input("\nاضغط Enter للخروج...")
    except KeyboardInterrupt:
        print("\n⏹️ تم الإيقاف")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        traceback.print_exc()
        input("اضغط Enter للخروج...")
