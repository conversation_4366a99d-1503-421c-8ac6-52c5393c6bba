import os

class ArabicText:
    """Arabic text constants and utilities"""
    
    # Main interface texts
    TEXTS = {
        'app_title': 'نظام مراقبة الكاميرات',
        'login': 'تسجيل الدخول',
        'username': 'اسم المستخدم',
        'password': 'كلمة المرور',
        'login_button': 'دخول',
        'logout': 'تسجيل الخروج',
        'cameras': 'الكاميرات',
        'recordings': 'التسجيلات',
        'settings': 'الإعدادات',
        'users': 'المستخدمين',
        'add_camera': 'إضافة كاميرا',
        'edit_camera': 'تعديل كاميرا',
        'delete_camera': 'حذف كاميرا',
        'camera_name': 'اسم الكاميرا',
        'camera_url': 'رابط الكاميرا',
        'camera_type': 'نوع الكاميرا',
        'rtsp_camera': 'كاميرا RTSP',
        'ip_camera': 'كاميرا IP',
        'usb_camera': 'كاميرا USB',
        'save': 'حفظ',
        'cancel': 'إلغاء',
        'delete': 'حذف',
        'edit': 'تعديل',
        'start_recording': 'بدء التسجيل',
        'stop_recording': 'إيقاف التسجيل',
        'motion_detection': 'كشف الحركة',
        'enable': 'تفعيل',
        'disable': 'إلغاء تفعيل',
        'fullscreen': 'ملء الشاشة',
        'exit_fullscreen': 'خروج من ملء الشاشة',
        'about': 'حول البرنامج',
        'help': 'المساعدة',
        'file': 'ملف',
        'view': 'عرض',
        'tools': 'أدوات',
        'window': 'نافذة',
        'status_connected': 'متصل',
        'status_disconnected': 'غير متصل',
        'status_recording': 'يسجل',
        'status_motion_detected': 'تم كشف حركة',
        'error': 'خطأ',
        'warning': 'تحذير',
        'info': 'معلومات',
        'success': 'نجح',
        'camera_connection_failed': 'فشل في الاتصال بالكاميرا',
        'invalid_login': 'بيانات دخول غير صحيحة',
        'camera_added_successfully': 'تم إضافة الكاميرا بنجاح',
        'camera_deleted_successfully': 'تم حذف الكاميرا بنجاح',
        'recording_started': 'بدأ التسجيل',
        'recording_stopped': 'توقف التسجيل',
        'motion_detected': 'تم كشف حركة',
        'no_cameras_found': 'لم يتم العثور على كاميرات',
        'loading': 'جاري التحميل...',
        'connecting': 'جاري الاتصال...',
        'auto_record': 'تسجيل تلقائي',
        'manual_record': 'تسجيل يدوي',
        'sensitivity': 'الحساسية',
        'quality': 'الجودة',
        'fps': 'إطار في الثانية',
        'duration': 'المدة',
        'storage_path': 'مسار التخزين',
        'theme': 'المظهر',
        'language': 'اللغة',
        'dark_theme': 'مظهر داكن',
        'light_theme': 'مظهر فاتح',
        'arabic': 'العربية',
        'english': 'English',
        'apply': 'تطبيق',
        'reset': 'إعادة تعيين',
        'browse': 'تصفح',
        'select_folder': 'اختيار مجلد',
        'camera_grid': 'شبكة الكاميرات',
        'grid_1x1': '1×1',
        'grid_2x2': '2×2',
        'grid_3x3': '3×3',
        'grid_4x4': '4×4',
        'zoom_in': 'تكبير',
        'zoom_out': 'تصغير',
        'fit_to_window': 'ملائمة النافذة',
        'snapshot': 'لقطة شاشة',
        'play': 'تشغيل',
        'pause': 'إيقاف مؤقت',
        'stop': 'إيقاف',
        'volume': 'الصوت',
        'mute': 'كتم الصوت',
        'unmute': 'إلغاء كتم الصوت'
    }
    
    @classmethod
    def get(cls, key, default=""):
        """Get Arabic text by key"""
        return cls.TEXTS.get(key, default)
    
    @classmethod
    def get_camera_types(cls):
        """Get camera type options"""
        return [
            ('rtsp', cls.get('rtsp_camera')),
            ('ip', cls.get('ip_camera')),
            ('usb', cls.get('usb_camera'))
        ]
    
    @classmethod
    def get_grid_options(cls):
        """Get grid layout options"""
        return [
            ('1x1', cls.get('grid_1x1')),
            ('2x2', cls.get('grid_2x2')),
            ('3x3', cls.get('grid_3x3')),
            ('4x4', cls.get('grid_4x4'))
        ]
    
    @classmethod
    def get_quality_options(cls):
        """Get video quality options"""
        return [
            ('low', 'منخفضة'),
            ('medium', 'متوسطة'),
            ('high', 'عالية'),
            ('ultra', 'فائقة')
        ]
    
    @classmethod
    def get_theme_options(cls):
        """Get theme options"""
        return [
            ('dark', cls.get('dark_theme')),
            ('light', cls.get('light_theme'))
        ]
    
    @classmethod
    def get_language_options(cls):
        """Get language options"""
        return [
            ('arabic', cls.get('arabic')),
            ('english', cls.get('english'))
        ]
