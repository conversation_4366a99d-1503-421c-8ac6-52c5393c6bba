#!/bin/bash

# نظام مراقبة الكاميرات - Camera Monitoring System
# Linux/Mac startup script

echo "================================================================================"
echo "🚀 مرحباً بك في نظام مراقبة الكاميرات الشامل"
echo "🚀 Welcome to Complete Camera Monitoring System"
echo "================================================================================"
echo

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "❌ Python غير مثبت على النظام"
        echo "❌ Python is not installed"
        echo
        echo "💡 يرجى تثبيت Python:"
        echo "💡 Please install Python:"
        echo "   Ubuntu/Debian: sudo apt install python3"
        echo "   CentOS/RHEL: sudo yum install python3"
        echo "   macOS: brew install python3"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

echo "✅ Python متوفر - Python available"
$PYTHON_CMD --version
echo

# Check if START_HERE.py exists
if [ ! -f "START_HERE.py" ]; then
    echo "❌ ملف START_HERE.py غير موجود"
    echo "❌ START_HERE.py file not found"
    echo
    echo "💡 تأكد من وجود جميع ملفات النظام"
    echo "💡 Make sure all system files are present"
    exit 1
fi

echo "🚀 تشغيل نظام مراقبة الكاميرات..."
echo "🚀 Starting Camera Monitoring System..."
echo

# Make sure the script has execute permissions
chmod +x START_HERE.py

# Run the main system
$PYTHON_CMD START_HERE.py

echo
echo "👋 تم إنهاء النظام - System terminated"
