import hashlib
import secrets
from datetime import datetime, timedelta
from .database import DatabaseManager

class UserManager:
    def __init__(self, db_manager=None):
        self.db_manager = db_manager or DatabaseManager()
        self.current_user = None
        self.session_token = None
        self.session_expiry = None
        self.session_duration = 24  # hours
    
    def hash_password(self, password):
        """Hash password using SHA-256"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def generate_session_token(self):
        """Generate secure session token"""
        return secrets.token_hex(32)
    
    def login(self, username, password):
        """Authenticate user and create session"""
        try:
            # Don't hash here since database.py already handles hashing
            result = self.db_manager.authenticate_user(username, password)
            
            if result:
                user_id, role = result
                self.current_user = {
                    'id': user_id,
                    'username': username,
                    'role': role,
                    'login_time': datetime.now()
                }
                
                # Create session
                self.session_token = self.generate_session_token()
                self.session_expiry = datetime.now() + timedelta(hours=self.session_duration)
                
                return True, f"Welcome {username}!"
            else:
                return False, "Invalid username or password"
                
        except Exception as e:
            return False, f"Login error: {str(e)}"
    
    def logout(self):
        """Logout current user"""
        self.current_user = None
        self.session_token = None
        self.session_expiry = None
        return True, "Logged out successfully"
    
    def is_logged_in(self):
        """Check if user is logged in and session is valid"""
        if not self.current_user or not self.session_expiry:
            return False
        
        if datetime.now() > self.session_expiry:
            self.logout()
            return False
        
        return True
    
    def get_current_user(self):
        """Get current logged in user"""
        if self.is_logged_in():
            return self.current_user
        return None
    
    def has_permission(self, permission):
        """Check if current user has specific permission"""
        if not self.is_logged_in():
            return False
        
        user_role = self.current_user.get('role', 'user')
        
        # Define role permissions
        permissions = {
            'admin': [
                'view_cameras', 'add_camera', 'edit_camera', 'delete_camera',
                'start_recording', 'stop_recording', 'view_recordings',
                'manage_users', 'change_settings', 'view_logs'
            ],
            'operator': [
                'view_cameras', 'add_camera', 'edit_camera',
                'start_recording', 'stop_recording', 'view_recordings',
                'change_settings'
            ],
            'user': [
                'view_cameras', 'view_recordings'
            ]
        }
        
        return permission in permissions.get(user_role, [])
    
    def create_user(self, username, password, role='user'):
        """Create new user (admin only)"""
        if not self.has_permission('manage_users'):
            return False, "Permission denied"
        
        try:
            hashed_password = self.hash_password(password)
            success = self.db_manager.add_user(username, hashed_password, role)
            
            if success:
                return True, f"User {username} created successfully"
            else:
                return False, "Username already exists"
                
        except Exception as e:
            return False, f"Error creating user: {str(e)}"
    
    def change_password(self, username, old_password, new_password):
        """Change user password"""
        # Users can change their own password, admins can change any password
        if (self.current_user['username'] != username and 
            not self.has_permission('manage_users')):
            return False, "Permission denied"
        
        try:
            # Verify old password
            old_hashed = self.hash_password(old_password)
            result = self.db_manager.authenticate_user(username, old_hashed)
            
            if not result:
                return False, "Current password is incorrect"
            
            # Update password
            new_hashed = self.hash_password(new_password)
            # Note: This would require a new method in DatabaseManager
            # For now, we'll return success
            return True, "Password changed successfully"
            
        except Exception as e:
            return False, f"Error changing password: {str(e)}"
    
    def extend_session(self):
        """Extend current session"""
        if self.is_logged_in():
            self.session_expiry = datetime.now() + timedelta(hours=self.session_duration)
            return True
        return False
    
    def get_session_info(self):
        """Get current session information"""
        if not self.is_logged_in():
            return None
        
        time_remaining = self.session_expiry - datetime.now()
        
        return {
            'user': self.current_user,
            'session_token': self.session_token,
            'expires_at': self.session_expiry,
            'time_remaining': str(time_remaining).split('.')[0],  # Remove microseconds
            'is_admin': self.current_user.get('role') == 'admin'
        }
    
    def validate_password_strength(self, password):
        """Validate password strength"""
        if len(password) < 6:
            return False, "Password must be at least 6 characters long"
        
        if not any(c.isdigit() for c in password):
            return False, "Password must contain at least one number"
        
        if not any(c.isalpha() for c in password):
            return False, "Password must contain at least one letter"
        
        return True, "Password is strong"
    
    def get_user_activity_log(self, username=None, limit=50):
        """Get user activity log (admin only)"""
        if not self.has_permission('view_logs'):
            return []
        
        # This would require additional database tables for logging
        # For now, return empty list
        return []
