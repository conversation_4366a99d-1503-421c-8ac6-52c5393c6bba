#!/usr/bin/env python3
"""
اختبار بسيط لتسجيل الدخول
Simple login test to debug the issue
"""

import sys
import os
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.database import DatabaseManager
from core.user_manager import UserManager

class SimpleLoginTest(QDialog):
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.db_manager = DatabaseManager()
        self.user_manager = UserManager()
        
    def setup_ui(self):
        """إعداد واجهة تسجيل الدخول البسيطة"""
        self.setWindowTitle("اختبار تسجيل الدخول")
        self.setFixedSize(400, 300)
        
        layout = QVBoxLayout()
        
        # العنوان
        title = QLabel("اختبار تسجيل الدخول")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 18px; font-weight: bold; margin: 20px;")
        layout.addWidget(title)
        
        # اسم المستخدم
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("اسم المستخدم")
        self.username_input.setText("admin")  # قيمة افتراضية
        layout.addWidget(QLabel("اسم المستخدم:"))
        layout.addWidget(self.username_input)
        
        # كلمة المرور
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("كلمة المرور")
        self.password_input.setText("admin123")  # قيمة افتراضية
        self.password_input.setEchoMode(QLineEdit.Password)
        layout.addWidget(QLabel("كلمة المرور:"))
        layout.addWidget(self.password_input)
        
        # أزرار الاختبار
        test_db_btn = QPushButton("اختبار قاعدة البيانات")
        test_db_btn.clicked.connect(self.test_database)
        layout.addWidget(test_db_btn)
        
        test_auth_btn = QPushButton("اختبار المصادقة")
        test_auth_btn.clicked.connect(self.test_authentication)
        layout.addWidget(test_auth_btn)
        
        login_btn = QPushButton("تسجيل الدخول")
        login_btn.clicked.connect(self.test_login)
        layout.addWidget(login_btn)
        
        # منطقة النتائج
        self.result_text = QTextEdit()
        self.result_text.setMaximumHeight(150)
        layout.addWidget(self.result_text)
        
        self.setLayout(layout)
        
        # تطبيق نمط بسيط
        self.setStyleSheet("""
            QDialog { background-color: #f0f0f0; }
            QLineEdit { padding: 8px; border: 1px solid #ccc; border-radius: 4px; }
            QPushButton { padding: 8px; background-color: #007acc; color: white; border: none; border-radius: 4px; }
            QPushButton:hover { background-color: #005a9e; }
            QTextEdit { border: 1px solid #ccc; border-radius: 4px; }
        """)
    
    def log_result(self, message):
        """إضافة رسالة إلى منطقة النتائج"""
        self.result_text.append(f"[{QTime.currentTime().toString()}] {message}")
        QApplication.processEvents()
    
    def test_database(self):
        """اختبار الاتصال بقاعدة البيانات"""
        self.log_result("🔍 اختبار قاعدة البيانات...")
        
        try:
            import sqlite3
            conn = sqlite3.connect("database.db")
            cursor = conn.cursor()
            
            # التحقق من وجود جدول المستخدمين
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='users'")
            if cursor.fetchone():
                self.log_result("✅ جدول المستخدمين موجود")
                
                # عرض جميع المستخدمين
                cursor.execute("SELECT username, role FROM users")
                users = cursor.fetchall()
                self.log_result(f"📊 عدد المستخدمين: {len(users)}")
                
                for username, role in users:
                    self.log_result(f"   👤 {username} ({role})")
                    
            else:
                self.log_result("❌ جدول المستخدمين غير موجود!")
                
            conn.close()
            
        except Exception as e:
            self.log_result(f"❌ خطأ في قاعدة البيانات: {str(e)}")
    
    def test_authentication(self):
        """اختبار المصادقة المباشرة"""
        username = self.username_input.text().strip()
        password = self.password_input.text()
        
        self.log_result(f"🔐 اختبار المصادقة للمستخدم: {username}")
        
        try:
            # اختبار مباشر لقاعدة البيانات
            import sqlite3
            import hashlib
            
            conn = sqlite3.connect("database.db")
            cursor = conn.cursor()
            
            # البحث عن المستخدم
            cursor.execute("SELECT id, password, role FROM users WHERE username = ?", (username,))
            result = cursor.fetchone()
            
            if result:
                user_id, stored_password, role = result
                self.log_result(f"✅ المستخدم موجود: ID={user_id}, Role={role}")
                
                # اختبار كلمة المرور
                hashed_input = hashlib.sha256(password.encode()).hexdigest()
                self.log_result(f"🔑 كلمة المرور المدخلة (مشفرة): {hashed_input[:20]}...")
                self.log_result(f"🔑 كلمة المرور المحفوظة: {stored_password[:20]}...")
                
                if stored_password == hashed_input:
                    self.log_result("✅ كلمة المرور صحيحة!")
                else:
                    self.log_result("❌ كلمة المرور خاطئة!")
                    
            else:
                self.log_result("❌ المستخدم غير موجود!")
                
            conn.close()
            
        except Exception as e:
            self.log_result(f"❌ خطأ في اختبار المصادقة: {str(e)}")
    
    def test_login(self):
        """اختبار تسجيل الدخول باستخدام UserManager"""
        username = self.username_input.text().strip()
        password = self.password_input.text()
        
        self.log_result(f"🚪 محاولة تسجيل الدخول: {username}")
        
        if not username or not password:
            self.log_result("❌ يرجى إدخال اسم المستخدم وكلمة المرور")
            return
        
        try:
            success, message = self.user_manager.login(username, password)
            
            if success:
                self.log_result(f"✅ تم تسجيل الدخول بنجاح: {message}")
                user_info = self.user_manager.get_current_user()
                if user_info:
                    self.log_result(f"👤 معلومات المستخدم: {user_info}")
                    
                    # إظهار رسالة نجاح
                    QMessageBox.information(self, "نجح تسجيل الدخول", 
                                          f"مرحباً {user_info['username']}!\n"
                                          f"الدور: {user_info['role']}")
            else:
                self.log_result(f"❌ فشل تسجيل الدخول: {message}")
                
        except Exception as e:
            self.log_result(f"❌ خطأ في تسجيل الدخول: {str(e)}")

def main():
    """تشغيل اختبار تسجيل الدخول"""
    app = QApplication(sys.argv)
    
    # تطبيق خط يدعم العربية
    font = QFont("Arial", 10)
    app.setFont(font)
    
    # إنشاء نافذة الاختبار
    test_window = SimpleLoginTest()
    test_window.show()
    
    # تشغيل اختبار قاعدة البيانات تلقائياً
    QTimer.singleShot(500, test_window.test_database)
    
    return app.exec_()

if __name__ == "__main__":
    sys.exit(main())
