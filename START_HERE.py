#!/usr/bin/env python3
"""
🚀 نقطة البداية الشاملة لنظام مراقبة الكاميرات
🚀 Complete Starting Point for Camera Monitoring System

هذا الملف يوفر نقطة دخول واحدة لجميع إصدارات النظام
This file provides a single entry point for all system versions
"""

import sys
import os
import subprocess
import time
from pathlib import Path

def print_main_banner():
    """طباعة الشعار الرئيسي"""
    print("=" * 80)
    print("🎯 مرحباً بك في نظام مراقبة الكاميرات الشامل")
    print("🎯 Welcome to Complete Camera Monitoring System")
    print("=" * 80)
    print()
    print("📋 الإصدارات المتوفرة:")
    print("1️⃣ النظام الأساسي - Basic System")
    print("   📹 مراقبة الكاميرات + كشف الحركة + التسجيل")
    print()
    print("2️⃣ النظام المتقدم - Advanced System v2.0")
    print("   🤖 ذكاء اصطناعي + تخزين سحابي + إشعارات ذكية")
    print()
    print("3️⃣ أدوات مساعدة - Helper Tools")
    print("   🔧 إضافة كاميرات + إصلاح المشاكل + إعدادات")
    print()

def check_system_requirements():
    """فحص متطلبات النظام الأساسية"""
    print("🔍 فحص متطلبات النظام...")
    
    # فحص Python
    if sys.version_info < (3, 6):
        print(f"❌ Python 3.6+ مطلوب. الإصدار الحالي: {sys.version}")
        return False
    
    print(f"✅ Python {sys.version.split()[0]}")
    
    # فحص المكتبات الأساسية
    basic_libs = ['cv2', 'numpy', 'PyQt5']
    missing = []
    
    for lib in basic_libs:
        try:
            __import__(lib)
            print(f"✅ {lib}")
        except ImportError:
            print(f"❌ {lib} - مفقود")
            missing.append(lib)
    
    if missing:
        print(f"\n⚠️ مكتبات مفقودة: {', '.join(missing)}")
        return False
    
    return True

def quick_install():
    """تثبيت سريع للمتطلبات الأساسية"""
    print("\n🚀 تثبيت سريع للمتطلبات الأساسية...")
    
    packages = [
        'opencv-python==********',
        'numpy==1.24.3',
        'Pillow==10.0.1',
        'PyQt5==5.15.10',
        'requests==2.31.0'
    ]
    
    try:
        for package in packages:
            print(f"📦 تثبيت {package}...")
            result = subprocess.run([
                sys.executable, '-m', 'pip', 'install', package
            ], capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                print(f"✅ تم تثبيت {package}")
            else:
                print(f"⚠️ مشكلة في {package}")
        
        print("✅ تم التثبيت السريع!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في التثبيت: {e}")
        return False

def run_basic_system():
    """تشغيل النظام الأساسي"""
    print("\n📹 تشغيل النظام الأساسي...")
    
    if os.path.exists('run_basic_system.py'):
        subprocess.run([sys.executable, 'run_basic_system.py'])
    elif os.path.exists('main.py'):
        subprocess.run([sys.executable, 'main.py'])
    else:
        print("❌ ملفات النظام الأساسي غير موجودة")

def run_advanced_system():
    """تشغيل النظام المتقدم"""
    print("\n🚀 تشغيل النظام المتقدم...")
    
    if os.path.exists('run_advanced_system.py'):
        subprocess.run([sys.executable, 'run_advanced_system.py'])
    else:
        print("❌ النظام المتقدم غير متوفر")
        print("💡 يمكنك تشغيل النظام الأساسي بدلاً من ذلك")

def show_helper_tools():
    """عرض الأدوات المساعدة"""
    print("\n🔧 الأدوات المساعدة المتوفرة:")
    
    tools = [
        ('add_your_xvr.py', '📹 إضافة كاميرات XVR/DVR'),
        ('xvr_camera_generator.py', '🎯 مولد الكاميرات التلقائي'),
        ('camera_fix_now.py', '🔧 إصلاح مشاكل الكاميرات'),
        ('database_fix.py', '🗄️ إصلاح قاعدة البيانات'),
        ('system_info.py', '📊 معلومات النظام')
    ]
    
    available_tools = []
    
    for i, (filename, description) in enumerate(tools, 1):
        if os.path.exists(filename):
            print(f"{i}. {description} ✅")
            available_tools.append(filename)
        else:
            print(f"{i}. {description} ❌")
    
    if available_tools:
        print(f"\nاختر أداة (1-{len(tools)}) أو 0 للعودة:")
        try:
            choice = int(input("الاختيار: "))
            if 1 <= choice <= len(tools):
                tool_file = tools[choice-1][0]
                if os.path.exists(tool_file):
                    print(f"\n🚀 تشغيل {tool_file}...")
                    subprocess.run([sys.executable, tool_file])
                else:
                    print("❌ الأداة غير متوفرة")
        except ValueError:
            print("❌ اختيار غير صحيح")

def show_system_info():
    """عرض معلومات النظام"""
    print("\n📊 معلومات النظام:")
    print("=" * 50)
    
    print(f"🐍 Python: {sys.version}")
    print(f"💻 نظام التشغيل: {os.name}")
    print(f"📁 المجلد الحالي: {os.getcwd()}")
    
    # فحص الملفات المهمة
    important_files = [
        'main.py', 'database.db', 'config.json',
        'run_basic_system.py', 'run_advanced_system.py'
    ]
    
    print("\n📄 الملفات الأساسية:")
    for file in important_files:
        status = "✅" if os.path.exists(file) else "❌"
        print(f"{status} {file}")
    
    # فحص المجلدات
    important_dirs = ['ui', 'core', 'utils', 'recordings', 'assets']
    
    print("\n📁 المجلدات:")
    for directory in important_dirs:
        status = "✅" if os.path.exists(directory) else "❌"
        print(f"{status} {directory}/")
    
    # فحص المكتبات
    print("\n📦 المكتبات الأساسية:")
    libs = ['cv2', 'numpy', 'PyQt5', 'sqlite3', 'requests']
    
    for lib in libs:
        try:
            __import__(lib)
            print(f"✅ {lib}")
        except ImportError:
            print(f"❌ {lib}")

def create_desktop_shortcut():
    """إنشاء اختصار على سطح المكتب"""
    print("\n🖥️ إنشاء اختصار على سطح المكتب...")
    
    try:
        if os.name == 'nt':  # Windows
            import winshell
            from win32com.client import Dispatch
            
            desktop = winshell.desktop()
            path = os.path.join(desktop, "Camera System.lnk")
            target = os.path.join(os.getcwd(), "START_HERE.py")
            
            shell = Dispatch('WScript.Shell')
            shortcut = shell.CreateShortCut(path)
            shortcut.Targetpath = sys.executable
            shortcut.Arguments = f'"{target}"'
            shortcut.WorkingDirectory = os.getcwd()
            shortcut.IconLocation = target
            shortcut.save()
            
            print("✅ تم إنشاء الاختصار على سطح المكتب")
        else:
            print("⚠️ إنشاء الاختصار متوفر على Windows فقط")
            
    except Exception as e:
        print(f"⚠️ لم يتم إنشاء الاختصار: {e}")

def main():
    """الوظيفة الرئيسية"""
    print_main_banner()
    
    while True:
        print("\n" + "=" * 50)
        print("🎯 اختر ما تريد فعله:")
        print("=" * 50)
        
        print("1️⃣  تشغيل النظام الأساسي")
        print("2️⃣  تشغيل النظام المتقدم")
        print("3️⃣  الأدوات المساعدة")
        print("4️⃣  تثبيت المتطلبات الأساسية")
        print("5️⃣  فحص النظام")
        print("6️⃣  معلومات النظام")
        print("7️⃣  إنشاء اختصار سطح المكتب")
        print("8️⃣  خروج")
        
        try:
            choice = input("\n🎯 اختيارك (1-8): ").strip()
            
            if choice == "1":
                if check_system_requirements():
                    run_basic_system()
                else:
                    print("❌ متطلبات النظام غير مكتملة")
                    install_choice = input("هل تريد تثبيت المتطلبات؟ (y/n): ")
                    if install_choice.lower() in ['y', 'yes', 'نعم']:
                        if quick_install():
                            run_basic_system()
            
            elif choice == "2":
                run_advanced_system()
            
            elif choice == "3":
                show_helper_tools()
            
            elif choice == "4":
                quick_install()
            
            elif choice == "5":
                check_system_requirements()
            
            elif choice == "6":
                show_system_info()
            
            elif choice == "7":
                create_desktop_shortcut()
            
            elif choice == "8":
                print("\n👋 شكراً لاستخدام نظام مراقبة الكاميرات!")
                print("👋 Thank you for using Camera Monitoring System!")
                break
            
            else:
                print("❌ اختيار غير صحيح، حاول مرة أخرى")
        
        except KeyboardInterrupt:
            print("\n\n⏹️ تم إيقاف التشغيل")
            break
        except Exception as e:
            print(f"\n❌ خطأ غير متوقع: {e}")
    
    return 0

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except Exception as e:
        print(f"\n💥 خطأ فادح: {e}")
        input("اضغط Enter للخروج...")
        sys.exit(1)
