@echo off
title نظام مراقبة الكاميرات - Camera Monitoring System
color 0A

echo ================================================
echo 🎥 نظام مراقبة الكاميرات
echo Camera Monitoring System
echo ================================================
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo Please install Python 3.7+ and add it to PATH
    pause
    exit /b 1
)

echo ✅ Python متوفر
echo.

REM التحقق من وجود الملفات المطلوبة
if not exist "safe_start.py" (
    echo ❌ ملف safe_start.py غير موجود
    pause
    exit /b 1
)

if not exist "main.py" (
    echo ❌ ملف main.py غير موجود
    pause
    exit /b 1
)

echo ✅ الملفات المطلوبة موجودة
echo.

REM تثبيت المتطلبات إذا لزم الأمر
if exist "requirements.txt" (
    echo 📦 التحقق من المتطلبات...
    python -c "import cv2, PyQt5, numpy" >nul 2>&1
    if errorlevel 1 (
        echo 🔄 تثبيت المتطلبات...
        python -m pip install -r requirements.txt
        if errorlevel 1 (
            echo ❌ فشل في تثبيت المتطلبات
            pause
            exit /b 1
        )
        echo ✅ تم تثبيت المتطلبات
    ) else (
        echo ✅ جميع المتطلبات متوفرة
    )
) else (
    echo ⚠️ ملف requirements.txt غير موجود
)

echo.
echo 🚀 بدء تشغيل النظام...
echo.

REM تشغيل النظام
python safe_start.py

REM التحقق من رمز الخروج
if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ أثناء تشغيل النظام
    echo.
    echo 🛠️ حلول مقترحة:
    echo 1. تشغيل quick_crash_fix.py لإصلاح المشاكل
    echo 2. تشغيل database_fix.py لإصلاح قاعدة البيانات
    echo 3. إعادة تثبيت المتطلبات: pip install -r requirements.txt
    echo 4. تشغيل البرنامج كمدير
    echo.
    pause
) else (
    echo.
    echo ✅ تم إغلاق النظام بنجاح
)

exit /b %errorlevel%
