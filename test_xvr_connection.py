#!/usr/bin/env python3
"""
اختبار الاتصال بجهاز XVR
Test XVR Device Connection
"""

import cv2
import time
import sys

def test_xvr_connection():
    """اختبار الاتصال بجهاز XVR"""
    print("🔍 اختبار الاتصال بجهاز XVR...")
    print("📍 IP: **************")
    print("👤 المستخدم: admin")
    print("🔒 كلمة المرور: Mnbv@1978")
    print()
    
    # معلومات الاتصال
    ip = "**************"
    username = "admin"
    password = "Mnbv@1978"
    port = 554
    
    # اختبار أول 4 قنوات
    for channel in range(1, 5):
        print(f"🔄 اختبار القناة {channel}...")
        
        # تكوين رابط RTSP
        rtsp_url = f"rtsp://{username}:{password}@{ip}:{port}/cam/realmonitor?channel={channel}&subtype=0"
        
        try:
            # محاولة الاتصال
            cap = cv2.VideoCapture(rtsp_url)
            cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
            cap.set(cv2.CAP_PROP_TIMEOUT, 5000)  # 5 ثوان timeout
            
            if cap.isOpened():
                print(f"   ✅ الاتصال ناجح للقناة {channel}")
                
                # محاولة قراءة إطار
                ret, frame = cap.read()
                if ret and frame is not None:
                    height, width = frame.shape[:2]
                    print(f"   📺 الدقة: {width}x{height}")
                    print(f"   🎥 الرابط: {rtsp_url}")
                else:
                    print(f"   ⚠️ متصل لكن لا يوجد فيديو")
            else:
                print(f"   ❌ فشل الاتصال للقناة {channel}")
            
            cap.release()
            
        except Exception as e:
            print(f"   ❌ خطأ في القناة {channel}: {e}")
        
        time.sleep(1)  # انتظار ثانية بين الاختبارات
    
    print("\n📊 انتهى اختبار الاتصال")

def test_ping():
    """اختبار ping للجهاز"""
    print("\n🌐 اختبار ping للجهاز...")
    
    import subprocess
    import platform
    
    ip = "**************"
    
    try:
        # تحديد أمر ping حسب نظام التشغيل
        if platform.system().lower() == "windows":
            cmd = ["ping", "-n", "4", ip]
        else:
            cmd = ["ping", "-c", "4", ip]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print(f"✅ الجهاز {ip} متاح على الشبكة")
        else:
            print(f"❌ الجهاز {ip} غير متاح على الشبكة")
            print("💡 تأكد من:")
            print("   - اتصال الجهاز بالشبكة")
            print("   - صحة عنوان IP")
            print("   - إعدادات الجدار الناري")
            
    except Exception as e:
        print(f"⚠️ لم يتم اختبار ping: {e}")

def main():
    """الوظيفة الرئيسية"""
    print("=" * 50)
    print("🔍 اختبار اتصال جهاز XVR")
    print("=" * 50)
    
    # اختبار ping أولاً
    test_ping()
    
    # اختبار RTSP
    test_xvr_connection()
    
    print("\n💡 نصائح:")
    print("- إذا فشل الاتصال، تأكد من إعدادات الشبكة")
    print("- تأكد من تفعيل RTSP في إعدادات الجهاز")
    print("- جرب تغيير منفذ RTSP إذا لزم الأمر")

if __name__ == "__main__":
    try:
        main()
        input("\nاضغط Enter للخروج...")
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار")
    except Exception as e:
        print(f"\n❌ خطأ: {e}")
        input("اضغط Enter للخروج...")
