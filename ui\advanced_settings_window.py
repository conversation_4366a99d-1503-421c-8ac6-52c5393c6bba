#!/usr/bin/env python3
"""
واجهة الإعدادات المتقدمة
Advanced Settings Window
"""

import sys
import os
import json
import sqlite3
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class AdvancedSettingsWindow(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("⚙️ الإعدادات المتقدمة - Advanced Settings")
        self.setFixedSize(1200, 800)

        # تحميل الإعدادات
        self.load_settings()

        self.setup_ui()
        self.apply_style()
        self.load_current_settings()

    def load_settings(self):
        """تحميل الإعدادات من الملف"""
        try:
            with open('advanced_config.json', 'r', encoding='utf-8') as f:
                self.settings = json.load(f)
        except:
            self.settings = self.get_default_settings()

    def get_default_settings(self):
        """الحصول على الإعدادات الافتراضية"""
        return {
            "motion_detection": {
                "enabled": True,
                "sensitivity": 0.3,
                "min_area": 500,
                "record_on_motion": True,
                "alert_on_motion": True
            },
            "face_recognition": {
                "enabled": True,
                "confidence_threshold": 0.6,
                "detection_frequency": 2,
                "alert_on_unknown": True
            },
            "license_plate_recognition": {
                "enabled": True,
                "confidence_threshold": 0.7,
                "save_plate_images": True,
                "alert_on_detection": True
            },
            "smart_notifications": {
                "enabled": True,
                "desktop": True,
                "sound": True,
                "email": False
            },
            "recording": {
                "enabled": True,
                "quality": "high",
                "fps": 15,
                "keep_days": 30
            }
        }

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout()

        # العنوان
        header = QLabel("⚙️ الإعدادات المتقدمة لنظام مراقبة الكاميرات")
        header.setAlignment(Qt.AlignCenter)
        header.setStyleSheet("font-size: 18px; font-weight: bold; margin: 15px; color: #2c3e50;")
        layout.addWidget(header)

        # التبويبات
        self.tab_widget = QTabWidget()

        # تبويب كشف الحركة
        self.motion_tab = self.create_motion_detection_tab()
        self.tab_widget.addTab(self.motion_tab, "🎯 كشف الحركة")

        # تبويب التعرف على الوجوه
        self.face_tab = self.create_face_recognition_tab()
        self.tab_widget.addTab(self.face_tab, "👤 التعرف على الوجوه")

        # تبويب أرقام السيارات
        self.plate_tab = self.create_license_plate_tab()
        self.tab_widget.addTab(self.plate_tab, "🚗 أرقام السيارات")

        # تبويب الإشعارات
        self.notification_tab = self.create_notifications_tab()
        self.tab_widget.addTab(self.notification_tab, "🔔 الإشعارات")

        # تبويب التسجيل
        self.recording_tab = self.create_recording_tab()
        self.tab_widget.addTab(self.recording_tab, "📹 التسجيل")

        # تبويب الأداء
        self.performance_tab = self.create_performance_tab()
        self.tab_widget.addTab(self.performance_tab, "⚡ الأداء")

        layout.addWidget(self.tab_widget)

        # أزرار التحكم
        button_layout = QHBoxLayout()

        self.test_btn = QPushButton("🧪 اختبار الإعدادات")
        self.test_btn.clicked.connect(self.test_settings)
        button_layout.addWidget(self.test_btn)

        self.reset_btn = QPushButton("🔄 إعادة تعيين")
        self.reset_btn.clicked.connect(self.reset_settings)
        button_layout.addWidget(self.reset_btn)

        button_layout.addStretch()

        self.save_btn = QPushButton("💾 حفظ الإعدادات")
        self.save_btn.clicked.connect(self.save_settings)
        button_layout.addWidget(self.save_btn)

        self.cancel_btn = QPushButton("❌ إلغاء")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)

        layout.addLayout(button_layout)

        self.setLayout(layout)

    def create_motion_detection_tab(self):
        """إنشاء تبويب كشف الحركة"""
        widget = QWidget()
        layout = QVBoxLayout()

        # تفعيل كشف الحركة
        self.motion_enabled = QCheckBox("تفعيل كشف الحركة")
        layout.addWidget(self.motion_enabled)

        # إعدادات الحساسية
        sensitivity_group = QGroupBox("إعدادات الحساسية")
        sensitivity_layout = QFormLayout()

        self.motion_sensitivity = QSlider(Qt.Horizontal)
        self.motion_sensitivity.setRange(1, 10)
        self.motion_sensitivity.setValue(3)
        self.sensitivity_label = QLabel("متوسط")
        self.motion_sensitivity.valueChanged.connect(self.update_sensitivity_label)

        sens_layout = QHBoxLayout()
        sens_layout.addWidget(self.motion_sensitivity)
        sens_layout.addWidget(self.sensitivity_label)
        sensitivity_layout.addRow("مستوى الحساسية:", sens_layout)

        self.min_area_spin = QSpinBox()
        self.min_area_spin.setRange(100, 5000)
        self.min_area_spin.setValue(500)
        self.min_area_spin.setSuffix(" بكسل")
        sensitivity_layout.addRow("أقل مساحة للكشف:", self.min_area_spin)

        sensitivity_group.setLayout(sensitivity_layout)
        layout.addWidget(sensitivity_group)

        # إعدادات التسجيل والتنبيه
        actions_group = QGroupBox("الإجراءات عند كشف الحركة")
        actions_layout = QVBoxLayout()

        self.record_on_motion = QCheckBox("تسجيل عند كشف الحركة")
        self.alert_on_motion = QCheckBox("تنبيه عند كشف الحركة")
        self.save_motion_images = QCheckBox("حفظ صور كشف الحركة")

        actions_layout.addWidget(self.record_on_motion)
        actions_layout.addWidget(self.alert_on_motion)
        actions_layout.addWidget(self.save_motion_images)

        actions_group.setLayout(actions_layout)
        layout.addWidget(actions_group)

        # إعدادات متقدمة
        advanced_group = QGroupBox("إعدادات متقدمة")
        advanced_layout = QFormLayout()

        self.motion_cooldown = QSpinBox()
        self.motion_cooldown.setRange(1, 60)
        self.motion_cooldown.setValue(2)
        self.motion_cooldown.setSuffix(" ثانية")
        advanced_layout.addRow("فترة التهدئة:", self.motion_cooldown)

        self.detection_method = QComboBox()
        self.detection_method.addItems(["Background Subtraction", "Frame Difference"])
        advanced_layout.addRow("طريقة الكشف:", self.detection_method)

        advanced_group.setLayout(advanced_layout)
        layout.addWidget(advanced_group)

        layout.addStretch()
        widget.setLayout(layout)
        return widget

    def create_face_recognition_tab(self):
        """إنشاء تبويب التعرف على الوجوه"""
        widget = QWidget()
        layout = QVBoxLayout()

        # تفعيل التعرف على الوجوه
        self.face_enabled = QCheckBox("تفعيل التعرف على الوجوه")
        layout.addWidget(self.face_enabled)

        # إعدادات الدقة
        accuracy_group = QGroupBox("إعدادات الدقة")
        accuracy_layout = QFormLayout()

        self.face_model = QComboBox()
        self.face_model.addItems(["HOG (سريع)", "CNN (دقيق)"])
        accuracy_layout.addRow("نموذج الكشف:", self.face_model)

        self.face_confidence = QSlider(Qt.Horizontal)
        self.face_confidence.setRange(1, 10)
        self.face_confidence.setValue(6)
        self.face_confidence_label = QLabel("60%")
        self.face_confidence.valueChanged.connect(self.update_face_confidence_label)

        conf_layout = QHBoxLayout()
        conf_layout.addWidget(self.face_confidence)
        conf_layout.addWidget(self.face_confidence_label)
        accuracy_layout.addRow("عتبة الثقة:", conf_layout)

        self.detection_frequency = QSpinBox()
        self.detection_frequency.setRange(1, 10)
        self.detection_frequency.setValue(2)
        self.detection_frequency.setSuffix(" إطار")
        accuracy_layout.addRow("تكرار الكشف:", self.detection_frequency)

        accuracy_group.setLayout(accuracy_layout)
        layout.addWidget(accuracy_group)

        # إعدادات التنبيهات
        alerts_group = QGroupBox("إعدادات التنبيهات")
        alerts_layout = QVBoxLayout()

        self.alert_on_known = QCheckBox("تنبيه عند رؤية شخص معروف")
        self.alert_on_unknown = QCheckBox("تنبيه عند رؤية شخص غير معروف")
        self.save_unknown_faces = QCheckBox("حفظ صور الوجوه غير المعروفة")

        alerts_layout.addWidget(self.alert_on_known)
        alerts_layout.addWidget(self.alert_on_unknown)
        alerts_layout.addWidget(self.save_unknown_faces)

        alerts_group.setLayout(alerts_layout)
        layout.addWidget(alerts_group)

        # إدارة الأشخاص المعروفين
        persons_group = QGroupBox("إدارة الأشخاص المعروفين")
        persons_layout = QVBoxLayout()

        # قائمة الأشخاص
        self.persons_list = QListWidget()
        self.load_known_persons()
        persons_layout.addWidget(self.persons_list)

        # أزرار الإدارة
        persons_buttons = QHBoxLayout()

        self.add_person_btn = QPushButton("➕ إضافة شخص")
        self.add_person_btn.clicked.connect(self.add_person)
        persons_buttons.addWidget(self.add_person_btn)

        self.edit_person_btn = QPushButton("✏️ تعديل")
        self.edit_person_btn.clicked.connect(self.edit_person)
        persons_buttons.addWidget(self.edit_person_btn)

        self.delete_person_btn = QPushButton("🗑️ حذف")
        self.delete_person_btn.clicked.connect(self.delete_person)
        persons_buttons.addWidget(self.delete_person_btn)

        persons_layout.addLayout(persons_buttons)
        persons_group.setLayout(persons_layout)
        layout.addWidget(persons_group)

        layout.addStretch()
        widget.setLayout(layout)
        return widget

    def create_license_plate_tab(self):
        """إنشاء تبويب أرقام السيارات"""
        widget = QWidget()
        layout = QVBoxLayout()

        # تفعيل قراءة أرقام السيارات
        self.plate_enabled = QCheckBox("تفعيل قراءة أرقام السيارات")
        layout.addWidget(self.plate_enabled)

        # إعدادات الكشف
        detection_group = QGroupBox("إعدادات الكشف")
        detection_layout = QFormLayout()

        self.plate_confidence = QSlider(Qt.Horizontal)
        self.plate_confidence.setRange(1, 10)
        self.plate_confidence.setValue(7)
        self.plate_confidence_label = QLabel("70%")
        self.plate_confidence.valueChanged.connect(self.update_plate_confidence_label)

        plate_conf_layout = QHBoxLayout()
        plate_conf_layout.addWidget(self.plate_confidence)
        plate_conf_layout.addWidget(self.plate_confidence_label)
        detection_layout.addRow("عتبة الثقة:", plate_conf_layout)

        self.min_plate_width = QSpinBox()
        self.min_plate_width.setRange(50, 200)
        self.min_plate_width.setValue(80)
        self.min_plate_width.setSuffix(" بكسل")
        detection_layout.addRow("أقل عرض للوحة:", self.min_plate_width)

        self.ocr_language = QComboBox()
        self.ocr_language.addItems(["عربي + إنجليزي", "عربي فقط", "إنجليزي فقط"])
        detection_layout.addRow("لغة OCR:", self.ocr_language)

        detection_group.setLayout(detection_layout)
        layout.addWidget(detection_group)

        # إعدادات الحفظ والتنبيه
        actions_group = QGroupBox("الإجراءات")
        actions_layout = QVBoxLayout()

        self.save_plate_images = QCheckBox("حفظ صور اللوحات")
        self.alert_on_plate = QCheckBox("تنبيه عند كشف لوحة")
        self.alert_on_blacklist = QCheckBox("تنبيه خاص للوحات المحظورة")

        actions_layout.addWidget(self.save_plate_images)
        actions_layout.addWidget(self.alert_on_plate)
        actions_layout.addWidget(self.alert_on_blacklist)

        actions_group.setLayout(actions_layout)
        layout.addWidget(actions_group)

        # إدارة قوائم اللوحات
        lists_group = QGroupBox("إدارة قوائم اللوحات")
        lists_layout = QHBoxLayout()

        # القائمة البيضاء
        whitelist_layout = QVBoxLayout()
        whitelist_layout.addWidget(QLabel("القائمة البيضاء (مسموحة):"))
        self.whitelist_widget = QListWidget()
        self.load_plate_lists('whitelist')
        whitelist_layout.addWidget(self.whitelist_widget)

        whitelist_buttons = QHBoxLayout()
        self.add_whitelist_btn = QPushButton("➕")
        self.add_whitelist_btn.clicked.connect(lambda: self.add_plate_to_list('whitelist'))
        self.remove_whitelist_btn = QPushButton("➖")
        self.remove_whitelist_btn.clicked.connect(lambda: self.remove_plate_from_list('whitelist'))
        whitelist_buttons.addWidget(self.add_whitelist_btn)
        whitelist_buttons.addWidget(self.remove_whitelist_btn)
        whitelist_layout.addLayout(whitelist_buttons)

        # القائمة السوداء
        blacklist_layout = QVBoxLayout()
        blacklist_layout.addWidget(QLabel("القائمة السوداء (محظورة):"))
        self.blacklist_widget = QListWidget()
        self.load_plate_lists('blacklist')
        blacklist_layout.addWidget(self.blacklist_widget)

        blacklist_buttons = QHBoxLayout()
        self.add_blacklist_btn = QPushButton("➕")
        self.add_blacklist_btn.clicked.connect(lambda: self.add_plate_to_list('blacklist'))
        self.remove_blacklist_btn = QPushButton("➖")
        self.remove_blacklist_btn.clicked.connect(lambda: self.remove_plate_from_list('blacklist'))
        blacklist_buttons.addWidget(self.add_blacklist_btn)
        blacklist_buttons.addWidget(self.remove_blacklist_btn)
        blacklist_layout.addLayout(blacklist_buttons)

        lists_layout.addLayout(whitelist_layout)
        lists_layout.addLayout(blacklist_layout)
        lists_group.setLayout(lists_layout)
        layout.addWidget(lists_group)

        layout.addStretch()
        widget.setLayout(layout)
        return widget