# Camera Monitoring System

A comprehensive Python-based camera monitoring system with Arabic language support and dark theme interface.

## Features

### Core Features
- **Multi-camera support**: RTSP, IP, and USB cameras
- **Real-time streaming**: Live video feeds with minimal delay
- **Motion detection**: Automatic motion detection with configurable sensitivity
- **Video recording**: Manual and automatic recording with motion triggers
- **User management**: Multi-user support with role-based permissions
- **Arabic language**: Full Arabic interface support
- **Dark theme**: Professional dark interface design

### Camera Management
- Add, edit, and delete cameras
- Support for DVR/NVR systems with multiple channels
- Camera connection testing
- Grid layout for multiple cameras (1x1, 2x2, 3x3, 4x4)
- Full-screen viewing mode

### Recording Features
- Manual recording control
- Motion-triggered automatic recording
- Configurable recording quality and duration
- Recording playback and management
- Snapshot capture

### User Interface
- Responsive design (1280x800 default, scalable)
- Context menus for camera controls
- Status indicators for recording and motion
- Real-time FPS and connection status
- Keyboard shortcuts support

## System Requirements

- **Operating System**: Windows 10 or later
- **Python**: 3.7 or higher (for development)
- **Memory**: Minimum 4GB RAM
- **Storage**: 1GB free disk space
- **Network**: For IP/RTSP cameras

## Installation

### For End Users (Executable)
1. Download the latest release
2. Extract all files to a folder
3. Run `CameraMonitoringSystem.exe`
4. Default login: `admin` / `admin123`

### For Developers
1. Clone the repository
2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```
3. Run the application:
   ```bash
   python main.py
   ```

## Dependencies

- **PyQt5**: GUI framework
- **OpenCV**: Computer vision and camera handling
- **NumPy**: Numerical operations
- **SQLite3**: Database management
- **PyInstaller**: Executable creation

## Project Structure

```
camera_system/
├── main.py                 # Main application entry point
├── ui/                     # User interface modules
│   ├── login_window.py     # Login interface
│   ├── main_window.py      # Main application window
│   └── camera_manager.py   # Camera management dialog
├── core/                   # Core functionality
│   ├── database.py         # Database operations
│   ├── camera_handler.py   # Camera streaming and control
│   ├── motion_detector.py  # Motion detection
│   ├── recorder.py         # Video recording
│   └── user_manager.py     # User authentication
├── utils/                  # Utility modules
│   ├── config.py           # Configuration management
│   └── arabic_support.py   # Arabic text constants
├── assets/                 # Images and icons
├── recordings/             # Video recordings storage
├── config.json             # Application configuration
├── database.db             # SQLite database
└── requirements.txt        # Python dependencies
```

## Configuration

The system uses `config.json` for configuration:

```json
{
    "app_settings": {
        "window_width": 1280,
        "window_height": 800,
        "theme": "dark",
        "language": "arabic",
        "fullscreen": false,
        "auto_record": true,
        "motion_sensitivity": 50
    },
    "recording_settings": {
        "format": "mp4",
        "quality": "high",
        "fps": 30,
        "duration_minutes": 60
    },
    "motion_detection": {
        "enabled": true,
        "threshold": 25,
        "min_area": 500
    }
}
```

## Usage

### Adding Cameras

1. Click the "Cameras" button in the toolbar
2. Click "Add Camera"
3. Enter camera details:
   - **Name**: Descriptive name for the camera
   - **URL**: Camera stream URL or device number
   - **Type**: RTSP, IP, or USB

### Camera URL Examples

- **RTSP**: `rtsp://admin:password@*************:554/stream`
- **IP Camera**: `http://*************:8080/video`
- **USB Camera**: `0` (device number)

### DVR/NVR Integration

For DVR/NVR systems, use the channel-based URL format:
```
rtsp://admin:password@*************:554/Streaming/Channels/{}/
```

The system will automatically generate URLs for multiple channels:
- Channel 1: `rtsp://admin:password@*************:554/Streaming/Channels/101/`
- Channel 2: `rtsp://admin:password@*************:554/Streaming/Channels/201/`
- Channel 3: `rtsp://admin:password@*************:554/Streaming/Channels/301/`

### Recording

- **Manual Recording**: Right-click on camera → Start Recording
- **Automatic Recording**: Enable motion detection for automatic recording
- **Snapshots**: Right-click on camera → Snapshot

### User Management

Default users:
- **Admin**: `admin` / `admin123` (full access)

User roles:
- **Admin**: Full system access
- **Operator**: Camera and recording management
- **User**: View-only access

## Building Executable

To create a standalone executable:

```bash
python build_exe.py
```

This will create `dist/CameraMonitoringSystem.exe` with all dependencies included.

## Testing

Run the test suite to verify system functionality:

```bash
python test_system.py
```

## Troubleshooting

### Common Issues

1. **Camera Connection Failed**
   - Verify camera URL and credentials
   - Check network connectivity
   - Ensure camera supports the specified protocol

2. **Motion Detection Not Working**
   - Adjust motion sensitivity in settings
   - Ensure adequate lighting
   - Check camera positioning

3. **Recording Issues**
   - Verify sufficient disk space
   - Check recording directory permissions
   - Ensure camera stream is stable

### Performance Optimization

- Limit the number of simultaneous cameras based on system resources
- Use lower resolution streams for better performance
- Close unused applications to free up system resources

## Development

### Adding New Features

1. Create feature branch
2. Implement changes following the existing code structure
3. Add tests for new functionality
4. Update documentation
5. Submit pull request

### Code Style

- Follow PEP 8 guidelines
- Use meaningful variable and function names
- Add docstrings for all classes and methods
- Include error handling and logging

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For technical support or questions:
- Check the troubleshooting section
- Review the documentation
- Contact the system administrator

## Version History

- **v1.0**: Initial release with core functionality
  - Multi-camera support
  - Motion detection
  - Video recording
  - User management
  - Arabic language support
