import sqlite3
import os
from datetime import datetime
import json

class DatabaseManager:
    def __init__(self, db_path="database.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """Initialize database with required tables"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Users table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password TEXT NOT NULL,
                role TEXT DEFAULT 'user',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_login TIMESTAMP
            )
        ''')
        
        # Cameras table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS cameras (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                url TEXT NOT NULL,
                camera_type TEXT DEFAULT 'rtsp',
                position_x INTEGER DEFAULT 0,
                position_y INTEGER DEFAULT 0,
                enabled BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Recordings table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS recordings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                camera_id INTEGER,
                filename TEXT NOT NULL,
                file_path TEXT NOT NULL,
                start_time TIMESTAMP,
                end_time TIMESTAMP,
                file_size INTEGER,
                motion_triggered BOOLEAN DEFAULT 0,
                FOREIGN KEY (camera_id) REFERENCES cameras (id)
            )
        ''')
        
        # Motion events table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS motion_events (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                camera_id INTEGER,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                confidence REAL,
                recording_id INTEGER,
                FOREIGN KEY (camera_id) REFERENCES cameras (id),
                FOREIGN KEY (recording_id) REFERENCES recordings (id)
            )
        ''')
        
        # Create default admin user if not exists
        cursor.execute("SELECT COUNT(*) FROM users WHERE username = 'admin'")
        if cursor.fetchone()[0] == 0:
            import hashlib
            hashed_password = hashlib.sha256('admin123'.encode()).hexdigest()
            cursor.execute(
                "INSERT INTO users (username, password, role) VALUES (?, ?, ?)",
                ('admin', hashed_password, 'admin')
            )
        
        conn.commit()
        conn.close()
    
    def add_user(self, username, password, role='user'):
        """Add new user"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        try:
            cursor.execute(
                "INSERT INTO users (username, password, role) VALUES (?, ?, ?)",
                (username, password, role)
            )
            conn.commit()
            return True
        except sqlite3.IntegrityError:
            return False
        finally:
            conn.close()
    
    def authenticate_user(self, username, password):
        """Authenticate user login"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Hash the input password
        import hashlib
        hashed_password = hashlib.sha256(password.encode()).hexdigest()

        cursor.execute(
            "SELECT id, role FROM users WHERE username = ? AND password = ?",
            (username, hashed_password)
        )
        result = cursor.fetchone()
        
        if result:
            # Update last login
            cursor.execute(
                "UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?",
                (result[0],)
            )
            conn.commit()
        
        conn.close()
        return result
    
    def add_camera(self, name, url, camera_type='rtsp'):
        """Add new camera"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute(
            "INSERT INTO cameras (name, url, camera_type) VALUES (?, ?, ?)",
            (name, url, camera_type)
        )
        camera_id = cursor.lastrowid
        conn.commit()
        conn.close()
        return camera_id
    
    def get_cameras(self):
        """Get all cameras"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM cameras WHERE enabled = 1")
        cameras = cursor.fetchall()
        conn.close()
        return cameras
    
    def update_camera(self, camera_id, name, url, camera_type):
        """Update camera information"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute(
            "UPDATE cameras SET name = ?, url = ?, camera_type = ? WHERE id = ?",
            (name, url, camera_type, camera_id)
        )
        conn.commit()
        conn.close()
    
    def delete_camera(self, camera_id):
        """Delete camera"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("UPDATE cameras SET enabled = 0 WHERE id = ?", (camera_id,))
        conn.commit()
        conn.close()
    
    def add_recording(self, camera_id, filename, file_path, start_time, motion_triggered=False):
        """Add recording entry"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute(
            "INSERT INTO recordings (camera_id, filename, file_path, start_time, motion_triggered) VALUES (?, ?, ?, ?, ?)",
            (camera_id, filename, file_path, start_time, motion_triggered)
        )
        recording_id = cursor.lastrowid
        conn.commit()
        conn.close()
        return recording_id
    
    def add_motion_event(self, camera_id, confidence, recording_id=None):
        """Add motion detection event"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute(
            "INSERT INTO motion_events (camera_id, confidence, recording_id) VALUES (?, ?, ?)",
            (camera_id, confidence, recording_id)
        )
        conn.commit()
        conn.close()
    
    def get_recordings(self, camera_id=None, limit=100):
        """Get recordings"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        if camera_id:
            cursor.execute(
                "SELECT * FROM recordings WHERE camera_id = ? ORDER BY start_time DESC LIMIT ?",
                (camera_id, limit)
            )
        else:
            cursor.execute(
                "SELECT * FROM recordings ORDER BY start_time DESC LIMIT ?",
                (limit,)
            )
        
        recordings = cursor.fetchall()
        conn.close()
        return recordings
