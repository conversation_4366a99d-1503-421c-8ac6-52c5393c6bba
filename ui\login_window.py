import sys
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.arabic_support import ArabicText
from core.user_manager import UserManager

class LoginWindow(QDialog):
    login_successful = pyqtSignal(dict)  # Signal emitted when login is successful
    
    def __init__(self):
        super().__init__()
        self.user_manager = UserManager()
        self.setup_ui()
        self.apply_dark_theme()
        
    def setup_ui(self):
        """Setup the login interface"""
        self.setWindowTitle(ArabicText.get('app_title'))
        self.setFixedSize(400, 300)
        self.setWindowFlags(Qt.Dialog | Qt.WindowCloseButtonHint)
        
        # Center the window
        self.center_window()
        
        # Main layout
        main_layout = QVBoxLayout()
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(40, 40, 40, 40)
        
        # Title
        title_label = QLabel(ArabicText.get('app_title'))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #ffffff;
                margin-bottom: 20px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # Login form
        form_layout = QFormLayout()
        form_layout.setSpacing(15)
        
        # Username field
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText(ArabicText.get('username'))
        self.username_input.setMinimumHeight(40)
        form_layout.addRow(ArabicText.get('username') + ":", self.username_input)
        
        # Password field
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText(ArabicText.get('password'))
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setMinimumHeight(40)
        form_layout.addRow(ArabicText.get('password') + ":", self.password_input)
        
        main_layout.addLayout(form_layout)
        
        # Login button
        self.login_button = QPushButton(ArabicText.get('login_button'))
        self.login_button.setMinimumHeight(45)
        self.login_button.clicked.connect(self.handle_login)
        main_layout.addWidget(self.login_button)
        
        # Status label
        self.status_label = QLabel("")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("color: #ff6b6b; font-size: 12px;")
        main_layout.addWidget(self.status_label)
        
        # Add some stretch
        main_layout.addStretch()
        
        self.setLayout(main_layout)
        
        # Connect Enter key to login
        self.username_input.returnPressed.connect(self.handle_login)
        self.password_input.returnPressed.connect(self.handle_login)
        
        # Set default values for testing
        self.username_input.setText("admin")
        self.password_input.setText("admin123")
    
    def center_window(self):
        """Center the window on screen"""
        screen = QApplication.desktop().screenGeometry()
        size = self.geometry()
        self.move(
            (screen.width() - size.width()) // 2,
            (screen.height() - size.height()) // 2
        )
    
    def apply_dark_theme(self):
        """Apply dark theme to the login window"""
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2c3e50, stop:1 #34495e);
                border-radius: 10px;
            }
            
            QLabel {
                color: #ecf0f1;
                font-size: 14px;
            }
            
            QLineEdit {
                background-color: #34495e;
                border: 2px solid #5d6d7e;
                border-radius: 8px;
                padding: 8px 12px;
                color: #ecf0f1;
                font-size: 14px;
            }
            
            QLineEdit:focus {
                border-color: #3498db;
                background-color: #2c3e50;
            }
            
            QLineEdit::placeholder {
                color: #95a5a6;
            }
            
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3498db, stop:1 #2980b9);
                border: none;
                border-radius: 8px;
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 12px;
            }
            
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #5dade2, stop:1 #3498db);
            }
            
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2980b9, stop:1 #1f618d);
            }
            
            QPushButton:disabled {
                background-color: #7f8c8d;
                color: #bdc3c7;
            }
        """)
    
    def handle_login(self):
        """Handle login button click"""
        username = self.username_input.text().strip()
        password = self.password_input.text()
        
        if not username or not password:
            self.show_status(ArabicText.get('error') + ": " + "Please enter username and password", error=True)
            return
        
        # Disable login button during authentication
        self.login_button.setEnabled(False)
        self.login_button.setText(ArabicText.get('loading'))
        
        # Perform authentication
        success, message = self.user_manager.login(username, password)
        
        if success:
            self.show_status(message, error=False)
            # Emit signal with user info
            user_info = self.user_manager.get_current_user()
            self.login_successful.emit(user_info)
            
            # Close login window after short delay
            QTimer.singleShot(1000, self.accept)
        else:
            self.show_status(message, error=True)
            self.login_button.setEnabled(True)
            self.login_button.setText(ArabicText.get('login_button'))
            
            # Clear password field
            self.password_input.clear()
            self.password_input.setFocus()
    
    def show_status(self, message, error=True):
        """Show status message"""
        self.status_label.setText(message)
        if error:
            self.status_label.setStyleSheet("color: #e74c3c; font-size: 12px;")
        else:
            self.status_label.setStyleSheet("color: #27ae60; font-size: 12px;")
    
    def keyPressEvent(self, event):
        """Handle key press events"""
        if event.key() == Qt.Key_Escape:
            self.reject()
        else:
            super().keyPressEvent(event)
