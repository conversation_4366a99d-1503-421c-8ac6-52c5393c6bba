#!/usr/bin/env python3
"""
حل مشكلة إغلاق النظام عند إضافة كاميرا - الحل النهائي
Final Solution for Camera Addition Crash
"""

import sys
import os
import sqlite3
import cv2
import time
import subprocess
from datetime import datetime

def print_solution_banner():
    """طباعة شعار الحل"""
    print("=" * 60)
    print("🔧 الحل النهائي لمشكلة إغلاق النظام عند إضافة كاميرا")
    print("🔧 Final Solution for Camera Addition Crash")
    print("=" * 60)
    print()
    print("🎯 المشاكل التي سيتم حلها:")
    print("❌ إغلاق النظام عند إضافة كاميرا")
    print("❌ تجمد الواجهة أثناء اختبار الاتصال")
    print("❌ مشاكل timeout في الاتصال")
    print("❌ عدم معالجة الأخطاء بشكل صحيح")
    print()

def fix_database_final():
    """إصلاح نهائي لقاعدة البيانات"""
    print("🗄️ إصلاح قاعدة البيانات...")
    
    try:
        # إنشاء نسخة احتياطية
        if os.path.exists('database.db'):
            backup_name = f'database_backup_{int(time.time())}.db'
            import shutil
            shutil.copy2('database.db', backup_name)
            print(f"📋 نسخة احتياطية: {backup_name}")
        
        conn = sqlite3.connect('database.db', timeout=15)
        cursor = conn.cursor()
        
        # إنشاء جدول الكاميرات المحسن
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS cameras (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                rtsp_url TEXT NOT NULL,
                username TEXT DEFAULT '',
                password TEXT DEFAULT '',
                is_active BOOLEAN DEFAULT 1,
                camera_type TEXT DEFAULT 'IP',
                ip_address TEXT,
                port INTEGER DEFAULT 554,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_tested TIMESTAMP,
                connection_status TEXT DEFAULT 'unknown'
            )
        ''')
        
        # إضافة أعمدة جديدة إذا لم تكن موجودة
        new_columns = [
            ('camera_type', 'TEXT DEFAULT "IP"'),
            ('ip_address', 'TEXT'),
            ('port', 'INTEGER DEFAULT 554'),
            ('last_tested', 'TIMESTAMP'),
            ('connection_status', 'TEXT DEFAULT "unknown"')
        ]
        
        for column_name, column_def in new_columns:
            try:
                cursor.execute(f'ALTER TABLE cameras ADD COLUMN {column_name} {column_def}')
            except sqlite3.OperationalError:
                pass  # العمود موجود مسبقاً
        
        conn.commit()
        conn.close()
        
        print("✅ تم إصلاح قاعدة البيانات")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح قاعدة البيانات: {e}")
        return False

def add_camera_ultra_safe(name, rtsp_url, username="", password=""):
    """إضافة كاميرا بطريقة آمنة جداً"""
    try:
        # التحقق من البيانات
        if not name or not rtsp_url:
            return False, "بيانات ناقصة"
        
        # تنظيف البيانات
        name = name.strip()
        rtsp_url = rtsp_url.strip()
        username = username.strip()
        password = password.strip()
        
        # الاتصال بقاعدة البيانات مع timeout طويل
        conn = sqlite3.connect('database.db', timeout=20)
        cursor = conn.cursor()
        
        # التحقق من عدم التكرار
        cursor.execute("SELECT id FROM cameras WHERE name = ? OR rtsp_url = ?", (name, rtsp_url))
        existing = cursor.fetchone()
        if existing:
            conn.close()
            return False, "الكاميرا موجودة مسبقاً"
        
        # استخراج IP من RTSP URL
        ip_address = ""
        try:
            if "@" in rtsp_url:
                ip_part = rtsp_url.split("@")[1].split(":")[0]
                ip_address = ip_part
        except:
            pass
        
        # الإضافة
        cursor.execute('''
            INSERT INTO cameras 
            (name, rtsp_url, username, password, is_active, camera_type, ip_address, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (name, rtsp_url, username, password, 1, 'XVR', ip_address, datetime.now()))
        
        camera_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        return True, f"تم إضافة الكاميرا بنجاح (ID: {camera_id})"
        
    except Exception as e:
        return False, f"خطأ في الإضافة: {str(e)}"

def add_all_xvr_cameras():
    """إضافة جميع كاميرات XVR بطريقة آمنة"""
    print("📹 إضافة كاميرات XVR...")
    
    xvr_config = {
        'ip': '**************',
        'username': 'admin',
        'password': 'Mnbv@1978',
        'port': 554,
        'channels': 8
    }
    
    added_count = 0
    failed_count = 0
    
    for channel in range(1, xvr_config['channels'] + 1):
        camera_name = f"XVR قناة {channel}"
        rtsp_url = f"rtsp://{xvr_config['username']}:{xvr_config['password']}@{xvr_config['ip']}:{xvr_config['port']}/cam/realmonitor?channel={channel}&subtype=0"
        
        print(f"🔄 إضافة {camera_name}...")
        
        success, message = add_camera_ultra_safe(
            name=camera_name,
            rtsp_url=rtsp_url,
            username=xvr_config['username'],
            password=xvr_config['password']
        )
        
        if success:
            print(f"✅ {camera_name}: {message}")
            added_count += 1
        else:
            print(f"❌ {camera_name}: {message}")
            failed_count += 1
        
        # انتظار قصير بين الإضافات
        time.sleep(0.3)
    
    print(f"\n📊 النتائج:")
    print(f"✅ تم إضافة: {added_count} كاميرا")
    print(f"❌ فشل: {failed_count} كاميرا")
    
    return added_count > 0

def create_ultimate_safe_camera_tool():
    """إنشاء أداة إضافة الكاميرات الآمنة النهائية"""
    tool_content = '''#!/usr/bin/env python3
"""
أداة إضافة الكاميرات الآمنة النهائية
Ultimate Safe Camera Addition Tool
"""

import sqlite3
import cv2
import sys
import time
import threading
from datetime import datetime

class UltimateSafeCameraManager:
    def __init__(self):
        self.db_path = 'database.db'
        self.connection_timeout = 10
        
    def test_camera_connection(self, rtsp_url, timeout=None):
        """اختبار اتصال الكاميرا بطريقة آمنة"""
        if timeout is None:
            timeout = self.connection_timeout
            
        result = {'success': False, 'message': '', 'completed': False}
        
        def test_worker():
            try:
                cap = cv2.VideoCapture()
                cap.set(cv2.CAP_PROP_OPEN_TIMEOUT_MSEC, timeout * 1000)
                cap.set(cv2.CAP_PROP_READ_TIMEOUT_MSEC, 3000)
                
                if not cap.open(rtsp_url):
                    result['success'] = False
                    result['message'] = "فشل في فتح الاتصال"
                else:
                    ret, frame = cap.read()
                    if ret and frame is not None:
                        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                        result['success'] = True
                        result['message'] = f"متصل - {width}x{height}"
                    else:
                        result['success'] = False
                        result['message'] = "لا يوجد إشارة فيديو"
                
                cap.release()
                
            except Exception as e:
                result['success'] = False
                result['message'] = f"خطأ: {str(e)}"
            finally:
                result['completed'] = True
        
        # تشغيل الاختبار في thread منفصل
        test_thread = threading.Thread(target=test_worker, daemon=True)
        test_thread.start()
        
        # انتظار النتيجة مع timeout
        start_time = time.time()
        while not result['completed'] and (time.time() - start_time) < timeout + 2:
            time.sleep(0.1)
        
        if not result['completed']:
            return False, "انتهت مهلة الاختبار"
        
        return result['success'], result['message']
    
    def add_camera(self, name, rtsp_url, username="", password="", test_first=False):
        """إضافة كاميرا بطريقة آمنة"""
        try:
            # التحقق من البيانات
            if not name or not rtsp_url:
                return False, "اسم الكاميرا ورابط RTSP مطلوبان"
            
            # تنظيف البيانات
            name = name.strip()
            rtsp_url = rtsp_url.strip()
            username = username.strip()
            password = password.strip()
            
            # اختبار الاتصال إذا طُلب
            if test_first:
                print("🔍 اختبار الاتصال...")
                success, message = self.test_camera_connection(rtsp_url)
                if not success:
                    return False, f"فشل اختبار الاتصال: {message}"
                print(f"✅ {message}")
            
            # الاتصال بقاعدة البيانات
            conn = sqlite3.connect(self.db_path, timeout=15)
            cursor = conn.cursor()
            
            # التحقق من عدم التكرار
            cursor.execute("SELECT id FROM cameras WHERE name = ?", (name,))
            if cursor.fetchone():
                conn.close()
                return False, "اسم الكاميرا موجود مسبقاً"
            
            # الإضافة
            cursor.execute('''
                INSERT INTO cameras (name, rtsp_url, username, password, is_active, created_at)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (name, rtsp_url, username, password, 1, datetime.now()))
            
            camera_id = cursor.lastrowid
            conn.commit()
            conn.close()
            
            return True, f"تم إضافة الكاميرا بنجاح (ID: {camera_id})"
            
        except Exception as e:
            return False, f"خطأ في الإضافة: {str(e)}"
    
    def list_cameras(self):
        """عرض قائمة الكاميرات"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("SELECT id, name, rtsp_url, is_active FROM cameras ORDER BY id")
            cameras = cursor.fetchall()
            
            conn.close()
            
            if not cameras:
                print("لا توجد كاميرات مضافة")
                return
            
            print("\\n📹 قائمة الكاميرات:")
            print("-" * 50)
            for camera_id, name, rtsp_url, is_active in cameras:
                status = "🟢 نشط" if is_active else "🔴 غير نشط"
                print(f"{camera_id:2d}. {name} - {status}")
                print(f"    {rtsp_url}")
            
        except Exception as e:
            print(f"خطأ في عرض الكاميرات: {e}")

def main():
    manager = UltimateSafeCameraManager()
    
    while True:
        print("\\n" + "=" * 50)
        print("📹 أداة إضافة الكاميرات الآمنة النهائية")
        print("=" * 50)
        print("1. إضافة كاميرا واحدة")
        print("2. إضافة كاميرات XVR (8 كاميرات)")
        print("3. عرض قائمة الكاميرات")
        print("4. اختبار كاميرا")
        print("5. خروج")
        
        choice = input("\\nاختيارك (1-5): ").strip()
        
        if choice == "1":
            print("\\n📹 إضافة كاميرا جديدة")
            print("-" * 30)
            
            name = input("اسم الكاميرا: ").strip()
            if not name:
                print("❌ اسم الكاميرا مطلوب")
                continue
            
            rtsp_url = input("رابط RTSP: ").strip()
            if not rtsp_url:
                print("❌ رابط RTSP مطلوب")
                continue
            
            username = input("اسم المستخدم (اختياري): ").strip()
            password = input("كلمة المرور (اختياري): ").strip()
            
            test_first = input("اختبار الاتصال أولاً؟ (y/n): ").lower() in ['y', 'yes', 'نعم']
            
            success, message = manager.add_camera(name, rtsp_url, username, password, test_first)
            
            if success:
                print(f"✅ {message}")
            else:
                print(f"❌ {message}")
        
        elif choice == "2":
            print("\\n🔧 إضافة كاميرات XVR")
            print("-" * 30)
            
            ip = input("IP جهاز XVR (افتراضي: **************): ").strip()
            if not ip:
                ip = "**************"
            
            username = input("اسم المستخدم (افتراضي: admin): ").strip()
            if not username:
                username = "admin"
            
            password = input("كلمة المرور (افتراضي: Mnbv@1978): ").strip()
            if not password:
                password = "Mnbv@1978"
            
            added = 0
            for channel in range(1, 9):
                name = f"XVR قناة {channel}"
                rtsp_url = f"rtsp://{username}:{password}@{ip}:554/cam/realmonitor?channel={channel}&subtype=0"
                
                success, message = manager.add_camera(name, rtsp_url, username, password, False)
                
                if success:
                    print(f"✅ {name}")
                    added += 1
                else:
                    print(f"❌ {name}: {message}")
            
            print(f"\\n📊 تم إضافة {added}/8 كاميرات")
        
        elif choice == "3":
            manager.list_cameras()
        
        elif choice == "4":
            rtsp_url = input("\\nرابط RTSP للاختبار: ").strip()
            if rtsp_url:
                print("🔍 اختبار الاتصال...")
                success, message = manager.test_camera_connection(rtsp_url)
                print(f"{'✅' if success else '❌'} {message}")
        
        elif choice == "5":
            print("👋 وداعاً!")
            break
        
        else:
            print("❌ خيار غير صحيح")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\\n⏹️ تم الإيقاف")
    except Exception as e:
        print(f"\\n❌ خطأ: {e}")
'''
    
    try:
        with open('ultimate_safe_camera_tool.py', 'w', encoding='utf-8') as f:
            f.write(tool_content)
        print("✅ تم إنشاء الأداة النهائية")
        return True
    except Exception as e:
        print(f"❌ فشل في إنشاء الأداة: {e}")
        return False

def test_final_system():
    """اختبار النظام النهائي"""
    print("🧪 اختبار النظام النهائي...")
    
    try:
        # اختبار قاعدة البيانات
        conn = sqlite3.connect('database.db')
        cursor = conn.cursor()
        
        cursor.execute("SELECT COUNT(*) FROM cameras")
        camera_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT name FROM cameras LIMIT 3")
        sample_cameras = cursor.fetchall()
        
        conn.close()
        
        print(f"✅ قاعدة البيانات: {camera_count} كاميرا")
        
        if sample_cameras:
            print("📹 عينة من الكاميرات:")
            for camera in sample_cameras:
                print(f"   • {camera[0]}")
        
        # اختبار OpenCV
        cap = cv2.VideoCapture()
        cap.release()
        print("✅ OpenCV يعمل")
        
        return True
        
    except Exception as e:
        print(f"❌ فشل الاختبار: {e}")
        return False

def main():
    """الوظيفة الرئيسية"""
    print_solution_banner()
    
    print("🎯 هذا الحل النهائي سيقوم بـ:")
    print("1. إصلاح قاعدة البيانات مع نسخة احتياطية")
    print("2. إضافة 8 كاميرات XVR بطريقة آمنة")
    print("3. إنشاء أداة إضافة كاميرات نهائية")
    print("4. اختبار شامل للنظام")
    print()
    
    confirm = input("هل تريد تطبيق الحل النهائي؟ (y/n): ").lower()
    if confirm not in ['y', 'yes', 'نعم']:
        print("❌ تم إلغاء الحل")
        return
    
    print("\n🚀 بدء تطبيق الحل النهائي...")
    
    # الخطوات
    steps = [
        ("إصلاح قاعدة البيانات النهائي", fix_database_final),
        ("إضافة كاميرات XVR", add_all_xvr_cameras),
        ("إنشاء الأداة النهائية", create_ultimate_safe_camera_tool),
        ("اختبار النظام النهائي", test_final_system)
    ]
    
    completed = 0
    
    for step_name, step_function in steps:
        print(f"\n🔄 {step_name}...")
        try:
            if step_function():
                print(f"✅ {step_name}: نجح")
                completed += 1
            else:
                print(f"⚠️ {step_name}: فشل جزئي")
        except Exception as e:
            print(f"❌ {step_name}: خطأ - {e}")
    
    # النتائج النهائية
    print("\n" + "=" * 60)
    print("🎊 نتائج الحل النهائي:")
    print("=" * 60)
    print(f"✅ تم إكمال {completed}/{len(steps)} خطوات")
    
    if completed >= 3:
        print("\n🎉 تم حل المشكلة نهائياً!")
        
        print("\n📋 الأدوات الجديدة:")
        print("• ultimate_safe_camera_tool.py - الأداة النهائية لإضافة الكاميرات")
        
        print("\n🛡️ الحماية المطبقة:")
        print("• معالجة آمنة للأخطاء")
        print("• timeout محدد للاتصالات")
        print("• اختبار الكاميرات في threads منفصلة")
        print("• نسخة احتياطية لقاعدة البيانات")
        print("• تنظيف البيانات المدخلة")
        
        print("\n🚀 للاستخدام الآمن:")
        print("• python ultimate_safe_camera_tool.py - للإضافة الآمنة")
        print("• python main.py - لتشغيل النظام")
        
        # خيار تشغيل الأداة النهائية
        run_tool = input("\nتشغيل الأداة النهائية الآن؟ (y/n): ").lower()
        if run_tool in ['y', 'yes', 'نعم']:
            print("\n🚀 تشغيل الأداة النهائية...")
            try:
                subprocess.run([sys.executable, 'ultimate_safe_camera_tool.py'])
            except Exception as e:
                print(f"❌ فشل التشغيل: {e}")
                print("💡 شغل الأداة يدوياً: python ultimate_safe_camera_tool.py")
        
    else:
        print("\n⚠️ الحل غير مكتمل")
        print("💡 راجع الأخطاء أعلاه")
        print("💡 أو اتصل بالدعم الفني")

if __name__ == "__main__":
    try:
        main()
        input("\nاضغط Enter للخروج...")
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الحل")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        input("اضغط Enter للخروج...")
