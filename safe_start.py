#!/usr/bin/env python3
"""
مشغل آمن نهائي لنظام مراقبة الكاميرات
Final Safe Launcher for Camera Monitoring System
"""

import sys
import os
import traceback
import time

def print_startup_banner():
    """طباعة شعار البدء"""
    print("=" * 60)
    print("🎥 نظام مراقبة الكاميرات - المشغل الآمن")
    print("Camera Monitoring System - Safe Launcher")
    print("=" * 60)
    print()

def check_prerequisites():
    """فحص المتطلبات الأساسية"""
    print("🔍 فحص المتطلبات الأساسية...")
    
    errors = []
    
    # فحص Python
    if sys.version_info < (3, 7):
        errors.append("Python 3.7+ مطلوب")
    else:
        print(f"✅ Python: {sys.version.split()[0]}")
    
    # فحص المكتبات الأساسية
    required_modules = ['cv2', 'PyQt5', 'numpy', 'sqlite3']
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module}: متوفر")
        except ImportError:
            errors.append(f"المكتبة {module} مفقودة")
    
    # فحص الملفات المطلوبة
    required_files = ['main.py', 'config.json']
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}: موجود")
        else:
            errors.append(f"الملف {file} مفقود")
    
    return errors

def setup_environment():
    """إعداد البيئة"""
    print("\n⚙️ إعداد البيئة...")
    
    try:
        # إضافة المسار الحالي
        current_dir = os.path.dirname(os.path.abspath(__file__))
        if current_dir not in sys.path:
            sys.path.insert(0, current_dir)
        
        print(f"📁 مسار العمل: {current_dir}")
        
        # إنشاء المجلدات المطلوبة
        required_dirs = ['recordings', 'assets', 'logs']
        
        for directory in required_dirs:
            if not os.path.exists(directory):
                os.makedirs(directory)
                print(f"📁 تم إنشاء مجلد: {directory}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إعداد البيئة: {str(e)}")
        return False

def test_database():
    """اختبار قاعدة البيانات"""
    print("\n🗄️ اختبار قاعدة البيانات...")
    
    try:
        from core.database import DatabaseManager
        
        db = DatabaseManager()
        
        # اختبار المصادقة
        result = db.authenticate_user('admin', 'admin123')
        
        if result:
            print("✅ قاعدة البيانات تعمل بشكل صحيح")
            return True
        else:
            print("⚠️ مشكلة في المصادقة")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {str(e)}")
        return False

def safe_import_ui():
    """استيراد آمن لمكونات الواجهة"""
    print("\n🖥️ تحميل مكونات الواجهة...")
    
    try:
        # استيراد PyQt5
        from PyQt5.QtWidgets import QApplication, QMessageBox
        from PyQt5.QtCore import Qt
        from PyQt5.QtGui import QFont
        
        print("✅ PyQt5: تم التحميل")
        
        # استيراد مكونات النظام
        from ui.login_window import LoginWindow
        print("✅ نافذة تسجيل الدخول: تم التحميل")
        
        from ui.main_window import MainWindow
        print("✅ النافذة الرئيسية: تم التحميل")
        
        return True, (QApplication, QMessageBox, LoginWindow, MainWindow, QFont)
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد الواجهة: {str(e)}")
        return False, None
    except Exception as e:
        print(f"❌ خطأ غير متوقع في الواجهة: {str(e)}")
        return False, None

def create_application():
    """إنشاء تطبيق PyQt5"""
    print("\n🚀 إنشاء التطبيق...")
    
    try:
        success, components = safe_import_ui()
        
        if not success:
            return None, None, None, None
        
        QApplication, QMessageBox, LoginWindow, MainWindow, QFont = components
        
        # إنشاء التطبيق
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # تعيين خط يدعم العربية
        font = QFont("Arial", 10)
        app.setFont(font)
        
        # تعيين خصائص التطبيق
        app.setApplicationName("Camera Monitoring System")
        app.setApplicationVersion("1.0")
        
        print("✅ تم إنشاء التطبيق بنجاح")
        
        return app, QMessageBox, LoginWindow, MainWindow
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء التطبيق: {str(e)}")
        return None, None, None, None

def run_login_process(app, QMessageBox, LoginWindow, MainWindow):
    """تشغيل عملية تسجيل الدخول"""
    print("\n🔐 بدء عملية تسجيل الدخول...")
    
    try:
        # إنشاء نافذة تسجيل الدخول
        login_window = LoginWindow()
        
        print("✅ تم إنشاء نافذة تسجيل الدخول")
        
        # عرض نافذة تسجيل الدخول
        if login_window.exec_() == LoginWindow.Accepted:
            print("✅ تم تسجيل الدخول بنجاح")
            
            # الحصول على معلومات المستخدم
            user_info = login_window.user_manager.get_current_user()
            
            if user_info:
                print(f"👤 المستخدم: {user_info['username']} ({user_info['role']})")
                
                # إنشاء النافذة الرئيسية
                main_window = MainWindow()
                main_window.set_current_user(user_info)
                
                print("✅ تم إنشاء النافذة الرئيسية")
                
                # عرض النافذة الرئيسية
                main_window.show()
                
                print("🎉 تم تشغيل النظام بنجاح!")
                
                # تشغيل حلقة الأحداث
                return app.exec_()
            else:
                print("❌ فشل في الحصول على معلومات المستخدم")
                return 1
        else:
            print("ℹ️ تم إلغاء تسجيل الدخول")
            return 0
            
    except Exception as e:
        print(f"❌ خطأ في عملية تسجيل الدخول: {str(e)}")
        
        # عرض رسالة خطأ للمستخدم
        try:
            QMessageBox.critical(None, "خطأ في النظام", 
                               f"حدث خطأ أثناء تشغيل النظام:\n\n{str(e)}\n\n"
                               f"تفاصيل الخطأ:\n{traceback.format_exc()}")
        except:
            pass
        
        return 1

def show_error_dialog(title, message):
    """عرض رسالة خطأ"""
    try:
        from PyQt5.QtWidgets import QApplication, QMessageBox
        
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        QMessageBox.critical(None, title, message)
        
    except:
        print(f"\n❌ {title}")
        print(f"📋 {message}")

def main():
    """الوظيفة الرئيسية"""
    try:
        # طباعة شعار البدء
        print_startup_banner()
        
        # فحص المتطلبات الأساسية
        errors = check_prerequisites()
        
        if errors:
            error_msg = "المتطلبات التالية مفقودة:\n" + "\n".join(f"• {error}" for error in errors)
            error_msg += "\n\nيرجى تشغيل: pip install -r requirements.txt"
            
            show_error_dialog("متطلبات مفقودة", error_msg)
            return 1
        
        # إعداد البيئة
        if not setup_environment():
            show_error_dialog("خطأ في الإعداد", "فشل في إعداد بيئة التشغيل")
            return 1
        
        # اختبار قاعدة البيانات
        if not test_database():
            print("⚠️ مشكلة في قاعدة البيانات، محاولة الإصلاح...")
            
            try:
                # تشغيل أداة إصلاح قاعدة البيانات
                import subprocess
                result = subprocess.run([sys.executable, 'database_fix.py'], 
                                      capture_output=True, text=True, timeout=30)
                
                if result.returncode == 0:
                    print("✅ تم إصلاح قاعدة البيانات")
                    
                    # إعادة اختبار
                    if not test_database():
                        show_error_dialog("خطأ في قاعدة البيانات", "فشل في إصلاح قاعدة البيانات")
                        return 1
                else:
                    show_error_dialog("خطأ في قاعدة البيانات", "فشل في إصلاح قاعدة البيانات")
                    return 1
                    
            except Exception as e:
                show_error_dialog("خطأ في قاعدة البيانات", f"فشل في إصلاح قاعدة البيانات:\n{str(e)}")
                return 1
        
        # إنشاء التطبيق
        app, QMessageBox, LoginWindow, MainWindow = create_application()
        
        if app is None:
            show_error_dialog("خطأ في التطبيق", "فشل في إنشاء تطبيق PyQt5")
            return 1
        
        # تشغيل عملية تسجيل الدخول
        return run_login_process(app, QMessageBox, LoginWindow, MainWindow)
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف التطبيق بواسطة المستخدم")
        return 0
        
    except Exception as e:
        error_msg = f"خطأ غير متوقع:\n{str(e)}\n\nتفاصيل الخطأ:\n{traceback.format_exc()}"
        show_error_dialog("خطأ غير متوقع", error_msg)
        return 1

if __name__ == "__main__":
    exit_code = main()
    
    # إذا كان هناك خطأ، انتظر قبل الإغلاق
    if exit_code != 0:
        try:
            input("\nاضغط Enter للخروج...")
        except:
            time.sleep(3)
    
    sys.exit(exit_code)
