import cv2
import numpy as np
from datetime import datetime
import threading
import time

class MotionDetector:
    def __init__(self, threshold=25, min_area=500, blur_size=21):
        self.threshold = threshold
        self.min_area = min_area
        self.blur_size = blur_size
        self.background_subtractor = cv2.createBackgroundSubtractorMOG2(
            detectShadows=True, varThreshold=50, history=500
        )
        self.previous_frame = None
        self.motion_detected = False
        self.motion_callback = None
        self.last_motion_time = None
        self.motion_cooldown = 2  # seconds
        
    def set_motion_callback(self, callback):
        """Set callback function for motion detection"""
        self.motion_callback = callback
    
    def detect_motion(self, frame):
        """Detect motion in frame"""
        if frame is None:
            return False, None
        
        # Convert to grayscale
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        
        # Apply Gaussian blur
        blurred = cv2.GaussianBlur(gray, (self.blur_size, self.blur_size), 0)
        
        # Initialize background if first frame
        if self.previous_frame is None:
            self.previous_frame = blurred
            return False, frame
        
        # Calculate absolute difference
        frame_diff = cv2.absdiff(self.previous_frame, blurred)
        
        # Apply threshold
        thresh = cv2.threshold(frame_diff, self.threshold, 255, cv2.THRESH_BINARY)[1]
        
        # Dilate to fill holes
        thresh = cv2.dilate(thresh, None, iterations=2)
        
        # Find contours
        contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        motion_detected = False
        motion_areas = []
        
        # Check each contour
        for contour in contours:
            area = cv2.contourArea(contour)
            if area > self.min_area:
                motion_detected = True
                motion_areas.append(contour)
                
                # Draw bounding rectangle
                x, y, w, h = cv2.boundingRect(contour)
                cv2.rectangle(frame, (x, y), (x + w, y + h), (0, 255, 0), 2)
                cv2.putText(frame, f"Motion: {int(area)}", (x, y - 10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
        
        # Update motion status
        current_time = time.time()
        if motion_detected:
            if (self.last_motion_time is None or 
                current_time - self.last_motion_time > self.motion_cooldown):
                
                self.motion_detected = True
                self.last_motion_time = current_time
                
                # Call motion callback
                if self.motion_callback:
                    confidence = min(100, sum(cv2.contourArea(c) for c in motion_areas) / 1000)
                    self.motion_callback(confidence, motion_areas)
            
        else:
            self.motion_detected = False
        
        # Update previous frame
        self.previous_frame = blurred
        
        return motion_detected, frame
    
    def detect_motion_background_subtraction(self, frame):
        """Alternative motion detection using background subtraction"""
        if frame is None:
            return False, None
        
        # Apply background subtraction
        fg_mask = self.background_subtractor.apply(frame)
        
        # Remove noise
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        fg_mask = cv2.morphologyEx(fg_mask, cv2.MORPH_OPEN, kernel)
        
        # Find contours
        contours, _ = cv2.findContours(fg_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        motion_detected = False
        motion_areas = []
        
        for contour in contours:
            area = cv2.contourArea(contour)
            if area > self.min_area:
                motion_detected = True
                motion_areas.append(contour)
                
                # Draw bounding rectangle
                x, y, w, h = cv2.boundingRect(contour)
                cv2.rectangle(frame, (x, y), (x + w, y + h), (0, 255, 0), 2)
        
        # Update motion status
        current_time = time.time()
        if motion_detected:
            if (self.last_motion_time is None or 
                current_time - self.last_motion_time > self.motion_cooldown):
                
                self.motion_detected = True
                self.last_motion_time = current_time
                
                if self.motion_callback:
                    confidence = min(100, sum(cv2.contourArea(c) for c in motion_areas) / 1000)
                    self.motion_callback(confidence, motion_areas)
        else:
            self.motion_detected = False
        
        return motion_detected, frame
    
    def reset(self):
        """Reset motion detector"""
        self.previous_frame = None
        self.motion_detected = False
        self.last_motion_time = None
        self.background_subtractor = cv2.createBackgroundSubtractorMOG2(
            detectShadows=True, varThreshold=50, history=500
        )
    
    def set_sensitivity(self, threshold, min_area=None):
        """Update motion detection sensitivity"""
        self.threshold = threshold
        if min_area is not None:
            self.min_area = min_area
    
    def get_motion_status(self):
        """Get current motion detection status"""
        return {
            'motion_detected': self.motion_detected,
            'last_motion_time': self.last_motion_time,
            'threshold': self.threshold,
            'min_area': self.min_area
        }
