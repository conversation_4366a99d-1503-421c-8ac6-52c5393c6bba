#!/usr/bin/env python3
"""
إضافة سريعة لجهاز XVR
Quick Add XVR Device
"""

import sqlite3
import sys
import os

def quick_add_xvr():
    """إضافة سريعة للجهاز"""
    print("🚀 إضافة سريعة لجهاز XVR...")
    
    # معلومات الجهاز
    ip = "**************"
    username = "admin"
    password = "Mnbv@1978"
    device_name = "XVR"
    
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect('database.db')
        cursor = conn.cursor()
        
        # إضافة 8 كاميرات (قنوات)
        for channel in range(1, 9):
            camera_name = f"{device_name} - قناة {channel}"
            rtsp_url = f"rtsp://{username}:{password}@{ip}:554/cam/realmonitor?channel={channel}&subtype=0"
            
            cursor.execute('''
                INSERT OR REPLACE INTO cameras 
                (name, rtsp_url, username, password, is_active, camera_type, ip_address)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (camera_name, rtsp_url, username, password, 1, f"XVR_CH{channel}", ip))
            
            print(f"✅ تم إضافة {camera_name}")
        
        conn.commit()
        conn.close()
        
        print(f"\n🎉 تم إضافة 8 كاميرات من جهاز {ip}")
        print("🚀 يمكنك الآن تشغيل النظام: python main.py")
        
    except Exception as e:
        print(f"❌ خطأ: {e}")

if __name__ == "__main__":
    quick_add_xvr()
    input("اضغط Enter للخروج...")
