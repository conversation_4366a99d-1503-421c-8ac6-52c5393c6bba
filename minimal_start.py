#!/usr/bin/env python3
import sys, os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# حذف قاعدة البيانات القديمة
try:
    if os.path.exists('database.db'):
        os.remove('database.db')
except:
    pass

# إنشاء قاعدة بيانات جديدة
import sqlite3, hashlib
conn = sqlite3.connect('database.db')
cursor = conn.cursor()

cursor.execute('''CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT UNIQUE NOT NULL,
    password TEXT NOT NULL,
    role TEXT DEFAULT 'user'
)''')

cursor.execute('''CREATE TABLE cameras (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    url TEXT NOT NULL,
    camera_type TEXT DEFAULT 'rtsp'
)''')

cursor.execute('''CREATE TABLE motion_events (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    camera_id INTEGER,
    confidence REAL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)''')

hashed = hashlib.sha256('admin123'.encode()).hexdigest()
cursor.execute("INSERT INTO users (username, password, role) VALUES (?, ?, ?)",
              ('admin', hashed, 'admin'))

conn.commit()
conn.close()

# تشغيل النظام
try:
    from PyQt5.QtWidgets import QApplication
    from ui.login_window import LoginWindow
    from ui.main_window import MainWindow
    
    app = QApplication(sys.argv)
    login = LoginWindow()
    
    if login.exec_() == LoginWindow.Accepted:
        main_window = MainWindow()
        user_info = login.user_manager.get_current_user()
        if user_info:
            main_window.set_current_user(user_info)
        main_window.show()
        sys.exit(app.exec_())
        
except Exception as e:
    print(f"خطأ: {e}")
    input("اضغط Enter...")
