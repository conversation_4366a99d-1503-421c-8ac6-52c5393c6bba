#!/usr/bin/env python3
"""
حل فوري - Instant Fix
"""

import sys
import os

def instant_fix():
    print("⚡ حل فوري...")
    
    # 1. حذف قاعدة البيانات
    try:
        if os.path.exists('database.db'):
            os.remove('database.db')
        print("✅ حذف قاعدة البيانات")
    except:
        pass
    
    # 2. إنشاء قاعدة بيانات جديدة
    try:
        import sqlite3
        import hashlib
        
        conn = sqlite3.connect('database.db')
        cursor = conn.cursor()
        
        cursor.execute('''CREATE TABLE users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            password TEXT NOT NULL,
            role TEXT DEFAULT 'user'
        )''')
        
        cursor.execute('''CREATE TABLE cameras (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            url TEXT NOT NULL,
            camera_type TEXT DEFAULT 'rtsp'
        )''')
        
        cursor.execute('''CREATE TABLE motion_events (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            camera_id INTEGER,
            confidence REAL,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )''')
        
        # إضافة admin
        hashed = hashlib.sha256('admin123'.encode()).hexdigest()
        cursor.execute("INSERT INTO users (username, password, role) VALUES (?, ?, ?)",
                      ('admin', hashed, 'admin'))
        
        conn.commit()
        conn.close()
        print("✅ قاعدة بيانات جديدة")
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False
    
    # 3. تشغيل النظام
    try:
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        from PyQt5.QtWidgets import QApplication
        from ui.login_window import LoginWindow
        from ui.main_window import MainWindow
        
        app = QApplication(sys.argv)
        
        login = LoginWindow()
        if login.exec_() == LoginWindow.Accepted:
            main_window = MainWindow()
            user_info = login.user_manager.get_current_user()
            if user_info:
                main_window.set_current_user(user_info)
            main_window.show()
            return app.exec_()
        
        return 0
        
    except Exception as e:
        print(f"❌ خطأ في التشغيل: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(instant_fix())
