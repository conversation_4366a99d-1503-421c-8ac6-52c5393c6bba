#!/usr/bin/env python3
"""
Demo setup script for Camera Monitoring System
Creates sample cameras and users for demonstration
"""

import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.database import DatabaseManager
from utils.config import Config<PERSON>anager

def setup_demo_data():
    """Setup demo cameras and users"""
    print("Setting up demo data...")
    
    # Initialize database
    db = DatabaseManager()
    
    # Add demo cameras
    demo_cameras = [
        {
            'name': 'Front Door Camera',
            'url': 'rtsp://demo:<EMAIL>:554/stream1',
            'type': 'rtsp'
        },
        {
            'name': 'Parking Lot Camera',
            'url': 'rtsp://demo:<EMAIL>:554/stream2',
            'type': 'rtsp'
        },
        {
            'name': 'Office Camera',
            'url': 'http://demo.com:8080/video',
            'type': 'ip'
        },
        {
            'name': 'USB Webcam',
            'url': '0',
            'type': 'usb'
        }
    ]
    
    print("Adding demo cameras...")
    for camera in demo_cameras:
        try:
            camera_id = db.add_camera(camera['name'], camera['url'], camera['type'])
            print(f"✓ Added camera: {camera['name']} (ID: {camera_id})")
        except Exception as e:
            print(f"✗ Failed to add camera {camera['name']}: {e}")
    
    # Add demo users
    demo_users = [
        {'username': 'operator', 'password': 'operator123', 'role': 'operator'},
        {'username': 'viewer', 'password': 'viewer123', 'role': 'user'}
    ]
    
    print("\nAdding demo users...")
    for user in demo_users:
        try:
            success = db.add_user(user['username'], user['password'], user['role'])
            if success:
                print(f"✓ Added user: {user['username']} ({user['role']})")
            else:
                print(f"✗ User {user['username']} already exists")
        except Exception as e:
            print(f"✗ Failed to add user {user['username']}: {e}")
    
    print("\nDemo setup completed!")
    print("\nLogin credentials:")
    print("- Admin: admin / admin123")
    print("- Operator: operator / operator123")
    print("- Viewer: viewer / viewer123")

def create_sample_config():
    """Create sample configuration with demo settings"""
    config = ConfigManager()
    
    # Update configuration for demo
    config.set('app_settings.auto_record', True)
    config.set('app_settings.motion_sensitivity', 30)
    config.set('recording_settings.duration_minutes', 30)
    config.set('motion_detection.threshold', 20)
    
    print("✓ Updated configuration for demo")

def show_usage_instructions():
    """Show usage instructions"""
    print("\n" + "="*60)
    print("CAMERA MONITORING SYSTEM - DEMO SETUP COMPLETE")
    print("="*60)
    print()
    print("GETTING STARTED:")
    print("1. Run the application: python main.py")
    print("2. Login with: admin / admin123")
    print("3. The system includes 4 demo cameras")
    print("4. Right-click on cameras for options")
    print("5. Use toolbar buttons for management")
    print()
    print("DEMO CAMERAS:")
    print("- Front Door Camera (RTSP demo)")
    print("- Parking Lot Camera (RTSP demo)")
    print("- Office Camera (IP demo)")
    print("- USB Webcam (will work if you have a webcam)")
    print()
    print("FEATURES TO TRY:")
    print("- Camera management (add/edit/delete)")
    print("- Motion detection settings")
    print("- Manual recording")
    print("- Full-screen mode (F11)")
    print("- User management (admin only)")
    print()
    print("NOTES:")
    print("- Demo RTSP/IP cameras won't connect (demo URLs)")
    print("- USB camera will work if webcam is connected")
    print("- All recordings saved to 'recordings/' folder")
    print("- Database file: database.db")
    print("="*60)

def main():
    """Main demo setup"""
    print("Camera Monitoring System - Demo Setup")
    print("="*50)
    
    try:
        # Setup demo data
        setup_demo_data()
        
        # Create sample config
        create_sample_config()
        
        # Show instructions
        show_usage_instructions()
        
        return 0
        
    except Exception as e:
        print(f"Demo setup failed: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
