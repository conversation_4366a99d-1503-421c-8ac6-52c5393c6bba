#!/usr/bin/env python3
"""
تفعيل الميزات المتقدمة
Activate Advanced Features
- كشف الحركة المتقدم
- التعرف على الوجوه
- قراءة أرقام السيارات
- الإعدادات الذكية
"""

import sys
import os
import sqlite3
import json
from datetime import datetime

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def print_banner():
    """طباعة شعار التفعيل"""
    print("=" * 70)
    print("🚀 تفعيل الميزات المتقدمة لنظام مراقبة الكاميرات")
    print("🚀 Activating Advanced Camera Monitoring Features")
    print("=" * 70)
    print()
    print("✨ الميزات التي سيتم تفعيلها:")
    print("🎯 كشف الحركة المتقدم - Advanced Motion Detection")
    print("👤 التعرف على الوجوه - Face Recognition")
    print("🚗 قراءة أرقام السيارات - License Plate Recognition")
    print("🧠 تحليل السلوك الذكي - Smart Behavior Analysis")
    print("🔔 الإشعارات الذكية - Smart Notifications")
    print("☁️ التخزين السحابي - Cloud Storage")
    print()

def create_advanced_config():
    """إنشاء ملف الإعدادات المتقدمة"""
    print("⚙️ إنشاء ملف الإعدادات المتقدمة...")
    
    advanced_config = {
        "system_info": {
            "version": "2.0",
            "activated_date": datetime.now().isoformat(),
            "features_enabled": True
        },
        "motion_detection": {
            "enabled": True,
            "sensitivity": 0.3,
            "min_area": 500,
            "blur_size": 21,
            "threshold": 25,
            "dilate_iterations": 2,
            "record_on_motion": True,
            "alert_on_motion": True,
            "ignore_small_movements": True,
            "detection_zones": []
        },
        "face_recognition": {
            "enabled": True,
            "model": "hog",  # hog أو cnn
            "confidence_threshold": 0.6,
            "detection_frequency": 2,
            "save_unknown_faces": True,
            "alert_on_unknown": True,
            "alert_on_known": False,
            "face_database_path": "face_database/",
            "unknown_faces_path": "unknown_faces/"
        },
        "license_plate_recognition": {
            "enabled": True,
            "confidence_threshold": 0.7,
            "min_plate_width": 80,
            "min_plate_height": 20,
            "save_plate_images": True,
            "alert_on_detection": True,
            "whitelist_enabled": False,
            "blacklist_enabled": True,
            "plates_database_path": "plates_database/",
            "ocr_language": "eng+ara"
        },
        "behavior_analysis": {
            "enabled": True,
            "loitering_detection": True,
            "loitering_time_threshold": 300,
            "running_detection": True,
            "running_speed_threshold": 5.0,
            "crowd_detection": True,
            "crowd_threshold": 5,
            "zone_violation_detection": True,
            "direction_analysis": True
        },
        "smart_notifications": {
            "enabled": True,
            "channels": {
                "desktop": True,
                "sound": True,
                "email": False,
                "sms": False,
                "webhook": False
            },
            "filters": {
                "min_importance": 1,
                "max_per_hour": 10,
                "quiet_hours_enabled": False,
                "quiet_start": "22:00",
                "quiet_end": "08:00"
            }
        },
        "cloud_storage": {
            "enabled": False,
            "auto_upload": False,
            "upload_important_only": True,
            "compression": True,
            "encryption": True,
            "provider": "local"
        },
        "recording": {
            "enabled": True,
            "quality": "high",
            "fps": 15,
            "duration_minutes": 5,
            "keep_days": 30,
            "motion_pre_record": 5,
            "motion_post_record": 10
        },
        "performance": {
            "frame_skip": 2,
            "max_cameras_simultaneous": 8,
            "cpu_usage_limit": 80,
            "memory_usage_limit": 70,
            "auto_optimize": True
        }
    }
    
    try:
        with open('advanced_config.json', 'w', encoding='utf-8') as f:
            json.dump(advanced_config, f, indent=2, ensure_ascii=False)
        print("✅ تم إنشاء ملف الإعدادات المتقدمة: advanced_config.json")
        return True
    except Exception as e:
        print(f"❌ خطأ في إنشاء الإعدادات: {e}")
        return False

def setup_database_tables():
    """إعداد جداول قاعدة البيانات للميزات المتقدمة"""
    print("🗄️ إعداد جداول قاعدة البيانات...")
    
    try:
        conn = sqlite3.connect('database.db')
        cursor = conn.cursor()
        
        # جدول كشف الحركة
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS motion_detections (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                camera_id INTEGER,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                motion_area INTEGER,
                confidence REAL,
                image_path TEXT,
                video_path TEXT,
                FOREIGN KEY (camera_id) REFERENCES cameras (id)
            )
        ''')
        
        # جدول التعرف على الوجوه
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS face_detections (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                camera_id INTEGER,
                person_name TEXT,
                confidence REAL,
                is_known BOOLEAN,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                image_path TEXT,
                bbox_x INTEGER,
                bbox_y INTEGER,
                bbox_width INTEGER,
                bbox_height INTEGER,
                FOREIGN KEY (camera_id) REFERENCES cameras (id)
            )
        ''')
        
        # جدول الأشخاص المعروفين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS known_persons (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                description TEXT,
                photo_path TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_seen TIMESTAMP,
                total_detections INTEGER DEFAULT 0
            )
        ''')
        
        # جدول أرقام السيارات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS license_plates (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                camera_id INTEGER,
                plate_number TEXT,
                confidence REAL,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                image_path TEXT,
                vehicle_type TEXT,
                is_whitelisted BOOLEAN DEFAULT 0,
                is_blacklisted BOOLEAN DEFAULT 0,
                FOREIGN KEY (camera_id) REFERENCES cameras (id)
            )
        ''')
        
        # جدول قائمة السيارات المسموحة/المحظورة
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS plate_lists (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                plate_number TEXT UNIQUE NOT NULL,
                list_type TEXT CHECK(list_type IN ('whitelist', 'blacklist')),
                owner_name TEXT,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول تحليل السلوك
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS behavior_events (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                camera_id INTEGER,
                event_type TEXT,
                description TEXT,
                severity INTEGER DEFAULT 1,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                bbox_x INTEGER,
                bbox_y INTEGER,
                bbox_width INTEGER,
                bbox_height INTEGER,
                resolved BOOLEAN DEFAULT 0,
                FOREIGN KEY (camera_id) REFERENCES cameras (id)
            )
        ''')
        
        # جدول الإشعارات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS notifications (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                notification_type TEXT,
                title TEXT,
                message TEXT,
                importance INTEGER DEFAULT 1,
                camera_id INTEGER,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                read_status BOOLEAN DEFAULT 0,
                channels_sent TEXT,
                FOREIGN KEY (camera_id) REFERENCES cameras (id)
            )
        ''')
        
        # جدول إعدادات النظام
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS system_settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                setting_name TEXT UNIQUE NOT NULL,
                setting_value TEXT,
                setting_type TEXT DEFAULT 'string',
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # إدراج الإعدادات الافتراضية
        default_settings = [
            ('motion_detection_enabled', 'true', 'boolean'),
            ('face_recognition_enabled', 'true', 'boolean'),
            ('license_plate_enabled', 'true', 'boolean'),
            ('behavior_analysis_enabled', 'true', 'boolean'),
            ('smart_notifications_enabled', 'true', 'boolean'),
            ('recording_enabled', 'true', 'boolean'),
            ('system_version', '2.0', 'string'),
            ('features_activated', 'true', 'boolean')
        ]
        
        for setting_name, setting_value, setting_type in default_settings:
            cursor.execute('''
                INSERT OR REPLACE INTO system_settings 
                (setting_name, setting_value, setting_type)
                VALUES (?, ?, ?)
            ''', (setting_name, setting_value, setting_type))
        
        conn.commit()
        conn.close()
        
        print("✅ تم إعداد جداول قاعدة البيانات بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إعداد قاعدة البيانات: {e}")
        return False

def create_directories():
    """إنشاء المجلدات المطلوبة"""
    print("📁 إنشاء المجلدات المطلوبة...")
    
    directories = [
        'face_database',
        'unknown_faces',
        'plates_database',
        'motion_captures',
        'behavior_events',
        'notifications_log',
        'ai_models',
        'temp_processing',
        'exports'
    ]
    
    created_count = 0
    
    for directory in directories:
        try:
            os.makedirs(directory, exist_ok=True)
            print(f"✅ {directory}/")
            created_count += 1
        except Exception as e:
            print(f"⚠️ فشل في إنشاء {directory}: {e}")
    
    print(f"📊 تم إنشاء {created_count}/{len(directories)} مجلد")
    return created_count == len(directories)

def install_required_packages():
    """تثبيت المكتبات المطلوبة للميزات المتقدمة"""
    print("📦 فحص وتثبيت المكتبات المطلوبة...")
    
    required_packages = [
        ('cv2', 'opencv-python', 'معالجة الصور والفيديو'),
        ('numpy', 'numpy', 'العمليات الرياضية'),
        ('PIL', 'Pillow', 'معالجة الصور'),
        ('sqlite3', None, 'قاعدة البيانات (مدمجة)'),
    ]
    
    optional_packages = [
        ('face_recognition', 'face-recognition', 'التعرف على الوجوه'),
        ('pytesseract', 'pytesseract', 'قراءة النصوص (OCR)'),
        ('sklearn', 'scikit-learn', 'التعلم الآلي'),
    ]
    
    print("\n🔍 فحص المكتبات الأساسية:")
    missing_required = []
    
    for package, install_name, description in required_packages:
        try:
            if package == 'sqlite3':
                import sqlite3
            else:
                __import__(package)
            print(f"✅ {description}: متوفر")
        except ImportError:
            print(f"❌ {description}: مفقود")
            if install_name:
                missing_required.append(install_name)
    
    print("\n🔍 فحص المكتبات الاختيارية:")
    missing_optional = []
    
    for package, install_name, description in optional_packages:
        try:
            __import__(package)
            print(f"✅ {description}: متوفر")
        except ImportError:
            print(f"⚠️ {description}: غير متوفر (اختياري)")
            if install_name:
                missing_optional.append(install_name)
    
    if missing_required:
        print(f"\n❌ مكتبات أساسية مفقودة: {', '.join(missing_required)}")
        print("💡 قم بتثبيتها باستخدام:")
        print(f"pip install {' '.join(missing_required)}")
        return False
    
    if missing_optional:
        print(f"\n💡 لتفعيل جميع الميزات، ثبت المكتبات الاختيارية:")
        print(f"pip install {' '.join(missing_optional)}")
    
    return True

def create_sample_data():
    """إنشاء بيانات تجريبية"""
    print("📋 إنشاء بيانات تجريبية...")
    
    try:
        conn = sqlite3.connect('database.db')
        cursor = conn.cursor()
        
        # إضافة أشخاص تجريبيين
        sample_persons = [
            ('المدير العام', 'مدير النظام الرئيسي'),
            ('موظف الأمن', 'مسؤول الأمان'),
            ('الزائر المعتمد', 'زائر له صلاحية دخول')
        ]
        
        for name, description in sample_persons:
            cursor.execute('''
                INSERT OR IGNORE INTO known_persons (name, description)
                VALUES (?, ?)
            ''', (name, description))
        
        # إضافة أرقام سيارات تجريبية
        sample_plates = [
            ('أ ب ج 123', 'whitelist', 'سيارة المدير', 'سيارة مسموحة'),
            ('د هـ و 456', 'blacklist', 'سيارة محظورة', 'سيارة غير مرغوب فيها')
        ]
        
        for plate, list_type, owner, description in sample_plates:
            cursor.execute('''
                INSERT OR IGNORE INTO plate_lists 
                (plate_number, list_type, owner_name, description)
                VALUES (?, ?, ?, ?)
            ''', (plate, list_type, owner, description))
        
        conn.commit()
        conn.close()
        
        print("✅ تم إنشاء البيانات التجريبية")
        return True
        
    except Exception as e:
        print(f"⚠️ خطأ في إنشاء البيانات التجريبية: {e}")
        return False

def create_activation_script():
    """إنشاء ملف تفعيل سريع"""
    activation_script = '''#!/usr/bin/env python3
"""
ملف التفعيل السريع للميزات المتقدمة
Quick Activation Script for Advanced Features
"""

import json
import os

def load_advanced_config():
    """تحميل الإعدادات المتقدمة"""
    try:
        with open('advanced_config.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except:
        return None

def is_features_activated():
    """فحص حالة تفعيل الميزات"""
    config = load_advanced_config()
    return config and config.get('system_info', {}).get('features_enabled', False)

def get_feature_status():
    """الحصول على حالة الميزات"""
    config = load_advanced_config()
    if not config:
        return "غير مفعل"
    
    features = []
    if config.get('motion_detection', {}).get('enabled'):
        features.append("كشف الحركة")
    if config.get('face_recognition', {}).get('enabled'):
        features.append("التعرف على الوجوه")
    if config.get('license_plate_recognition', {}).get('enabled'):
        features.append("قراءة أرقام السيارات")
    if config.get('behavior_analysis', {}).get('enabled'):
        features.append("تحليل السلوك")
    
    return f"مفعل: {', '.join(features)}" if features else "غير مفعل"

if __name__ == "__main__":
    print("🔍 حالة الميزات المتقدمة:")
    print(f"📊 الحالة: {get_feature_status()}")
'''
    
    try:
        with open('check_features.py', 'w', encoding='utf-8') as f:
            f.write(activation_script)
        print("✅ تم إنشاء ملف فحص الميزات: check_features.py")
    except Exception as e:
        print(f"⚠️ فشل في إنشاء ملف الفحص: {e}")

def main():
    """الوظيفة الرئيسية"""
    print_banner()
    
    # تأكيد التفعيل
    confirm = input("هل تريد تفعيل جميع الميزات المتقدمة؟ (y/n): ").lower()
    
    if confirm not in ['y', 'yes', 'نعم']:
        print("❌ تم إلغاء التفعيل")
        return
    
    print("\n🚀 بدء عملية التفعيل...")
    
    # الخطوات
    steps = [
        ("فحص المكتبات المطلوبة", install_required_packages),
        ("إنشاء ملف الإعدادات", create_advanced_config),
        ("إعداد قاعدة البيانات", setup_database_tables),
        ("إنشاء المجلدات", create_directories),
        ("إنشاء البيانات التجريبية", create_sample_data),
        ("إنشاء ملفات مساعدة", create_activation_script)
    ]
    
    completed_steps = 0
    
    for step_name, step_function in steps:
        print(f"\n🔄 {step_name}...")
        try:
            if step_function():
                print(f"✅ {step_name}: مكتمل")
                completed_steps += 1
            else:
                print(f"⚠️ {step_name}: فشل جزئي")
        except Exception as e:
            print(f"❌ {step_name}: فشل - {e}")
    
    # النتائج
    print("\n" + "=" * 70)
    print("📊 نتائج التفعيل:")
    print(f"✅ تم إكمال {completed_steps}/{len(steps)} خطوات")
    
    if completed_steps >= len(steps) - 1:
        print("🎉 تم تفعيل الميزات المتقدمة بنجاح!")
        print("\n🎯 الميزات المفعلة:")
        print("✅ كشف الحركة المتقدم")
        print("✅ التعرف على الوجوه")
        print("✅ قراءة أرقام السيارات")
        print("✅ تحليل السلوك الذكي")
        print("✅ الإشعارات الذكية")
        print("✅ قاعدة بيانات متقدمة")
        
        print("\n🚀 الخطوات التالية:")
        print("1. أعد تشغيل النظام: python main.py")
        print("2. افتح إعدادات النظام لتخصيص الميزات")
        print("3. أضف أشخاص معروفين في قسم التعرف على الوجوه")
        print("4. اضبط حساسية كشف الحركة حسب الحاجة")
        
    else:
        print("⚠️ تم التفعيل مع بعض المشاكل")
        print("💡 راجع الرسائل أعلاه لحل المشاكل")
    
    print("=" * 70)

if __name__ == "__main__":
    try:
        main()
        input("\nاضغط Enter للخروج...")
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف التفعيل")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        input("اضغط Enter للخروج...")
