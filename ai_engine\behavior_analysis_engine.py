#!/usr/bin/env python3
"""
محرك تحليل السلوك المتقدم
Advanced Behavior Analysis Engine
"""

import cv2
import numpy as np
import sqlite3
from datetime import datetime, timedelta
import threading
import time
import math
from collections import deque, defaultdict

class BehaviorAnalysisEngine:
    def __init__(self, database_path="database.db"):
        self.database_path = database_path
        
        # Tracking settings
        self.max_track_age = 30  # Maximum frames to keep a track
        self.min_track_length = 10  # Minimum track length for analysis
        self.max_distance = 100  # Maximum distance for track association
        
        # Behavior detection settings
        self.loitering_time_threshold = 300  # 5 minutes in seconds
        self.running_speed_threshold = 5.0  # pixels per frame
        self.direction_change_threshold = 45  # degrees
        self.crowd_density_threshold = 5  # people per area
        
        # Data structures
        self.tracks = {}  # Active tracks
        self.track_id_counter = 0
        self.behavior_events = []
        
        # Zone definitions
        self.zones = {}  # Defined zones for analysis
        self.restricted_zones = {}  # Restricted access zones
        
        # Initialize database
        self.init_behavior_database()
    
    def init_behavior_database(self):
        """Initialize behavior analysis database tables"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()
            
            # Behavior events table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS behavior_events (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    camera_id INTEGER,
                    event_type TEXT NOT NULL,
                    description TEXT,
                    severity INTEGER DEFAULT 1,
                    bbox_x INTEGER,
                    bbox_y INTEGER,
                    bbox_width INTEGER,
                    bbox_height INTEGER,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    resolved BOOLEAN DEFAULT 0,
                    FOREIGN KEY (camera_id) REFERENCES cameras (id)
                )
            ''')
            
            # Object tracks table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS object_tracks (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    camera_id INTEGER,
                    track_id INTEGER,
                    object_class TEXT,
                    start_time TIMESTAMP,
                    end_time TIMESTAMP,
                    total_distance REAL,
                    avg_speed REAL,
                    max_speed REAL,
                    direction_changes INTEGER,
                    zones_visited TEXT,
                    FOREIGN KEY (camera_id) REFERENCES cameras (id)
                )
            ''')
            
            # Zone definitions table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS analysis_zones (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    camera_id INTEGER,
                    zone_name TEXT NOT NULL,
                    zone_type TEXT DEFAULT 'normal',
                    polygon_points TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (camera_id) REFERENCES cameras (id)
                )
            ''')
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"Error initializing behavior database: {e}")
    
    def update_tracks(self, detections, camera_id=None):
        """Update object tracks with new detections"""
        current_time = time.time()
        
        # Convert detections to track format
        detection_centers = []
        for detection in detections:
            if detection['class'] == 'person':  # Focus on people for behavior analysis
                x, y, w, h = detection['bbox']
                center_x = x + w // 2
                center_y = y + h // 2
                detection_centers.append((center_x, center_y, detection))
        
        # Update existing tracks
        updated_tracks = set()
        
        for track_id, track in list(self.tracks.items()):
            # Find closest detection
            if detection_centers:
                distances = []
                for i, (cx, cy, det) in enumerate(detection_centers):
                    last_pos = track['positions'][-1]
                    dist = math.sqrt((cx - last_pos[0])**2 + (cy - last_pos[1])**2)
                    distances.append((dist, i, cx, cy, det))
                
                distances.sort()
                closest_dist, closest_idx, cx, cy, detection = distances[0]
                
                if closest_dist < self.max_distance:
                    # Update track
                    track['positions'].append((cx, cy))
                    track['timestamps'].append(current_time)
                    track['detections'].append(detection)
                    track['last_update'] = current_time
                    
                    # Keep only recent positions
                    if len(track['positions']) > self.max_track_age:
                        track['positions'].pop(0)
                        track['timestamps'].pop(0)
                        track['detections'].pop(0)
                    
                    updated_tracks.add(track_id)
                    detection_centers.pop(closest_idx)
            
            # Remove old tracks
            if current_time - track['last_update'] > 5.0:  # 5 seconds timeout
                self.finalize_track(track_id, camera_id)
                del self.tracks[track_id]
        
        # Create new tracks for unmatched detections
        for cx, cy, detection in detection_centers:
            self.track_id_counter += 1
            track_id = self.track_id_counter
            
            self.tracks[track_id] = {
                'positions': [(cx, cy)],
                'timestamps': [current_time],
                'detections': [detection],
                'start_time': current_time,
                'last_update': current_time,
                'behaviors': []
            }
    
    def analyze_behaviors(self, camera_id=None):
        """Analyze behaviors from current tracks"""
        current_time = time.time()
        behavior_events = []
        
        for track_id, track in self.tracks.items():
            if len(track['positions']) < self.min_track_length:
                continue
            
            # Analyze different behaviors
            behaviors = []
            
            # 1. Loitering detection
            loitering = self.detect_loitering(track, current_time)
            if loitering:
                behaviors.append(loitering)
            
            # 2. Running detection
            running = self.detect_running(track)
            if running:
                behaviors.append(running)
            
            # 3. Erratic movement
            erratic = self.detect_erratic_movement(track)
            if erratic:
                behaviors.append(erratic)
            
            # 4. Zone violations
            violations = self.detect_zone_violations(track, camera_id)
            behaviors.extend(violations)
            
            # 5. Crowd analysis
            crowd_events = self.analyze_crowd_behavior(track, camera_id)
            behaviors.extend(crowd_events)
            
            # Store behaviors in track
            track['behaviors'].extend(behaviors)
            behavior_events.extend(behaviors)
        
        # Log behavior events to database
        for event in behavior_events:
            self.log_behavior_event(event, camera_id)
        
        return behavior_events
    
    def detect_loitering(self, track, current_time):
        """Detect loitering behavior"""
        if len(track['positions']) < 10:
            return None
        
        # Calculate time spent in area
        time_in_area = current_time - track['start_time']
        
        if time_in_area > self.loitering_time_threshold:
            # Check if person is staying in roughly the same area
            positions = track['positions'][-20:]  # Last 20 positions
            
            if len(positions) > 5:
                # Calculate area covered
                xs = [p[0] for p in positions]
                ys = [p[1] for p in positions]
                
                area_width = max(xs) - min(xs)
                area_height = max(ys) - min(ys)
                area_covered = area_width * area_height
                
                if area_covered < 10000:  # Small area threshold
                    center_x = sum(xs) // len(xs)
                    center_y = sum(ys) // len(ys)
                    
                    return {
                        'type': 'loitering',
                        'description': f'Person loitering for {time_in_area:.1f} seconds',
                        'severity': 2,
                        'position': (center_x, center_y),
                        'duration': time_in_area
                    }
        
        return None
    
    def detect_running(self, track):
        """Detect running behavior"""
        if len(track['positions']) < 5:
            return None
        
        # Calculate recent speeds
        recent_positions = track['positions'][-5:]
        recent_timestamps = track['timestamps'][-5:]
        
        speeds = []
        for i in range(1, len(recent_positions)):
            dx = recent_positions[i][0] - recent_positions[i-1][0]
            dy = recent_positions[i][1] - recent_positions[i-1][1]
            dt = recent_timestamps[i] - recent_timestamps[i-1]
            
            if dt > 0:
                speed = math.sqrt(dx*dx + dy*dy) / dt
                speeds.append(speed)
        
        if speeds and max(speeds) > self.running_speed_threshold:
            last_pos = track['positions'][-1]
            return {
                'type': 'running',
                'description': f'Person running at {max(speeds):.1f} pixels/sec',
                'severity': 1,
                'position': last_pos,
                'speed': max(speeds)
            }
        
        return None
    
    def detect_erratic_movement(self, track):
        """Detect erratic movement patterns"""
        if len(track['positions']) < 10:
            return None
        
        positions = track['positions'][-10:]
        direction_changes = 0
        
        # Calculate direction changes
        for i in range(2, len(positions)):
            # Calculate angles
            dx1 = positions[i-1][0] - positions[i-2][0]
            dy1 = positions[i-1][1] - positions[i-2][1]
            dx2 = positions[i][0] - positions[i-1][0]
            dy2 = positions[i][1] - positions[i-1][1]
            
            if dx1 != 0 or dy1 != 0:
                angle1 = math.atan2(dy1, dx1)
            else:
                continue
                
            if dx2 != 0 or dy2 != 0:
                angle2 = math.atan2(dy2, dx2)
            else:
                continue
            
            # Calculate angle difference
            angle_diff = abs(math.degrees(angle2 - angle1))
            if angle_diff > 180:
                angle_diff = 360 - angle_diff
            
            if angle_diff > self.direction_change_threshold:
                direction_changes += 1
        
        if direction_changes > 3:  # Too many direction changes
            last_pos = track['positions'][-1]
            return {
                'type': 'erratic_movement',
                'description': f'Erratic movement detected ({direction_changes} direction changes)',
                'severity': 2,
                'position': last_pos,
                'direction_changes': direction_changes
            }
        
        return None
    
    def detect_zone_violations(self, track, camera_id):
        """Detect zone violations"""
        violations = []
        
        if camera_id not in self.restricted_zones:
            return violations
        
        last_pos = track['positions'][-1]
        
        for zone_name, zone_polygon in self.restricted_zones[camera_id].items():
            if self.point_in_polygon(last_pos, zone_polygon):
                violations.append({
                    'type': 'zone_violation',
                    'description': f'Unauthorized access to {zone_name}',
                    'severity': 3,
                    'position': last_pos,
                    'zone': zone_name
                })
        
        return violations
    
    def analyze_crowd_behavior(self, track, camera_id):
        """Analyze crowd-related behaviors"""
        events = []
        
        # Count nearby people
        last_pos = track['positions'][-1]
        nearby_count = 0
        
        for other_track in self.tracks.values():
            if other_track is track:
                continue
            
            if other_track['positions']:
                other_pos = other_track['positions'][-1]
                distance = math.sqrt((last_pos[0] - other_pos[0])**2 + (last_pos[1] - other_pos[1])**2)
                
                if distance < 100:  # Within 100 pixels
                    nearby_count += 1
        
        if nearby_count > self.crowd_density_threshold:
            events.append({
                'type': 'crowd_detected',
                'description': f'Crowd of {nearby_count + 1} people detected',
                'severity': 1,
                'position': last_pos,
                'crowd_size': nearby_count + 1
            })
        
        return events
    
    def point_in_polygon(self, point, polygon):
        """Check if point is inside polygon"""
        x, y = point
        n = len(polygon)
        inside = False
        
        p1x, p1y = polygon[0]
        for i in range(1, n + 1):
            p2x, p2y = polygon[i % n]
            if y > min(p1y, p2y):
                if y <= max(p1y, p2y):
                    if x <= max(p1x, p2x):
                        if p1y != p2y:
                            xinters = (y - p1y) * (p2x - p1x) / (p2y - p1y) + p1x
                        if p1x == p2x or x <= xinters:
                            inside = not inside
            p1x, p1y = p2x, p2y
        
        return inside
    
    def finalize_track(self, track_id, camera_id):
        """Finalize and store track data"""
        if track_id not in self.tracks:
            return
        
        track = self.tracks[track_id]
        
        if len(track['positions']) < self.min_track_length:
            return
        
        # Calculate track statistics
        total_distance = 0
        speeds = []
        direction_changes = 0
        
        positions = track['positions']
        timestamps = track['timestamps']
        
        for i in range(1, len(positions)):
            # Distance
            dx = positions[i][0] - positions[i-1][0]
            dy = positions[i][1] - positions[i-1][1]
            distance = math.sqrt(dx*dx + dy*dy)
            total_distance += distance
            
            # Speed
            dt = timestamps[i] - timestamps[i-1]
            if dt > 0:
                speed = distance / dt
                speeds.append(speed)
        
        avg_speed = sum(speeds) / len(speeds) if speeds else 0
        max_speed = max(speeds) if speeds else 0
        
        # Store track in database
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO object_tracks 
                (camera_id, track_id, object_class, start_time, end_time, 
                 total_distance, avg_speed, max_speed, direction_changes)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                camera_id, track_id, 'person', 
                datetime.fromtimestamp(track['start_time']),
                datetime.fromtimestamp(track['last_update']),
                total_distance, avg_speed, max_speed, direction_changes
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"Error storing track: {e}")
    
    def log_behavior_event(self, event, camera_id):
        """Log behavior event to database"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()
            
            x, y = event['position']
            
            cursor.execute('''
                INSERT INTO behavior_events 
                (camera_id, event_type, description, severity, bbox_x, bbox_y)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                camera_id, event['type'], event['description'], 
                event['severity'], x, y
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"Error logging behavior event: {e}")
    
    def add_zone(self, camera_id, zone_name, polygon_points, zone_type='normal'):
        """Add analysis zone"""
        if camera_id not in self.zones:
            self.zones[camera_id] = {}
        
        self.zones[camera_id][zone_name] = polygon_points
        
        if zone_type == 'restricted':
            if camera_id not in self.restricted_zones:
                self.restricted_zones[camera_id] = {}
            self.restricted_zones[camera_id][zone_name] = polygon_points
        
        # Store in database
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()
            
            points_str = ','.join([f"{p[0]},{p[1]}" for p in polygon_points])
            
            cursor.execute('''
                INSERT OR REPLACE INTO analysis_zones 
                (camera_id, zone_name, zone_type, polygon_points)
                VALUES (?, ?, ?, ?)
            ''', (camera_id, zone_name, zone_type, points_str))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"Error adding zone: {e}")
    
    def get_behavior_events(self, camera_id=None, hours=24, severity=None):
        """Get behavior events from database"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()
            
            query = '''
                SELECT event_type, description, severity, bbox_x, bbox_y, timestamp, resolved
                FROM behavior_events 
                WHERE timestamp > datetime('now', '-{} hours')
            '''.format(hours)
            
            params = []
            
            if camera_id:
                query += " AND camera_id = ?"
                params.append(camera_id)
            
            if severity:
                query += " AND severity >= ?"
                params.append(severity)
            
            query += " ORDER BY timestamp DESC"
            
            cursor.execute(query, params)
            events = cursor.fetchall()
            conn.close()
            
            return [
                {
                    'event_type': e[0],
                    'description': e[1],
                    'severity': e[2],
                    'position': (e[3], e[4]),
                    'timestamp': e[5],
                    'resolved': e[6]
                }
                for e in events
            ]
            
        except Exception as e:
            print(f"Error getting behavior events: {e}")
            return []
    
    def draw_tracks(self, frame, show_trails=True):
        """Draw tracks and behavior indicators on frame"""
        for track_id, track in self.tracks.items():
            positions = track['positions']
            
            if len(positions) < 2:
                continue
            
            # Draw trail
            if show_trails:
                for i in range(1, len(positions)):
                    cv2.line(frame, positions[i-1], positions[i], (0, 255, 255), 2)
            
            # Draw current position
            last_pos = positions[-1]
            cv2.circle(frame, last_pos, 5, (0, 255, 0), -1)
            
            # Draw track ID
            cv2.putText(frame, f"ID:{track_id}", 
                       (last_pos[0] + 10, last_pos[1] - 10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            
            # Draw behavior indicators
            for behavior in track['behaviors'][-3:]:  # Show last 3 behaviors
                if behavior['type'] == 'loitering':
                    cv2.circle(frame, last_pos, 20, (0, 0, 255), 2)
                elif behavior['type'] == 'running':
                    cv2.circle(frame, last_pos, 15, (255, 0, 0), 2)
                elif behavior['type'] == 'erratic_movement':
                    cv2.circle(frame, last_pos, 25, (255, 0, 255), 2)
        
        return frame
