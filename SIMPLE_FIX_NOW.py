#!/usr/bin/env python3
"""
حل بسيط وسريع - تشغيل فوري
Simple Quick Fix - Run Now
"""

import sqlite3
import os
import time
from datetime import datetime

def quick_fix():
    """حل سريع في 30 ثانية"""
    print("⚡ حل سريع - 30 ثانية")
    print("=" * 30)
    
    # 1. إنشاء قاعدة بيانات جديدة
    print("1️⃣ إنشاء قاعدة بيانات...")
    try:
        if os.path.exists('database.db'):
            os.rename('database.db', f'old_db_{int(time.time())}.db')
        
        conn = sqlite3.connect('database.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE users (
                id INTEGER PRIMARY KEY,
                username TEXT UNIQUE,
                password TEXT,
                role TEXT DEFAULT 'user'
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE cameras (
                id INTEGER PRIMARY KEY,
                name TEXT,
                rtsp_url TEXT,
                username TEXT DEFAULT '',
                password TEXT DEFAULT '',
                is_active BOOLEAN DEFAULT 1
            )
        ''')
        
        cursor.execute("INSERT INTO users VALUES (1, 'admin', 'admin123', 'admin')")
        
        conn.commit()
        conn.close()
        print("✅ قاعدة بيانات جديدة")
    except Exception as e:
        print(f"❌ خطأ: {e}")
    
    # 2. إضافة كاميرات XVR
    print("2️⃣ إضافة كاميرات XVR...")
    try:
        conn = sqlite3.connect('database.db')
        cursor = conn.cursor()
        
        for i in range(1, 9):
            name = f"XVR قناة {i}"
            url = f"rtsp://admin:Mnbv@1978@192.168.18.108:554/cam/realmonitor?channel={i}&subtype=0"
            
            cursor.execute('''
                INSERT INTO cameras (name, rtsp_url, username, password)
                VALUES (?, ?, 'admin', 'Mnbv@1978')
            ''', (name, url))
            
            print(f"✅ {name}")
        
        conn.commit()
        conn.close()
        print("✅ تم إضافة 8 كاميرات")
    except Exception as e:
        print(f"❌ خطأ: {e}")
    
    # 3. إنشاء أداة بسيطة
    print("3️⃣ إنشاء أداة بسيطة...")
    try:
        simple_tool = '''#!/usr/bin/env python3
import sqlite3
from datetime import datetime

def add_camera():
    name = input("اسم الكاميرا: ")
    url = input("رابط RTSP: ")
    
    if not name or not url:
        print("❌ البيانات ناقصة")
        return
    
    try:
        conn = sqlite3.connect('database.db')
        cursor = conn.cursor()
        
        cursor.execute("SELECT id FROM cameras WHERE name = ?", (name,))
        if cursor.fetchone():
            print("❌ الاسم موجود")
            return
        
        cursor.execute('''
            INSERT INTO cameras (name, rtsp_url, is_active)
            VALUES (?, ?, 1)
        ''', (name, url))
        
        conn.commit()
        conn.close()
        print("✅ تمت الإضافة")
        
    except Exception as e:
        print(f"❌ خطأ: {e}")

def list_cameras():
    try:
        conn = sqlite3.connect('database.db')
        cursor = conn.cursor()
        
        cursor.execute("SELECT id, name FROM cameras")
        cameras = cursor.fetchall()
        
        if not cameras:
            print("لا توجد كاميرات")
        else:
            print("\\nالكاميرات:")
            for cam_id, name in cameras:
                print(f"{cam_id}. {name}")
        
        conn.close()
    except Exception as e:
        print(f"خطأ: {e}")

def main():
    while True:
        print("\\n1. إضافة كاميرا")
        print("2. عرض الكاميرات") 
        print("3. خروج")
        
        choice = input("الاختيار: ")
        
        if choice == "1":
            add_camera()
        elif choice == "2":
            list_cameras()
        elif choice == "3":
            break

if __name__ == "__main__":
    main()
'''
        
        with open('simple_camera_tool.py', 'w', encoding='utf-8') as f:
            f.write(simple_tool)
        
        print("✅ أداة بسيطة: simple_camera_tool.py")
    except Exception as e:
        print(f"❌ خطأ: {e}")
    
    print("\n🎉 انتهى الحل السريع!")
    print("🚀 استخدم: python simple_camera_tool.py")

if __name__ == "__main__":
    quick_fix()
    input("\nاضغط Enter...")
