#!/usr/bin/env python3
"""
دليل حل مشاكل الكاميرات
Camera Troubleshooting Guide
"""

import sys
import os
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

class CameraTroubleshootingGuide(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة دليل حل المشاكل"""
        self.setWindowTitle("دليل حل مشاكل الكاميرات - Camera Troubleshooting Guide")
        self.setGeometry(100, 100, 1000, 700)
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        central_widget.setLayout(main_layout)
        
        # العنوان
        title = QLabel("🔧 دليل حل مشاكل الكاميرات")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 24px; font-weight: bold; margin: 15px; color: #2c3e50;")
        main_layout.addWidget(title)
        
        # شريط التبويبات
        tab_widget = QTabWidget()
        
        # تبويب أنواع الكاميرات
        camera_types_tab = self.create_camera_types_tab()
        tab_widget.addTab(camera_types_tab, "🎥 أنواع الكاميرات")
        
        # تبويب الأخطاء الشائعة
        common_errors_tab = self.create_common_errors_tab()
        tab_widget.addTab(common_errors_tab, "❌ الأخطاء الشائعة")
        
        # تبويب أمثلة الروابط
        url_examples_tab = self.create_url_examples_tab()
        tab_widget.addTab(url_examples_tab, "🔗 أمثلة الروابط")
        
        # تبويب إعدادات الشبكة
        network_settings_tab = self.create_network_settings_tab()
        tab_widget.addTab(network_settings_tab, "🌐 إعدادات الشبكة")
        
        main_layout.addWidget(tab_widget)
        
        # أزرار الأدوات
        tools_layout = QHBoxLayout()
        
        test_camera_btn = QPushButton("🧪 اختبار الكاميرا")
        test_camera_btn.clicked.connect(self.open_camera_tester)
        tools_layout.addWidget(test_camera_btn)
        
        ping_test_btn = QPushButton("📡 اختبار الشبكة")
        ping_test_btn.clicked.connect(self.ping_test)
        tools_layout.addWidget(ping_test_btn)
        
        port_scan_btn = QPushButton("🔍 فحص المنافذ")
        port_scan_btn.clicked.connect(self.port_scan)
        tools_layout.addWidget(port_scan_btn)
        
        tools_layout.addStretch()
        main_layout.addLayout(tools_layout)
        
        # تطبيق النمط
        self.apply_style()
    
    def create_camera_types_tab(self):
        """إنشاء تبويب أنواع الكاميرات"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # معلومات أنواع الكاميرات
        camera_info = QTextEdit()
        camera_info.setReadOnly(True)
        camera_info.setHtml("""
        <h2>🎥 أنواع الكاميرات المدعومة</h2>
        
        <h3>1. كاميرات RTSP</h3>
        <ul>
            <li><b>الوصف:</b> بروتوكول البث المباشر للفيديو</li>
            <li><b>المنفذ الافتراضي:</b> 554</li>
            <li><b>التشفير:</b> يدعم المصادقة</li>
            <li><b>الجودة:</b> عالية الجودة</li>
            <li><b>مثال:</b> rtsp://admin:password@*************:554/stream</li>
        </ul>
        
        <h3>2. كاميرات HTTP/IP</h3>
        <ul>
            <li><b>الوصف:</b> كاميرات الويب عبر HTTP</li>
            <li><b>المنفذ الافتراضي:</b> 80, 8080</li>
            <li><b>التنسيق:</b> MJPEG, H.264</li>
            <li><b>مثال:</b> http://*************:8080/video</li>
        </ul>
        
        <h3>3. كاميرات USB</h3>
        <ul>
            <li><b>الوصف:</b> كاميرات متصلة مباشرة بالكمبيوتر</li>
            <li><b>الرقم:</b> 0, 1, 2... (رقم الجهاز)</li>
            <li><b>السرعة:</b> سريعة جداً</li>
            <li><b>مثال:</b> 0 (للكاميرا الأولى)</li>
        </ul>
        
        <h3>4. أنظمة DVR/NVR</h3>
        <ul>
            <li><b>الوصف:</b> أنظمة تسجيل متعددة الكاميرات</li>
            <li><b>القنوات:</b> 101, 201, 301, 401...</li>
            <li><b>مثال:</b> rtsp://admin:12345@************:554/Streaming/Channels/101/</li>
        </ul>
        """)
        
        layout.addWidget(camera_info)
        widget.setLayout(layout)
        return widget
    
    def create_common_errors_tab(self):
        """إنشاء تبويب الأخطاء الشائعة"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        errors_info = QTextEdit()
        errors_info.setReadOnly(True)
        errors_info.setHtml("""
        <h2>❌ الأخطاء الشائعة وحلولها</h2>
        
        <h3>🔴 خطأ: "فشل الاتصال بالكاميرا"</h3>
        <b>الأسباب المحتملة:</b>
        <ul>
            <li>رابط الكاميرا خاطئ</li>
            <li>الكاميرا غير متصلة بالشبكة</li>
            <li>اسم المستخدم أو كلمة المرور خاطئة</li>
            <li>المنفذ مغلق أو محجوب</li>
        </ul>
        <b>الحلول:</b>
        <ul>
            <li>تحقق من عنوان IP للكاميرا</li>
            <li>اختبر الاتصال بـ ping</li>
            <li>تأكد من بيانات المصادقة</li>
            <li>تحقق من إعدادات الجدار الناري</li>
        </ul>
        
        <h3>🔴 خطأ: "انتهت مهلة الاتصال"</h3>
        <b>الأسباب:</b>
        <ul>
            <li>الشبكة بطيئة</li>
            <li>الكاميرا مشغولة</li>
            <li>مشاكل في الراوتر</li>
        </ul>
        <b>الحلول:</b>
        <ul>
            <li>زيادة مهلة الاتصال</li>
            <li>إعادة تشغيل الكاميرا</li>
            <li>فحص كابل الشبكة</li>
        </ul>
        
        <h3>🔴 خطأ: "تنسيق الفيديو غير مدعوم"</h3>
        <b>الحلول:</b>
        <ul>
            <li>تحديث برامج التشغيل</li>
            <li>تغيير إعدادات الكاميرا</li>
            <li>استخدام برنامج VLC للاختبار</li>
        </ul>
        
        <h3>🔴 خطأ: "الصورة متقطعة أو بطيئة"</h3>
        <b>الحلول:</b>
        <ul>
            <li>تقليل جودة الفيديو</li>
            <li>تحسين إعدادات الشبكة</li>
            <li>إغلاق البرامج الأخرى</li>
            <li>استخدام كابل شبكة بدلاً من WiFi</li>
        </ul>
        """)
        
        layout.addWidget(errors_info)
        widget.setLayout(layout)
        return widget
    
    def create_url_examples_tab(self):
        """إنشاء تبويب أمثلة الروابط"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # قائمة أمثلة الروابط
        examples_list = QListWidget()
        
        # إضافة أمثلة مختلفة
        examples = [
            "RTSP - Hikvision: rtsp://admin:12345@************:554/Streaming/Channels/101/",
            "RTSP - Dahua: rtsp://admin:admin@*************:554/cam/realmonitor?channel=1&subtype=0",
            "RTSP - Generic: rtsp://username:password@ip:554/stream",
            "HTTP - Axis: http://*************/axis-cgi/mjpg/video.cgi",
            "HTTP - Generic: http://*************:8080/video",
            "HTTP - With Auth: ********************************/mjpeg",
            "USB Camera: 0",
            "USB Camera 2: 1",
            "File: test_video.mp4",
            "RTMP: rtmp://server/live/stream"
        ]
        
        for example in examples:
            item = QListWidgetItem(example)
            examples_list.addItem(item)
        
        # زر نسخ الرابط
        copy_btn = QPushButton("📋 نسخ الرابط المحدد")
        copy_btn.clicked.connect(lambda: self.copy_selected_url(examples_list))
        
        layout.addWidget(QLabel("أمثلة روابط الكاميرات:"))
        layout.addWidget(examples_list)
        layout.addWidget(copy_btn)
        
        widget.setLayout(layout)
        return widget
    
    def create_network_settings_tab(self):
        """إنشاء تبويب إعدادات الشبكة"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        network_info = QTextEdit()
        network_info.setReadOnly(True)
        network_info.setHtml("""
        <h2>🌐 إعدادات الشبكة المطلوبة</h2>
        
        <h3>📡 إعدادات الراوتر</h3>
        <ul>
            <li><b>Port Forwarding:</b> فتح المنافذ المطلوبة (554, 80, 8080)</li>
            <li><b>DHCP Reservation:</b> تثبيت عنوان IP للكاميرا</li>
            <li><b>Firewall:</b> السماح للكاميرات بالمرور</li>
        </ul>
        
        <h3>🔧 إعدادات الكاميرا</h3>
        <ul>
            <li><b>IP Address:</b> تعيين عنوان IP ثابت</li>
            <li><b>Subnet Mask:</b> عادة *************</li>
            <li><b>Gateway:</b> عنوان الراوتر</li>
            <li><b>DNS:</b> ******* أو *******</li>
        </ul>
        
        <h3>💻 إعدادات الكمبيوتر</h3>
        <ul>
            <li><b>Windows Firewall:</b> السماح للبرنامج</li>
            <li><b>Antivirus:</b> إضافة استثناء للبرنامج</li>
            <li><b>Network Profile:</b> تعيين الشبكة كـ Private</li>
        </ul>
        
        <h3>🔍 أدوات الفحص</h3>
        <ul>
            <li><b>Ping:</b> ping *************</li>
            <li><b>Telnet:</b> telnet ************* 554</li>
            <li><b>VLC:</b> اختبار الرابط في VLC Media Player</li>
            <li><b>Browser:</b> فتح رابط HTTP في المتصفح</li>
        </ul>
        """)
        
        layout.addWidget(network_info)
        widget.setLayout(layout)
        return widget
    
    def copy_selected_url(self, list_widget):
        """نسخ الرابط المحدد"""
        current_item = list_widget.currentItem()
        if current_item:
            text = current_item.text()
            # استخراج الرابط من النص
            if ": " in text:
                url = text.split(": ", 1)[1]
            else:
                url = text
            
            clipboard = QApplication.clipboard()
            clipboard.setText(url)
            
            QMessageBox.information(self, "تم النسخ", f"تم نسخ الرابط:\n{url}")
    
    def open_camera_tester(self):
        """فتح أداة اختبار الكاميرات"""
        try:
            import subprocess
            subprocess.Popen([sys.executable, "camera_link_tester.py"])
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في فتح أداة الاختبار:\n{str(e)}")
    
    def ping_test(self):
        """اختبار ping للشبكة"""
        ip, ok = QInputDialog.getText(self, "اختبار Ping", "أدخل عنوان IP للكاميرا:")
        if ok and ip:
            try:
                import subprocess
                result = subprocess.run(['ping', '-n', '4', ip], 
                                      capture_output=True, text=True, timeout=10)
                
                if result.returncode == 0:
                    QMessageBox.information(self, "نتيجة Ping", f"✅ الاتصال ناجح مع {ip}")
                else:
                    QMessageBox.warning(self, "نتيجة Ping", f"❌ فشل الاتصال مع {ip}")
                    
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"خطأ في اختبار Ping:\n{str(e)}")
    
    def port_scan(self):
        """فحص المنافذ"""
        ip, ok = QInputDialog.getText(self, "فحص المنافذ", "أدخل عنوان IP للكاميرا:")
        if ok and ip:
            try:
                import socket
                common_ports = [80, 554, 8080, 8000, 443, 1935]
                open_ports = []
                
                for port in common_ports:
                    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                    sock.settimeout(2)
                    result = sock.connect_ex((ip, port))
                    if result == 0:
                        open_ports.append(port)
                    sock.close()
                
                if open_ports:
                    ports_str = ", ".join(map(str, open_ports))
                    QMessageBox.information(self, "نتيجة فحص المنافذ", 
                                          f"✅ المنافذ المفتوحة في {ip}:\n{ports_str}")
                else:
                    QMessageBox.warning(self, "نتيجة فحص المنافذ", 
                                      f"❌ لا توجد منافذ مفتوحة في {ip}")
                    
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"خطأ في فحص المنافذ:\n{str(e)}")
    
    def apply_style(self):
        """تطبيق نمط الواجهة"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f8f9fa;
            }
            QTabWidget::pane {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #ecf0f1;
                padding: 10px 15px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
            }
            QTabBar::tab:selected {
                background-color: #3498db;
                color: white;
            }
            QTextEdit {
                border: none;
                background-color: white;
                font-size: 13px;
                line-height: 1.5;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 15px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QListWidget {
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                background-color: white;
                font-family: 'Courier New', monospace;
            }
        """)


def main():
    """تشغيل دليل حل المشاكل"""
    app = QApplication(sys.argv)
    
    # تعيين خط يدعم العربية
    font = QFont("Arial", 10)
    app.setFont(font)
    
    # إنشاء النافذة الرئيسية
    window = CameraTroubleshootingGuide()
    window.show()
    
    return app.exec_()


if __name__ == "__main__":
    sys.exit(main())
