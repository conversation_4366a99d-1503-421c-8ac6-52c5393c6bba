#!/usr/bin/env python3
"""
تشغيل النظام الأساسي (بدون الميزات المتقدمة)
Run Basic Camera Monitoring System
"""

import sys
import os
import subprocess
from pathlib import Path

def print_banner():
    """طباعة شعار النظام الأساسي"""
    print("=" * 60)
    print("📹 نظام مراقبة الكاميرات - الإصدار الأساسي")
    print("📹 Basic Camera Monitoring System")
    print("=" * 60)
    print()
    print("✨ الميزات المتوفرة:")
    print("📹 مراقبة الكاميرات المباشرة")
    print("🎯 كشف الحركة الأساسي")
    print("📹 التسجيل التلقائي")
    print("👥 إدارة المستخدمين")
    print("🌙 الواجهة المظلمة")
    print("🇸🇦 دعم اللغة العربية")
    print()

def check_python_version():
    """فحص إصدار Python"""
    print("🐍 فحص إصدار Python...")
    
    if sys.version_info < (3, 6):
        print("❌ Python 3.6+ مطلوب")
        print(f"الإصدار الحالي: {sys.version}")
        return False
    
    print(f"✅ Python {sys.version.split()[0]} - متوافق")
    return True

def check_requirements():
    """فحص المتطلبات الأساسية"""
    print("\n📦 فحص المتطلبات...")
    
    required_packages = {
        'cv2': 'opencv-python',
        'numpy': 'numpy', 
        'PyQt5': 'PyQt5',
        'sqlite3': 'مدمج مع Python',
        'requests': 'requests'
    }
    
    missing_packages = []
    
    for package, install_name in required_packages.items():
        try:
            __import__(package)
            print(f"✅ {package}: متوفر")
        except ImportError:
            print(f"❌ {package}: مفقود")
            if install_name != 'مدمج مع Python':
                missing_packages.append(install_name)
    
    return missing_packages

def install_requirements(packages):
    """تثبيت المتطلبات المفقودة"""
    print(f"\n🔄 تثبيت المكتبات المفقودة...")
    
    try:
        for package in packages:
            print(f"📦 تثبيت {package}...")
            result = subprocess.run([
                sys.executable, '-m', 'pip', 'install', package
            ], capture_output=True, text=True, timeout=120)
            
            if result.returncode == 0:
                print(f"✅ تم تثبيت {package}")
            else:
                print(f"⚠️ مشكلة في تثبيت {package}")
                print(f"الخطأ: {result.stderr}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في التثبيت: {e}")
        return False

def setup_directories():
    """إنشاء المجلدات الأساسية"""
    print("\n📁 إنشاء المجلدات...")
    
    directories = [
        'recordings',
        'assets', 
        'logs'
    ]
    
    for directory in directories:
        try:
            Path(directory).mkdir(exist_ok=True)
            print(f"✅ {directory}/")
        except Exception as e:
            print(f"⚠️ فشل في إنشاء {directory}: {e}")

def check_database():
    """فحص قاعدة البيانات"""
    print("\n🗄️ فحص قاعدة البيانات...")
    
    try:
        # إضافة المسار الحالي
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        from core.database import DatabaseManager
        
        db = DatabaseManager()
        
        # اختبار الاتصال
        result = db.authenticate_user('admin', 'admin123')
        
        print("✅ قاعدة البيانات تعمل بشكل صحيح")
        return True
            
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False

def run_system():
    """تشغيل النظام الأساسي"""
    print("\n🚀 تشغيل النظام الأساسي...")
    
    try:
        # تشغيل النظام الرئيسي
        subprocess.run([sys.executable, 'main.py'])
        return True
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        return False

def show_usage_info():
    """عرض معلومات الاستخدام"""
    print("\n" + "=" * 60)
    print("📖 معلومات الاستخدام")
    print("=" * 60)
    
    print("\n🔑 بيانات تسجيل الدخول الافتراضية:")
    print("👤 اسم المستخدم: admin")
    print("🔒 كلمة المرور: admin123")
    
    print("\n📹 إضافة الكاميرات:")
    print("• استخدم 'إضافة كاميرا' من القائمة الرئيسية")
    print("• أو شغل: python add_your_xvr.py")
    print("• أو استخدم المولد: python xvr_camera_generator.py")
    
    print("\n🎯 الميزات الأساسية:")
    print("• مراقبة مباشرة لعدة كاميرات")
    print("• كشف الحركة التلقائي")
    print("• تسجيل عند كشف الحركة")
    print("• إدارة المستخدمين والصلاحيات")
    print("• واجهة عربية مع ثيم مظلم")
    
    print("\n💡 نصائح:")
    print("• للحصول على أفضل أداء، استخدم كاميرات بدقة 720p")
    print("• تأكد من استقرار الاتصال بالشبكة")
    print("• راجع مجلد recordings/ للتسجيلات")
    print("• استخدم camera_fix_now.py لحل مشاكل الكاميرات")

def main():
    """الوظيفة الرئيسية"""
    print_banner()
    
    # فحص Python
    if not check_python_version():
        input("اضغط Enter للخروج...")
        return 1
    
    # فحص المتطلبات
    missing_packages = check_requirements()
    
    if missing_packages:
        print(f"\n⚠️ مكتبات مفقودة: {', '.join(missing_packages)}")
        choice = input("هل تريد تثبيتها تلقائياً؟ (y/n): ").lower()
        
        if choice in ['y', 'yes', 'نعم']:
            if not install_requirements(missing_packages):
                print("❌ فشل في تثبيت المتطلبات")
                input("اضغط Enter للخروج...")
                return 1
            
            # إعادة فحص بعد التثبيت
            print("\n🔄 إعادة فحص المتطلبات...")
            remaining_missing = check_requirements()
            if remaining_missing:
                print(f"❌ لا تزال هناك مكتبات مفقودة: {', '.join(remaining_missing)}")
                input("اضغط Enter للخروج...")
                return 1
        else:
            print("❌ لا يمكن تشغيل النظام بدون المتطلبات الأساسية")
            input("اضغط Enter للخروج...")
            return 1
    
    # إعداد المجلدات
    setup_directories()
    
    # فحص قاعدة البيانات
    if not check_database():
        print("❌ مشكلة في قاعدة البيانات")
        input("اضغط Enter للخروج...")
        return 1
    
    # عرض معلومات الاستخدام
    show_usage_info()
    
    print("\n" + "=" * 60)
    print("🎉 النظام جاهز للتشغيل!")
    print("=" * 60)
    
    # خيارات التشغيل
    print("\nخيارات التشغيل:")
    print("1. تشغيل النظام الأساسي")
    print("2. إضافة كاميرات XVR/DVR")
    print("3. مولد الكاميرات التلقائي")
    print("4. إصلاح مشاكل الكاميرات")
    print("5. عرض معلومات النظام")
    print("6. خروج")
    
    while True:
        choice = input("\nاختر (1-6): ").strip()
        
        if choice == "1":
            print("\n🚀 تشغيل النظام الأساسي...")
            run_system()
            break
        elif choice == "2":
            print("\n📹 إضافة كاميرات XVR/DVR...")
            if os.path.exists('add_your_xvr.py'):
                subprocess.run([sys.executable, 'add_your_xvr.py'])
            else:
                print("❌ ملف add_your_xvr.py غير موجود")
        elif choice == "3":
            print("\n🎯 مولد الكاميرات التلقائي...")
            if os.path.exists('xvr_camera_generator.py'):
                subprocess.run([sys.executable, 'xvr_camera_generator.py'])
            else:
                print("❌ ملف xvr_camera_generator.py غير موجود")
        elif choice == "4":
            print("\n🔧 إصلاح مشاكل الكاميرات...")
            if os.path.exists('camera_fix_now.py'):
                subprocess.run([sys.executable, 'camera_fix_now.py'])
            else:
                print("❌ ملف camera_fix_now.py غير موجود")
        elif choice == "5":
            print("\n📊 معلومات النظام:")
            print(f"🐍 Python: {sys.version}")
            print(f"💻 النظام: {os.name}")
            print(f"📁 المجلد الحالي: {os.getcwd()}")
            
            # فحص الملفات المهمة
            important_files = [
                'main.py', 'database.db', 'config.json',
                'add_your_xvr.py', 'camera_fix_now.py'
            ]
            
            print("\n📄 الملفات المهمة:")
            for file in important_files:
                status = "✅" if os.path.exists(file) else "❌"
                print(f"{status} {file}")
        elif choice == "6":
            print("👋 وداعاً!")
            return 0
        else:
            print("❌ اختيار غير صحيح، حاول مرة أخرى")
    
    return 0

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف التشغيل")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        input("اضغط Enter للخروج...")
        sys.exit(1)
