@echo off
echo ⚡ حل فوري - Instant Fix
echo ========================

REM حذف قاعدة البيانات
if exist database.db (
    del database.db
    echo ✅ تم حذف قاعدة البيانات
)

REM تنظيف ملفات مؤقتة
for /d /r . %%d in (__pycache__) do @if exist "%%d" rd /s /q "%%d"
del /s /q *.pyc 2>nul
echo ✅ تم التنظيف

REM إنهاء عمليات Python
taskkill /F /IM python.exe 2>nul
taskkill /F /IM pythonw.exe 2>nul
echo ✅ تم إنهاء العمليات

REM انتظار
timeout /t 2 /nobreak >nul

REM تشغيل النظام
echo 🚀 تشغيل النظام...
python main.py

pause
