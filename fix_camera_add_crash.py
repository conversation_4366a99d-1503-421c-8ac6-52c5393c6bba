#!/usr/bin/env python3
"""
حل مشكلة إغلاق النظام عند إضافة كاميرا
Fix Camera Addition Crash Issue
"""

import sys
import os
import cv2
import sqlite3
import threading
import time
import traceback
from datetime import datetime

def print_fix_banner():
    """طباعة شعار الإصلاح"""
    print("=" * 60)
    print("📹 حل مشكلة إغلاق النظام عند إضافة كاميرا")
    print("📹 Fix Camera Addition Crash Issue")
    print("=" * 60)
    print()
    print("🎯 المشاكل التي سيتم حلها:")
    print("❌ إغلاق النظام عند إضافة كاميرا")
    print("❌ تجمد الواجهة أثناء اختبار الاتصال")
    print("❌ عدم معالجة أخطاء الاتصال بشكل صحيح")
    print("❌ مشاكل timeout في الاتصال")
    print("❌ تداخل العمليات المتزامنة")
    print()

def test_camera_connection_safe(rtsp_url, timeout=10):
    """اختبار آمن لاتصال الكاميرا"""
    print(f"🔍 اختبار اتصال آمن: {rtsp_url}")

    try:
        # إنشاء كائن VideoCapture مع timeout
        cap = cv2.VideoCapture()

        # تعيين timeout للاتصال
        cap.set(cv2.CAP_PROP_OPEN_TIMEOUT_MSEC, timeout * 1000)
        cap.set(cv2.CAP_PROP_READ_TIMEOUT_MSEC, 5000)

        # محاولة فتح الكاميرا
        success = cap.open(rtsp_url)

        if not success:
            cap.release()
            return False, "فشل في فتح الاتصال"

        # محاولة قراءة إطار واحد
        ret, frame = cap.read()

        if not ret:
            cap.release()
            return False, "الكاميرا متصلة لكن لا تعطي صورة"

        # فحص جودة الإطار
        if frame is None or frame.size == 0:
            cap.release()
            return False, "الإطار فارغ أو تالف"

        # الحصول على معلومات الكاميرا
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        fps = cap.get(cv2.CAP_PROP_FPS)

        cap.release()

        info = {
            'width': width,
            'height': height,
            'fps': fps,
            'frame_size': frame.shape if frame is not None else None
        }

        return True, f"متصل بنجاح - {width}x{height} @ {fps:.1f}fps"

    except Exception as e:
        return False, f"خطأ في الاتصال: {str(e)}"

def test_camera_threaded(rtsp_url, timeout=15):
    """اختبار الكاميرا في thread منفصل لتجنب تجمد الواجهة"""
    result = {'success': False, 'message': '', 'completed': False}

    def test_worker():
        try:
            success, message = test_camera_connection_safe(rtsp_url, timeout)
            result['success'] = success
            result['message'] = message
        except Exception as e:
            result['success'] = False
            result['message'] = f"خطأ في الاختبار: {str(e)}"
        finally:
            result['completed'] = True

    # تشغيل الاختبار في thread منفصل
    test_thread = threading.Thread(target=test_worker, daemon=True)
    test_thread.start()

    # انتظار النتيجة مع timeout
    start_time = time.time()
    while not result['completed'] and (time.time() - start_time) < timeout:
        time.sleep(0.1)

    if not result['completed']:
        result['success'] = False
        result['message'] = "انتهت مهلة الاختبار"

    return result['success'], result['message']

def add_camera_safe(name, rtsp_url, username="", password="", test_connection=True):
    """إضافة كاميرا بطريقة آمنة"""
    print(f"📹 إضافة كاميرا آمنة: {name}")

    try:
        # التحقق من صحة البيانات
        if not name or not rtsp_url:
            return False, "اسم الكاميرا أو الرابط فارغ"

        # اختبار الاتصال إذا طُلب
        if test_connection:
            print("🔍 اختبار الاتصال...")
            success, message = test_camera_threaded(rtsp_url, timeout=10)

            if not success:
                print(f"❌ فشل اختبار الاتصال: {message}")
                # السماح بالإضافة حتى لو فشل الاختبار (للاختبار لاحقاً)
                print("⚠️ سيتم إضافة الكاميرا بدون اختبار")

        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect('database.db', timeout=10)
        cursor = conn.cursor()

        # التحقق من عدم وجود كاميرا بنفس الاسم
        cursor.execute("SELECT id FROM cameras WHERE name = ?", (name,))
        if cursor.fetchone():
            conn.close()
            return False, "يوجد كاميرا بنفس الاسم"

        # إضافة الكاميرا
        cursor.execute('''
            INSERT INTO cameras (name, rtsp_url, username, password, is_active, created_at)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (name, rtsp_url, username, password, 1, datetime.now()))

        camera_id = cursor.lastrowid
        conn.commit()
        conn.close()

        print(f"✅ تم إضافة الكاميرا بنجاح - ID: {camera_id}")
        return True, f"تم إضافة الكاميرا بنجاح (ID: {camera_id})"

    except sqlite3.Error as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False, f"خطأ في قاعدة البيانات: {str(e)}"
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        traceback.print_exc()
        return False, f"خطأ عام: {str(e)}"

def add_xvr_cameras_safe():
    """إضافة كاميرات XVR بطريقة آمنة"""
    print("🔧 إضافة كاميرات XVR بطريقة آمنة...")

    # معلومات جهاز XVR
    xvr_config = {
        'ip': '**************',
        'username': 'admin',
        'password': 'Mnbv@1978',
        'port': 554,
        'channels': 8
    }

    added_count = 0
    failed_count = 0

    for channel in range(1, xvr_config['channels'] + 1):
        camera_name = f"XVR قناة {channel}"
        rtsp_url = f"rtsp://{xvr_config['username']}:{xvr_config['password']}@{xvr_config['ip']}:{xvr_config['port']}/cam/realmonitor?channel={channel}&subtype=0"

        print(f"\n📹 إضافة {camera_name}...")

        try:
            success, message = add_camera_safe(
                name=camera_name,
                rtsp_url=rtsp_url,
                username=xvr_config['username'],
                password=xvr_config['password'],
                test_connection=False  # تجنب اختبار الاتصال لتسريع العملية
            )

            if success:
                print(f"✅ {camera_name}: {message}")
                added_count += 1
            else:
                print(f"❌ {camera_name}: {message}")
                failed_count += 1

        except Exception as e:
            print(f"❌ {camera_name}: خطأ - {e}")
            failed_count += 1

        # انتظار قصير بين الإضافات
        time.sleep(0.5)

    print(f"\n📊 النتائج:")
    print(f"✅ تم إضافة: {added_count} كاميرا")
    print(f"❌ فشل: {failed_count} كاميرا")

    return added_count > 0

def create_safe_camera_manager():
    """إنشاء مدير كاميرات آمن"""
    safe_manager_content = '''#!/usr/bin/env python3
"""
مدير كاميرات آمن
Safe Camera Manager
"""

import sys
import os
import cv2
import sqlite3
import threading
import time
from datetime import datetime

class SafeCameraManager:
    def __init__(self, database_path="database.db"):
        self.database_path = database_path
        self.connection_timeout = 10
        self.read_timeout = 5

    def test_camera_connection(self, rtsp_url, timeout=None):
        """اختبار اتصال الكاميرا بطريقة آمنة"""
        if timeout is None:
            timeout = self.connection_timeout

        try:
            cap = cv2.VideoCapture()
            cap.set(cv2.CAP_PROP_OPEN_TIMEOUT_MSEC, timeout * 1000)
            cap.set(cv2.CAP_PROP_READ_TIMEOUT_MSEC, self.read_timeout * 1000)

            if not cap.open(rtsp_url):
                cap.release()
                return False, "فشل في فتح الاتصال"

            ret, frame = cap.read()
            cap.release()

            if not ret:
                return False, "لا يوجد إشارة فيديو"

            return True, "الاتصال ناجح"

        except Exception as e:
            return False, f"خطأ: {str(e)}"

    def test_camera_threaded(self, rtsp_url, timeout=15):
        """اختبار الكاميرا في thread منفصل"""
        result = {'success': False, 'message': '', 'done': False}

        def test_worker():
            try:
                success, message = self.test_camera_connection(rtsp_url, timeout)
                result['success'] = success
                result['message'] = message
            except Exception as e:
                result['success'] = False
                result['message'] = f"خطأ: {str(e)}"
            finally:
                result['done'] = True

        thread = threading.Thread(target=test_worker, daemon=True)
        thread.start()

        start_time = time.time()
        while not result['done'] and (time.time() - start_time) < timeout:
            time.sleep(0.1)

        if not result['done']:
            return False, "انتهت مهلة الاختبار"

        return result['success'], result['message']

    def add_camera(self, name, rtsp_url, username="", password="", test_first=True):
        """إضافة كاميرا بطريقة آمنة"""
        try:
            # التحقق من البيانات
            if not name or not rtsp_url:
                return False, "بيانات ناقصة"

            # اختبار الاتصال إذا طُلب
            if test_first:
                success, message = self.test_camera_threaded(rtsp_url)
                if not success:
                    return False, f"فشل اختبار الاتصال: {message}"

            # إضافة إلى قاعدة البيانات
            conn = sqlite3.connect(self.database_path, timeout=10)
            cursor = conn.cursor()

            # التحقق من عدم التكرار
            cursor.execute("SELECT id FROM cameras WHERE name = ?", (name,))
            if cursor.fetchone():
                conn.close()
                return False, "اسم الكاميرا موجود مسبقاً"

            # الإضافة
            cursor.execute('''
                INSERT INTO cameras (name, rtsp_url, username, password, is_active, created_at)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (name, rtsp_url, username, password, 1, datetime.now()))

            camera_id = cursor.lastrowid
            conn.commit()
            conn.close()

            return True, f"تم إضافة الكاميرا (ID: {camera_id})"

        except Exception as e:
            return False, f"خطأ: {str(e)}"

    def get_cameras(self):
        """الحصول على قائمة الكاميرات"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT id, name, rtsp_url, username, is_active, created_at
                FROM cameras ORDER BY id
            ''')

            cameras = cursor.fetchall()
            conn.close()

            return cameras

        except Exception as e:
            print(f"خطأ في الحصول على الكاميرات: {e}")
            return []

    def delete_camera(self, camera_id):
        """حذف كاميرا"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()

            cursor.execute("DELETE FROM cameras WHERE id = ?", (camera_id,))

            if cursor.rowcount > 0:
                conn.commit()
                conn.close()
                return True, "تم حذف الكاميرا"
            else:
                conn.close()
                return False, "الكاميرا غير موجودة"

        except Exception as e:
            return False, f"خطأ في الحذف: {str(e)}"

# مثال للاستخدام
if __name__ == "__main__":
    manager = SafeCameraManager()

    # اختبار إضافة كاميرا
    success, message = manager.add_camera(
        name="كاميرا اختبار",
        rtsp_url="rtsp://admin:password@192.168.1.100:554/stream",
        test_first=True
    )

    print(f"النتيجة: {message}")
'''

    try:
        with open('safe_camera_manager.py', 'w', encoding='utf-8') as f:
            f.write(safe_manager_content)
        print("✅ تم إنشاء مدير الكاميرات الآمن")
        return True
    except Exception as e:
        print(f"❌ فشل في إنشاء مدير الكاميرات: {e}")
        return False

def fix_database_for_cameras():
    """إصلاح قاعدة البيانات للكاميرات"""
    print("🗄️ إصلاح قاعدة البيانات للكاميرات...")

    try:
        conn = sqlite3.connect('database.db')
        cursor = conn.cursor()

        # إنشاء جدول الكاميرات إذا لم يكن موجوداً
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS cameras (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                rtsp_url TEXT NOT NULL,
                username TEXT,
                password TEXT,
                is_active BOOLEAN DEFAULT 1,
                camera_type TEXT,
                ip_address TEXT,
                port INTEGER DEFAULT 554,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_connected TIMESTAMP,
                connection_status TEXT DEFAULT 'unknown'
            )
        ''')

        # إضافة أعمدة جديدة إذا لم تكن موجودة
        new_columns = [
            ('camera_type', 'TEXT'),
            ('ip_address', 'TEXT'),
            ('port', 'INTEGER DEFAULT 554'),
            ('last_connected', 'TIMESTAMP'),
            ('connection_status', 'TEXT DEFAULT "unknown"')
        ]

        for column_name, column_type in new_columns:
            try:
                cursor.execute(f'ALTER TABLE cameras ADD COLUMN {column_name} {column_type}')
                print(f"✅ تم إضافة عمود {column_name}")
            except sqlite3.OperationalError:
                # العمود موجود مسبقاً
                pass

        # إنشاء جدول سجل اتصال الكاميرات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS camera_connection_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                camera_id INTEGER,
                connection_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                connection_status TEXT,
                error_message TEXT,
                FOREIGN KEY (camera_id) REFERENCES cameras (id)
            )
        ''')

        conn.commit()
        conn.close()

        print("✅ تم إصلاح قاعدة البيانات")
        return True

    except Exception as e:
        print(f"❌ خطأ في إصلاح قاعدة البيانات: {e}")
        return False

def create_camera_test_tool():
    """إنشاء أداة اختبار الكاميرات"""
    test_tool_content = '''#!/usr/bin/env python3
"""
أداة اختبار الكاميرات
Camera Test Tool
"""

import cv2
import sys
import time
import threading

def test_single_camera(rtsp_url, timeout=10):
    """اختبار كاميرا واحدة"""
    print(f"🔍 اختبار: {rtsp_url}")

    try:
        cap = cv2.VideoCapture()
        cap.set(cv2.CAP_PROP_OPEN_TIMEOUT_MSEC, timeout * 1000)
        cap.set(cv2.CAP_PROP_READ_TIMEOUT_MSEC, 5000)

        if not cap.open(rtsp_url):
            return False, "فشل في فتح الاتصال"

        ret, frame = cap.read()

        if ret and frame is not None:
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            fps = cap.get(cv2.CAP_PROP_FPS)

            cap.release()
            return True, f"نجح - {width}x{height} @ {fps:.1f}fps"
        else:
            cap.release()
            return False, "لا يوجد إشارة فيديو"

    except Exception as e:
        return False, f"خطأ: {str(e)}"

def test_xvr_cameras():
    """اختبار جميع كاميرات XVR"""
    print("📹 اختبار كاميرات XVR...")

    ip = "**************"
    username = "admin"
    password = "Mnbv@1978"

    results = []

    for channel in range(1, 9):
        rtsp_url = f"rtsp://{username}:{password}@{ip}:554/cam/realmonitor?channel={channel}&subtype=0"

        success, message = test_single_camera(rtsp_url, timeout=8)

        result = {
            'channel': channel,
            'url': rtsp_url,
            'success': success,
            'message': message
        }

        results.append(result)

        status = "✅" if success else "❌"
        print(f"{status} قناة {channel}: {message}")

        time.sleep(1)  # انتظار بين الاختبارات

    # ملخص النتائج
    successful = sum(1 for r in results if r['success'])
    print(f"\\n📊 النتائج: {successful}/8 كاميرات تعمل")

    return results

if __name__ == "__main__":
    if len(sys.argv) > 1:
        # اختبار رابط محدد
        rtsp_url = sys.argv[1]
        success, message = test_single_camera(rtsp_url)
        print(f"النتيجة: {message}")
    else:
        # اختبار كاميرات XVR
        test_xvr_cameras()
'''

    try:
        with open('test_cameras.py', 'w', encoding='utf-8') as f:
            f.write(test_tool_content)
        print("✅ تم إنشاء أداة اختبار الكاميرات")
        return True
    except Exception as e:
        print(f"❌ فشل في إنشاء أداة الاختبار: {e}")
        return False

def create_safe_add_camera_gui():
    """إنشاء واجهة آمنة لإضافة الكاميرات"""
    gui_content = '''#!/usr/bin/env python3
"""
واجهة آمنة لإضافة الكاميرات
Safe Camera Addition GUI
"""

import sys
import os
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
import threading
import time

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from safe_camera_manager import SafeCameraManager
except ImportError:
    print("تحذير: لم يتم العثور على safe_camera_manager")
    SafeCameraManager = None

class CameraTestWorker(QThread):
    """Worker thread لاختبار الكاميرا"""
    finished = pyqtSignal(bool, str)
    progress = pyqtSignal(str)

    def __init__(self, rtsp_url):
        super().__init__()
        self.rtsp_url = rtsp_url

    def run(self):
        try:
            self.progress.emit("جاري اختبار الاتصال...")

            if SafeCameraManager:
                manager = SafeCameraManager()
                success, message = manager.test_camera_connection(self.rtsp_url, timeout=10)
            else:
                # اختبار بسيط بدون المدير
                import cv2
                cap = cv2.VideoCapture(self.rtsp_url)
                success = cap.isOpened()
                message = "متصل" if success else "فشل الاتصال"
                cap.release()

            self.finished.emit(success, message)

        except Exception as e:
            self.finished.emit(False, f"خطأ: {str(e)}")

class SafeAddCameraDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("إضافة كاميرا آمنة")
        self.setFixedSize(500, 400)

        self.manager = SafeCameraManager() if SafeCameraManager else None
        self.test_worker = None

        self.setup_ui()
        self.apply_style()

    def setup_ui(self):
        layout = QVBoxLayout()

        # العنوان
        title = QLabel("📹 إضافة كاميرا جديدة")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 16px; font-weight: bold; margin: 10px;")
        layout.addWidget(title)

        # نموذج الإدخال
        form_layout = QFormLayout()

        self.name_input = QLineEdit()
        self.name_input.setPlaceholderText("مثال: كاميرا المدخل الرئيسي")
        form_layout.addRow("اسم الكاميرا:", self.name_input)

        self.rtsp_input = QLineEdit()
        self.rtsp_input.setPlaceholderText("rtsp://username:password@ip:port/path")
        form_layout.addRow("رابط RTSP:", self.rtsp_input)

        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("admin")
        form_layout.addRow("اسم المستخدم:", self.username_input)

        self.password_input = QLineEdit()
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setPlaceholderText("كلمة المرور")
        form_layout.addRow("كلمة المرور:", self.password_input)

        layout.addLayout(form_layout)

        # أزرار الاختبار
        test_layout = QHBoxLayout()

        self.test_btn = QPushButton("🔍 اختبار الاتصال")
        self.test_btn.clicked.connect(self.test_connection)
        test_layout.addWidget(self.test_btn)

        self.auto_fill_btn = QPushButton("🔧 ملء تلقائي XVR")
        self.auto_fill_btn.clicked.connect(self.auto_fill_xvr)
        test_layout.addWidget(self.auto_fill_btn)

        layout.addLayout(test_layout)

        # منطقة النتائج
        self.result_text = QTextEdit()
        self.result_text.setMaximumHeight(100)
        self.result_text.setReadOnly(True)
        layout.addWidget(self.result_text)

        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)

        # أزرار التحكم
        button_layout = QHBoxLayout()

        self.add_btn = QPushButton("✅ إضافة الكاميرا")
        self.add_btn.clicked.connect(self.add_camera)
        self.add_btn.setEnabled(False)
        button_layout.addWidget(self.add_btn)

        self.cancel_btn = QPushButton("❌ إلغاء")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)

        layout.addLayout(button_layout)

        self.setLayout(layout)

    def apply_style(self):
        self.setStyleSheet("""
            QDialog {
                background-color: #2b2b2b;
                color: white;
            }
            QLineEdit {
                padding: 8px;
                border: 2px solid #555;
                border-radius: 4px;
                background-color: #3b3b3b;
                color: white;
            }
            QPushButton {
                padding: 8px 16px;
                border: none;
                border-radius: 4px;
                background-color: #0078d4;
                color: white;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
            QPushButton:disabled {
                background-color: #555;
                color: #999;
            }
            QTextEdit {
                border: 2px solid #555;
                border-radius: 4px;
                background-color: #3b3b3b;
                color: white;
            }
        """)

    def auto_fill_xvr(self):
        """ملء تلقائي لإعدادات XVR"""
        self.rtsp_input.setText("rtsp://admin:Mnbv@1978@**************:554/cam/realmonitor?channel=1&subtype=0")
        self.username_input.setText("admin")
        self.password_input.setText("Mnbv@1978")
        self.name_input.setText("XVR قناة 1")

        self.result_text.append("✅ تم ملء إعدادات XVR تلقائياً")

    def test_connection(self):
        """اختبار اتصال الكاميرا"""
        rtsp_url = self.rtsp_input.text().strip()

        if not rtsp_url:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال رابط RTSP")
            return

        self.test_btn.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # شريط تقدم غير محدد

        self.result_text.append(f"🔍 اختبار: {rtsp_url}")

        # تشغيل الاختبار في thread منفصل
        self.test_worker = CameraTestWorker(rtsp_url)
        self.test_worker.progress.connect(self.update_progress)
        self.test_worker.finished.connect(self.test_finished)
        self.test_worker.start()

    def update_progress(self, message):
        """تحديث رسالة التقدم"""
        self.result_text.append(f"⏳ {message}")

    def test_finished(self, success, message):
        """انتهاء اختبار الاتصال"""
        self.test_btn.setEnabled(True)
        self.progress_bar.setVisible(False)

        if success:
            self.result_text.append(f"✅ {message}")
            self.add_btn.setEnabled(True)
        else:
            self.result_text.append(f"❌ {message}")
            self.add_btn.setEnabled(False)

    def add_camera(self):
        """إضافة الكاميرا"""
        name = self.name_input.text().strip()
        rtsp_url = self.rtsp_input.text().strip()
        username = self.username_input.text().strip()
        password = self.password_input.text().strip()

        if not name or not rtsp_url:
            QMessageBox.warning(self, "تحذير", "يرجى ملء الحقول المطلوبة")
            return

        if self.manager:
            success, message = self.manager.add_camera(name, rtsp_url, username, password, test_first=False)
        else:
            # إضافة بسيطة بدون المدير
            try:
                import sqlite3
                conn = sqlite3.connect('database.db')
                cursor = conn.cursor()

                cursor.execute('''
                    INSERT INTO cameras (name, rtsp_url, username, password, is_active)
                    VALUES (?, ?, ?, ?, ?)
                ''', (name, rtsp_url, username, password, 1))

                conn.commit()
                conn.close()
                success, message = True, "تم إضافة الكاميرا"
            except Exception as e:
                success, message = False, f"خطأ: {str(e)}"

        if success:
            QMessageBox.information(self, "نجح", message)
            self.accept()
        else:
            QMessageBox.critical(self, "خطأ", message)

def main():
    app = QApplication(sys.argv)

    dialog = SafeAddCameraDialog()

    if dialog.exec_() == QDialog.Accepted:
        print("تم إضافة الكاميرا بنجاح")
    else:
        print("تم إلغاء إضافة الكاميرا")

if __name__ == "__main__":
    main()
'''

    try:
        with open('safe_add_camera_gui.py', 'w', encoding='utf-8') as f:
            f.write(gui_content)
        print("✅ تم إنشاء واجهة إضافة الكاميرات الآمنة")
        return True
    except Exception as e:
        print(f"❌ فشل في إنشاء الواجهة: {e}")
        return False

def run_all_fixes():
    """تشغيل جميع الإصلاحات"""
    print("🔧 تشغيل جميع إصلاحات مشكلة إضافة الكاميرات...")

    fixes = [
        ("إصلاح قاعدة البيانات", fix_database_for_cameras),
        ("إنشاء مدير الكاميرات الآمن", create_safe_camera_manager),
        ("إنشاء أداة اختبار الكاميرات", create_camera_test_tool),
        ("إنشاء واجهة إضافة آمنة", create_safe_add_camera_gui),
        ("إضافة كاميرات XVR", add_xvr_cameras_safe)
    ]

    completed_fixes = 0

    for fix_name, fix_function in fixes:
        print(f"\n🔄 {fix_name}...")
        try:
            if fix_function():
                print(f"✅ {fix_name}: مكتمل")
                completed_fixes += 1
            else:
                print(f"⚠️ {fix_name}: فشل جزئي")
        except Exception as e:
            print(f"❌ {fix_name}: فشل - {e}")

    return completed_fixes, len(fixes)

def test_camera_addition():
    """اختبار إضافة الكاميرات"""
    print("🧪 اختبار إضافة الكاميرات...")

    try:
        # اختبار إضافة كاميرا تجريبية
        test_camera = {
            'name': 'كاميرا اختبار',
            'rtsp_url': 'rtsp://admin:Mnbv@1978@**************:554/cam/realmonitor?channel=1&subtype=0',
            'username': 'admin',
            'password': 'Mnbv@1978'
        }

        success, message = add_camera_safe(
            name=test_camera['name'],
            rtsp_url=test_camera['rtsp_url'],
            username=test_camera['username'],
            password=test_camera['password'],
            test_connection=False
        )

        if success:
            print(f"✅ اختبار الإضافة: {message}")

            # حذف الكاميرا التجريبية
            try:
                conn = sqlite3.connect('database.db')
                cursor = conn.cursor()
                cursor.execute("DELETE FROM cameras WHERE name = ?", (test_camera['name'],))
                conn.commit()
                conn.close()
                print("✅ تم حذف الكاميرا التجريبية")
            except:
                pass

            return True
        else:
            print(f"❌ فشل اختبار الإضافة: {message}")
            return False

    except Exception as e:
        print(f"❌ خطأ في اختبار الإضافة: {e}")
        return False

def show_usage_instructions():
    """عرض تعليمات الاستخدام"""
    print("\n" + "=" * 60)
    print("📋 تعليمات الاستخدام بعد الإصلاح:")
    print("=" * 60)

    print("\n🎯 طرق إضافة الكاميرات الآمنة:")

    print("\n1️⃣ استخدام الواجهة الآمنة:")
    print("   python safe_add_camera_gui.py")

    print("\n2️⃣ استخدام المدير الآمن:")
    print("   python safe_camera_manager.py")

    print("\n3️⃣ اختبار الكاميرات:")
    print("   python test_cameras.py")
    print("   python test_cameras.py rtsp://your-camera-url")

    print("\n4️⃣ إضافة كاميرات XVR مباشرة:")
    print("   python add_cameras_now.py")

    print("\n💡 نصائح لتجنب المشاكل:")
    print("• استخدم دائماً الواجهة الآمنة لإضافة الكاميرات")
    print("• اختبر الاتصال قبل الإضافة")
    print("• تأكد من صحة رابط RTSP")
    print("• تحقق من اتصال الشبكة")
    print("• استخدم timeout مناسب للاختبار")

    print("\n🔧 في حالة المشاكل:")
    print("• شغل fix_camera_add_crash.py مرة أخرى")
    print("• تحقق من سجل الأخطاء")
    print("• استخدم أداة اختبار الكاميرات")
    print("• أعد تشغيل النظام")

def main():
    """الوظيفة الرئيسية"""
    print_fix_banner()

    print("⚠️ هذا الإصلاح سيقوم بـ:")
    print("1. إصلاح قاعدة البيانات للكاميرات")
    print("2. إنشاء مدير كاميرات آمن")
    print("3. إنشاء أداة اختبار الكاميرات")
    print("4. إنشاء واجهة إضافة آمنة")
    print("5. إضافة كاميرات XVR بطريقة آمنة")
    print()

    confirm = input("هل تريد المتابعة؟ (y/n): ").lower()
    if confirm not in ['y', 'yes', 'نعم']:
        print("❌ تم إلغاء الإصلاح")
        return

    print("\n🚀 بدء إصلاح مشكلة إضافة الكاميرات...")

    # تشغيل جميع الإصلاحات
    completed, total = run_all_fixes()

    # اختبار النظام
    print("\n🧪 اختبار النظام المُصلح...")
    test_success = test_camera_addition()

    # النتائج
    print("\n" + "=" * 60)
    print("📊 نتائج الإصلاح:")
    print("=" * 60)
    print(f"✅ تم إكمال {completed}/{total} إصلاح")

    if test_success:
        print("✅ اختبار إضافة الكاميرات: نجح")
    else:
        print("⚠️ اختبار إضافة الكاميرات: فشل")

    if completed >= 3:
        print("\n🎉 تم إصلاح مشكلة إضافة الكاميرات بنجاح!")

        print("\n📋 الملفات الجديدة:")
        print("• safe_camera_manager.py - مدير كاميرات آمن")
        print("• safe_add_camera_gui.py - واجهة إضافة آمنة")
        print("• test_cameras.py - أداة اختبار الكاميرات")

        # عرض تعليمات الاستخدام
        show_usage_instructions()

        # خيار تشغيل الواجهة الآمنة
        run_gui = input("\nهل تريد تشغيل واجهة إضافة الكاميرات الآمنة؟ (y/n): ").lower()
        if run_gui in ['y', 'yes', 'نعم']:
            print("\n🚀 تشغيل الواجهة الآمنة...")
            try:
                import subprocess
                subprocess.run([sys.executable, 'safe_add_camera_gui.py'])
            except Exception as e:
                print(f"❌ فشل تشغيل الواجهة: {e}")
                print("💡 شغل الواجهة يدوياً: python safe_add_camera_gui.py")

    else:
        print("\n⚠️ الإصلاح غير مكتمل")
        print("💡 راجع الأخطاء أعلاه وأعد المحاولة")
        print("💡 أو شغل الإصلاحات يدوياً حسب الحاجة")

if __name__ == "__main__":
    try:
        main()
        input("\nاضغط Enter للخروج...")
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الإصلاح")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        traceback.print_exc()
        input("اضغط Enter للخروج...")