#!/usr/bin/env python3
"""
إعداد نظام أرقام السيارات القطرية
Setup Qatar License Plate System
"""

import sys
import os
import sqlite3
import json
from datetime import datetime

def print_qatar_setup_banner():
    """طباعة شعار الإعداد القطري"""
    print("=" * 60)
    print("🇶🇦 إعداد نظام أرقام السيارات القطرية")
    print("🇶🇦 Qatar License Plate System Setup")
    print("=" * 60)
    print()
    print("🎯 الميزات القطرية المتخصصة:")
    print("✅ دعم الأنماط القطرية الحديثة (6 أرقام)")
    print("✅ دعم الأنماط التقليدية (أرقام + أحرف)")
    print("✅ كشف اللوحات المميزة (VIP)")
    print("✅ كشف اللوحات الحكومية")
    print("✅ كشف اللوحات الدبلوماسية")
    print("✅ دعم اللغة العربية والإنجليزية")
    print("✅ إحصائيات مفصلة للوحات القطرية")
    print()

def create_qatar_database():
    """إنشاء قاعدة البيانات للوحات القطرية"""
    print("🗄️ إنشاء قاعدة البيانات القطرية...")
    
    try:
        conn = sqlite3.connect('database.db')
        cursor = conn.cursor()
        
        # جدول أرقام السيارات القطرية
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS qatar_license_plates (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                camera_id INTEGER,
                plate_number TEXT,
                plate_type TEXT,
                confidence REAL,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                image_path TEXT,
                is_vip BOOLEAN DEFAULT 0,
                is_government BOOLEAN DEFAULT 0,
                is_diplomatic BOOLEAN DEFAULT 0,
                is_whitelisted BOOLEAN DEFAULT 0,
                is_blacklisted BOOLEAN DEFAULT 0,
                owner_info TEXT,
                FOREIGN KEY (camera_id) REFERENCES cameras (id)
            )
        ''')
        
        # جدول قوائم السيارات القطرية
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS qatar_plate_lists (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                plate_number TEXT UNIQUE NOT NULL,
                list_type TEXT CHECK(list_type IN ('whitelist', 'blacklist', 'vip', 'government')),
                owner_name TEXT,
                owner_id TEXT,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول إحصائيات اللوحات القطرية
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS qatar_plate_stats (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                date DATE,
                total_detections INTEGER DEFAULT 0,
                vip_detections INTEGER DEFAULT 0,
                government_detections INTEGER DEFAULT 0,
                diplomatic_detections INTEGER DEFAULT 0,
                blacklist_detections INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول أنواع اللوحات القطرية
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS qatar_plate_types (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                type_name TEXT UNIQUE NOT NULL,
                pattern TEXT,
                description TEXT,
                color_scheme TEXT,
                is_special BOOLEAN DEFAULT 0
            )
        ''')
        
        conn.commit()
        conn.close()
        
        print("✅ تم إنشاء قاعدة البيانات القطرية")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")
        return False

def add_qatar_plate_types():
    """إضافة أنواع اللوحات القطرية"""
    print("📋 إضافة أنواع اللوحات القطرية...")
    
    try:
        conn = sqlite3.connect('database.db')
        cursor = conn.cursor()
        
        plate_types = [
            # الأنماط الحديثة
            ('modern_6_digits', r'^\d{6}$', 'لوحة حديثة 6 أرقام', 'أبيض/أسود', 0),
            ('modern_5_digits', r'^\d{5}$', 'لوحة حديثة 5 أرقام', 'أبيض/أسود', 0),
            
            # الأنماط التقليدية
            ('traditional_number_letter', r'^\d{1,4}[أ-ي]$', 'لوحة تقليدية رقم+حرف', 'أبيض/أسود', 0),
            ('traditional_letter_number', r'^[أ-ي]\d{1,5}$', 'لوحة تقليدية حرف+رقم', 'أبيض/أسود', 0),
            
            # اللوحات المميزة
            ('vip_single', r'^\d{1,3}$', 'لوحة مميزة VIP', 'ذهبي/أسود', 1),
            ('vip_double', r'^\d{2}$', 'لوحة مميزة رقمين', 'ذهبي/أسود', 1),
            ('vip_triple', r'^\d{3}$', 'لوحة مميزة ثلاثة أرقام', 'ذهبي/أسود', 1),
            
            # اللوحات الحكومية
            ('government', r'^[1-9]\d{0,2}[أ-ي]{1,2}$', 'لوحة حكومية', 'أزرق/أبيض', 1),
            ('police', r'^POLICE\d{1,4}$', 'لوحة شرطة', 'أزرق/أبيض', 1),
            ('military', r'^ARMY\d{1,4}$', 'لوحة عسكرية', 'أخضر/أبيض', 1),
            
            # اللوحات الدبلوماسية
            ('diplomatic', r'^CD\d{1,4}$', 'لوحة دبلوماسية', 'أحمر/أبيض', 1),
            ('embassy', r'^EMB\d{1,4}$', 'لوحة سفارة', 'أحمر/أبيض', 1),
            
            # أنماط خاصة
            ('taxi', r'^TAXI\d{1,4}$', 'لوحة تاكسي', 'أصفر/أسود', 0),
            ('transport', r'^TRANS\d{1,4}$', 'لوحة نقل', 'أبيض/أزرق', 0),
        ]
        
        for type_name, pattern, description, color_scheme, is_special in plate_types:
            cursor.execute('''
                INSERT OR REPLACE INTO qatar_plate_types 
                (type_name, pattern, description, color_scheme, is_special)
                VALUES (?, ?, ?, ?, ?)
            ''', (type_name, pattern, description, color_scheme, is_special))
        
        conn.commit()
        conn.close()
        
        print(f"✅ تم إضافة {len(plate_types)} نوع لوحة قطرية")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إضافة أنواع اللوحات: {e}")
        return False

def add_sample_qatar_plates():
    """إضافة أرقام سيارات قطرية تجريبية"""
    print("🚗 إضافة أرقام سيارات قطرية تجريبية...")
    
    try:
        conn = sqlite3.connect('database.db')
        cursor = conn.cursor()
        
        sample_plates = [
            # لوحات VIP
            ('1', 'vip', 'أمير قطر', '12345678901', 'لوحة رقم 1 - أمير البلاد'),
            ('7', 'vip', 'ولي العهد', '12345678902', 'لوحة رقم 7 - ولي العهد'),
            ('123', 'vip', 'شخصية مهمة', '12345678903', 'لوحة مميزة ثلاثة أرقام'),
            
            # لوحات حكومية
            ('POLICE1', 'government', 'شرطة قطر', 'POLICE001', 'مركبة شرطة'),
            ('ARMY1', 'government', 'الجيش القطري', 'ARMY001', 'مركبة عسكرية'),
            ('123أ', 'government', 'وزارة الداخلية', 'GOV001', 'مركبة حكومية'),
            
            # لوحات دبلوماسية
            ('CD1', 'government', 'السفارة الأمريكية', 'DIPL001', 'مركبة دبلوماسية'),
            ('CD2', 'government', 'السفارة البريطانية', 'DIPL002', 'مركبة دبلوماسية'),
            
            # لوحات عادية - قائمة بيضاء
            ('123456', 'whitelist', 'موظف مهم', '12345678904', 'موظف في شركة مهمة'),
            ('98765', 'whitelist', 'زائر معتمد', '12345678905', 'زائر له صلاحية دخول'),
            ('456ب', 'whitelist', 'مقاول معتمد', '12345678906', 'مقاول معتمد للشركة'),
            
            # لوحات عادية - قائمة سوداء
            ('666666', 'blacklist', 'مركبة محظورة', '12345678907', 'مركبة غير مرغوب فيها'),
            ('13579', 'blacklist', 'شخص محظور', '12345678908', 'شخص محظور من الدخول'),
        ]
        
        for plate_number, list_type, owner_name, owner_id, description in sample_plates:
            cursor.execute('''
                INSERT OR REPLACE INTO qatar_plate_lists 
                (plate_number, list_type, owner_name, owner_id, description)
                VALUES (?, ?, ?, ?, ?)
            ''', (plate_number, list_type, owner_name, owner_id, description))
        
        conn.commit()
        conn.close()
        
        print(f"✅ تم إضافة {len(sample_plates)} لوحة تجريبية")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إضافة اللوحات التجريبية: {e}")
        return False

def create_qatar_config():
    """إنشاء ملف إعدادات خاص بقطر"""
    print("⚙️ إنشاء ملف الإعدادات القطرية...")
    
    try:
        qatar_config = {
            "country_info": {
                "name": "دولة قطر",
                "name_en": "State of Qatar",
                "code": "QA",
                "flag": "🇶🇦"
            },
            "plate_system": {
                "current_system": "modern_2018",
                "previous_system": "traditional",
                "transition_date": "2018-01-01"
            },
            "plate_patterns": {
                "modern": {
                    "6_digits": {
                        "pattern": "^\\d{6}$",
                        "example": "123456",
                        "description": "النظام الحديث 6 أرقام",
                        "introduced": "2018"
                    },
                    "5_digits": {
                        "pattern": "^\\d{5}$",
                        "example": "12345",
                        "description": "النظام الحديث 5 أرقام",
                        "introduced": "2018"
                    }
                },
                "traditional": {
                    "number_letter": {
                        "pattern": "^\\d{1,4}[أ-ي]$",
                        "example": "123أ",
                        "description": "النظام التقليدي رقم+حرف"
                    },
                    "letter_number": {
                        "pattern": "^[أ-ي]\\d{1,5}$",
                        "example": "أ12345",
                        "description": "النظام التقليدي حرف+رقم"
                    }
                },
                "special": {
                    "vip": {
                        "pattern": "^\\d{1,3}$",
                        "example": "1, 7, 123",
                        "description": "لوحات مميزة VIP",
                        "color": "ذهبي"
                    },
                    "government": {
                        "pattern": "^[1-9]\\d{0,2}[أ-ي]{1,2}$",
                        "example": "123أب",
                        "description": "لوحات حكومية",
                        "color": "أزرق"
                    },
                    "diplomatic": {
                        "pattern": "^CD\\d{1,4}$",
                        "example": "CD123",
                        "description": "لوحات دبلوماسية",
                        "color": "أحمر"
                    }
                }
            },
            "detection_settings": {
                "confidence_threshold": 0.75,
                "min_plate_width": 100,
                "min_plate_height": 30,
                "max_plate_width": 400,
                "max_plate_height": 120,
                "aspect_ratio_min": 2.5,
                "aspect_ratio_max": 5.5
            },
            "ocr_settings": {
                "languages": ["ara", "eng"],
                "char_whitelist": "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZأبتثجحخدذرزسشصضطظعغفقكلمنهوي",
                "psm_mode": 8,
                "oem_mode": 3
            },
            "alerts": {
                "vip_detection": {
                    "enabled": True,
                    "priority": "high",
                    "message": "تم كشف لوحة مميزة VIP"
                },
                "government_detection": {
                    "enabled": True,
                    "priority": "medium",
                    "message": "تم كشف لوحة حكومية"
                },
                "diplomatic_detection": {
                    "enabled": True,
                    "priority": "high",
                    "message": "تم كشف لوحة دبلوماسية"
                },
                "blacklist_detection": {
                    "enabled": True,
                    "priority": "critical",
                    "message": "تحذير: تم كشف لوحة محظورة"
                }
            },
            "statistics": {
                "daily_reports": True,
                "weekly_reports": True,
                "monthly_reports": True,
                "export_format": "excel"
            }
        }
        
        with open('qatar_config.json', 'w', encoding='utf-8') as f:
            json.dump(qatar_config, f, indent=2, ensure_ascii=False)
        
        print("✅ تم إنشاء ملف الإعدادات القطرية")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الإعدادات: {e}")
        return False

def create_directories():
    """إنشاء المجلدات الخاصة بالنظام القطري"""
    print("📁 إنشاء المجلدات القطرية...")
    
    directories = [
        'qatar_plates_database',
        'qatar_plates_database/vip',
        'qatar_plates_database/government',
        'qatar_plates_database/diplomatic',
        'qatar_plates_database/standard',
        'qatar_reports',
        'qatar_statistics',
        'qatar_exports'
    ]
    
    created_count = 0
    
    for directory in directories:
        try:
            os.makedirs(directory, exist_ok=True)
            print(f"✅ {directory}/")
            created_count += 1
        except Exception as e:
            print(f"❌ فشل في إنشاء {directory}: {e}")
    
    print(f"📊 تم إنشاء {created_count}/{len(directories)} مجلد")
    return created_count > 0

def test_qatar_system():
    """اختبار النظام القطري"""
    print("🧪 اختبار النظام القطري...")
    
    try:
        # اختبار قاعدة البيانات
        conn = sqlite3.connect('database.db')
        cursor = conn.cursor()
        
        # فحص الجداول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE 'qatar_%'")
        tables = cursor.fetchall()
        print(f"✅ {len(tables)} جدول قطري")
        
        # فحص البيانات
        cursor.execute("SELECT COUNT(*) FROM qatar_plate_lists")
        plate_count = cursor.fetchone()[0]
        print(f"✅ {plate_count} لوحة في القوائم")
        
        cursor.execute("SELECT COUNT(*) FROM qatar_plate_types")
        type_count = cursor.fetchone()[0]
        print(f"✅ {type_count} نوع لوحة")
        
        conn.close()
        
        # اختبار ملف الإعدادات
        if os.path.exists('qatar_config.json'):
            with open('qatar_config.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
            print("✅ ملف الإعدادات القطرية")
        
        # اختبار المجلدات
        if os.path.exists('qatar_plates_database'):
            print("✅ مجلدات النظام القطري")
        
        print("✅ النظام القطري جاهز للعمل")
        return True
        
    except Exception as e:
        print(f"❌ فشل اختبار النظام: {e}")
        return False

def main():
    """الوظيفة الرئيسية"""
    print_qatar_setup_banner()
    
    confirm = input("هل تريد إعداد نظام أرقام السيارات القطرية؟ (y/n): ").lower()
    if confirm not in ['y', 'yes', 'نعم']:
        print("❌ تم إلغاء الإعداد")
        return
    
    print("\n🚀 بدء إعداد النظام القطري...")
    
    # الخطوات
    steps = [
        ("إنشاء قاعدة البيانات", create_qatar_database),
        ("إضافة أنواع اللوحات", add_qatar_plate_types),
        ("إضافة لوحات تجريبية", add_sample_qatar_plates),
        ("إنشاء ملف الإعدادات", create_qatar_config),
        ("إنشاء المجلدات", create_directories),
        ("اختبار النظام", test_qatar_system)
    ]
    
    completed_steps = 0
    
    for step_name, step_function in steps:
        print(f"\n🔄 {step_name}...")
        try:
            if step_function():
                print(f"✅ {step_name}: مكتمل")
                completed_steps += 1
            else:
                print(f"⚠️ {step_name}: فشل جزئي")
        except Exception as e:
            print(f"❌ {step_name}: فشل - {e}")
    
    # النتائج
    print("\n" + "=" * 60)
    print("📊 نتائج إعداد النظام القطري:")
    print("=" * 60)
    print(f"✅ تم إكمال {completed_steps}/{len(steps)} خطوات")
    
    if completed_steps >= 4:
        print("🎉 تم إعداد النظام القطري بنجاح!")
        print("\n🇶🇦 الميزات المفعلة:")
        print("✅ كشف اللوحات القطرية الحديثة والتقليدية")
        print("✅ كشف اللوحات المميزة VIP")
        print("✅ كشف اللوحات الحكومية والدبلوماسية")
        print("✅ دعم اللغة العربية والإنجليزية")
        print("✅ قوائم بيضاء وسوداء متخصصة")
        print("✅ إحصائيات مفصلة")
        
        print("\n📋 ملفات تم إنشاؤها:")
        print("• qatar_config.json - إعدادات النظام القطري")
        print("• qatar_plates_database/ - مجلد صور اللوحات")
        print("• qatar_reports/ - مجلد التقارير")
        print("• قاعدة بيانات محدثة بالجداول القطرية")
        
        print("\n🚀 للاستخدام:")
        print("• python qatar_license_plate_detector.py - للاختبار")
        print("• ادمج النظام مع main.py للتشغيل الكامل")
        
    else:
        print("⚠️ الإعداد غير مكتمل")
        print("💡 راجع الأخطاء أعلاه وأعد المحاولة")

if __name__ == "__main__":
    try:
        main()
        input("\nاضغط Enter للخروج...")
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الإعداد")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        input("اضغط Enter للخروج...")
