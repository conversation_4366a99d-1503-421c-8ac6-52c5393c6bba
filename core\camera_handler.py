import cv2
import threading
import time
import numpy as np
from datetime import datetime
from .motion_detector import MotionDetector
from .recorder import VideoRecorder

class CameraHandler:
    def __init__(self, camera_id, name, url, camera_type='rtsp'):
        self.camera_id = camera_id
        self.name = name
        self.url = url
        self.camera_type = camera_type
        self.cap = None
        self.is_connected = False
        self.is_streaming = False
        self.current_frame = None
        self.frame_lock = threading.Lock()
        self.stream_thread = None
        self.last_frame_time = time.time()
        self.fps = 0
        self.frame_count = 0
        
        # Motion detection
        self.motion_detector = MotionDetector()
        self.motion_enabled = True
        self.motion_callback = None
        
        # Recording
        self.recorder = VideoRecorder()
        self.auto_record_on_motion = False
        
        # Connection retry settings
        self.max_retries = 3
        self.retry_delay = 5
        self.connection_timeout = 10
        
    def connect(self):
        """Connect to camera"""
        try:
            # Release existing connection
            if self.cap:
                self.cap.release()
            
            # Determine camera source
            if self.camera_type == 'usb':
                camera_source = int(self.url) if self.url.isdigit() else 0
            else:
                camera_source = self.url
            
            # Create VideoCapture object
            self.cap = cv2.VideoCapture(camera_source)
            
            # Set connection timeout for network cameras
            if self.camera_type in ['rtsp', 'ip']:
                self.cap.set(cv2.CAP_PROP_OPEN_TIMEOUT_MSEC, self.connection_timeout * 1000)
                self.cap.set(cv2.CAP_PROP_READ_TIMEOUT_MSEC, 5000)
            
            # Configure camera properties
            self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)  # Reduce buffer to minimize delay
            self.cap.set(cv2.CAP_PROP_FPS, 30)
            
            # Test connection
            ret, frame = self.cap.read()
            if ret and frame is not None:
                self.is_connected = True
                with self.frame_lock:
                    self.current_frame = frame
                return True, "Connected successfully"
            else:
                self.is_connected = False
                return False, "Failed to read from camera"
                
        except Exception as e:
            self.is_connected = False
            return False, f"Connection error: {str(e)}"
    
    def disconnect(self):
        """Disconnect from camera"""
        self.stop_streaming()
        
        if self.cap:
            self.cap.release()
            self.cap = None
        
        self.is_connected = False
        with self.frame_lock:
            self.current_frame = None
    
    def start_streaming(self):
        """Start video streaming"""
        if not self.is_connected:
            success, message = self.connect()
            if not success:
                return False, message
        
        if self.is_streaming:
            return True, "Already streaming"
        
        self.is_streaming = True
        self.stream_thread = threading.Thread(target=self._stream_loop)
        self.stream_thread.daemon = True
        self.stream_thread.start()
        
        return True, "Streaming started"
    
    def stop_streaming(self):
        """Stop video streaming"""
        self.is_streaming = False
        
        if self.stream_thread and self.stream_thread.is_alive():
            self.stream_thread.join(timeout=2)
        
        # Stop recording if active
        if self.recorder.is_recording:
            self.recorder.stop_recording()
    
    def get_current_frame(self):
        """Get current frame safely"""
        with self.frame_lock:
            if self.current_frame is not None:
                return self.current_frame.copy()
            return None
    
    def start_recording(self, duration_minutes=60):
        """Start manual recording"""
        if not self.current_frame is None:
            frame_size = (self.current_frame.shape[1], self.current_frame.shape[0])
            return self.recorder.start_recording(
                self.camera_id, self.name, frame_size, duration_minutes=duration_minutes
            )
        return False, "No frame available"
    
    def stop_recording(self):
        """Stop recording"""
        return self.recorder.stop_recording()
    
    def set_motion_callback(self, callback):
        """Set motion detection callback"""
        self.motion_callback = callback
        self.motion_detector.set_motion_callback(callback)
    
    def enable_motion_detection(self, enabled=True):
        """Enable/disable motion detection"""
        self.motion_enabled = enabled
    
    def set_auto_record_on_motion(self, enabled=True):
        """Enable/disable automatic recording on motion"""
        self.auto_record_on_motion = enabled
    
    def get_status(self):
        """Get camera status"""
        return {
            'camera_id': self.camera_id,
            'name': self.name,
            'url': self.url,
            'type': self.camera_type,
            'connected': self.is_connected,
            'streaming': self.is_streaming,
            'fps': round(self.fps, 1),
            'motion_enabled': self.motion_enabled,
            'motion_detected': self.motion_detector.motion_detected,
            'recording': self.recorder.is_recording,
            'recording_status': self.recorder.get_recording_status()
        }
    
    @staticmethod
    def generate_camera_urls(base_url, channels):
        """Generate multiple camera URLs from DVR/NVR"""
        camera_urls = []
        for channel in channels:
            url = base_url.format(channel)
            camera_urls.append(url)
        return camera_urls
    
    def _stream_loop(self):
        """Main streaming loop"""
        retry_count = 0
        last_fps_time = time.time()

        while self.is_streaming:
            try:
                if not self.cap or not self.cap.isOpened():
                    # Try to reconnect
                    success, _ = self.connect()
                    if not success:
                        retry_count += 1
                        if retry_count >= self.max_retries:
                            break
                        time.sleep(self.retry_delay)
                        continue
                    retry_count = 0

                ret, frame = self.cap.read()

                if ret and frame is not None:
                    # Update frame
                    with self.frame_lock:
                        self.current_frame = frame.copy()

                    # Calculate FPS
                    current_time = time.time()
                    self.frame_count += 1

                    if current_time - last_fps_time >= 1.0:
                        self.fps = self.frame_count / (current_time - last_fps_time)
                        self.frame_count = 0
                        last_fps_time = current_time

                    # Motion detection
                    if self.motion_enabled:
                        motion_detected, processed_frame = self.motion_detector.detect_motion(frame)

                        if motion_detected:
                            # Update frame with motion indicators
                            with self.frame_lock:
                                self.current_frame = processed_frame

                            # Start recording if auto-record is enabled
                            if self.auto_record_on_motion and not self.recorder.is_recording:
                                frame_size = (frame.shape[1], frame.shape[0])
                                self.recorder.start_recording(
                                    self.camera_id, self.name, frame_size
                                )

                            # Call motion callback
                            if self.motion_callback:
                                self.motion_callback(self.camera_id, motion_detected)

                    # Add frame to recorder if recording
                    if self.recorder.is_recording:
                        self.recorder.add_frame(frame)

                    self.last_frame_time = current_time
                    retry_count = 0

                else:
                    # Frame read failed
                    retry_count += 1
                    if retry_count >= self.max_retries:
                        break
                    time.sleep(0.1)

            except Exception as e:
                print(f"Camera {self.name} streaming error: {e}")
                retry_count += 1
                if retry_count >= self.max_retries:
                    break
                time.sleep(self.retry_delay)

        self.is_streaming = False
        self.is_connected = False

    @staticmethod
    def test_camera_connection(url, camera_type='rtsp', timeout=10):
        """Test camera connection without creating handler"""
        try:
            if camera_type == 'usb':
                camera_source = int(url) if url.isdigit() else 0
            else:
                camera_source = url

            cap = cv2.VideoCapture(camera_source)

            if camera_type in ['rtsp', 'ip']:
                cap.set(cv2.CAP_PROP_OPEN_TIMEOUT_MSEC, timeout * 1000)

            ret, frame = cap.read()
            cap.release()

            if ret and frame is not None:
                return True, "Connection successful"
            else:
                return False, "Failed to read from camera"

        except Exception as e:
            return False, f"Connection error: {str(e)}"
