#!/usr/bin/env python3
"""
محرك قراءة أرقام السيارات
License Plate Recognition Engine
"""

import cv2
import numpy as np
import sqlite3
import os
import re
from datetime import datetime
import threading

class LicensePlateDetector:
    def __init__(self, database_path="database.db"):
        self.database_path = database_path
        
        # إعدادات كشف اللوحات
        self.min_plate_width = 80
        self.min_plate_height = 20
        self.max_plate_width = 300
        self.max_plate_height = 100
        self.confidence_threshold = 0.7
        
        # إعدادات OCR
        self.ocr_available = False
        self.init_ocr()
        
        # إعدادات الحفظ
        self.save_plate_images = True
        self.plates_path = "plates_database"
        
        # أنماط أرقام السيارات القطرية
        self.plate_patterns = [
            # الأنماط القطرية الحديثة
            r'\d{6}',                              # 6 أرقام (النمط الجديد)
            r'\d{5}',                              # 5 أرقام
            r'\d{1,3}\s*[أ-ي]\s*\d{1,4}',        # رقم + حرف عربي + أرقام
            r'\d{1,3}\s*[A-Z]\s*\d{1,4}',        # رقم + حرف إنجليزي + أرقام
            r'[أ-ي]\s*\d{1,6}',                   # حرف عربي + أرقام
            r'[A-Z]\s*\d{1,6}',                   # حرف إنجليزي + أرقام

            # الأنماط القطرية التقليدية
            r'\d{1,4}\s*[أ-ي]',                   # أرقام + حرف عربي
            r'\d{1,4}\s*[A-Z]',                   # أرقام + حرف إنجليزي
            r'[أ-ي]\s*[أ-ي]\s*\d{1,4}',          # حرفين عربي + أرقام
            r'[A-Z]\s*[A-Z]\s*\d{1,4}',          # حرفين إنجليزي + أرقام

            # أنماط خاصة قطرية
            r'QAT\s*\d{1,6}',                     # QAT + أرقام
            r'QATAR\s*\d{1,6}',                   # QATAR + أرقام
            r'قطر\s*\d{1,6}',                     # قطر + أرقام
        ]
        
        # إنشاء مجلد الحفظ
        os.makedirs(self.plates_path, exist_ok=True)
        
        # تهيئة قاعدة البيانات
        self.init_plate_database()
        
        # تحميل كاشف اللوحات
        self.plate_cascade = self.load_plate_detector()
    
    def init_ocr(self):
        """تهيئة محرك OCR"""
        try:
            import pytesseract
            self.pytesseract = pytesseract
            self.ocr_available = True
            print("✅ محرك OCR متوفر")
        except ImportError:
            print("⚠️ pytesseract غير متوفر - سيتم استخدام كشف بسيط")
            self.ocr_available = False
    
    def load_plate_detector(self):
        """تحميل كاشف اللوحات"""
        try:
            # محاولة تحميل Haar Cascade للوحات
            cascade_path = cv2.data.haarcascades + 'haarcascade_russian_plate_number.xml'
            if os.path.exists(cascade_path):
                return cv2.CascadeClassifier(cascade_path)
            else:
                print("⚠️ كاشف اللوحات غير متوفر - سيتم استخدام كشف بديل")
                return None
        except:
            return None
    
    def init_plate_database(self):
        """تهيئة جداول قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()
            
            # جدول أرقام السيارات المكتشفة
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS license_plates (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    camera_id INTEGER,
                    plate_number TEXT,
                    confidence REAL,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    image_path TEXT,
                    vehicle_type TEXT,
                    is_whitelisted BOOLEAN DEFAULT 0,
                    is_blacklisted BOOLEAN DEFAULT 0,
                    FOREIGN KEY (camera_id) REFERENCES cameras (id)
                )
            ''')
            
            # جدول قوائم السيارات المسموحة/المحظورة
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS plate_lists (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    plate_number TEXT UNIQUE NOT NULL,
                    list_type TEXT CHECK(list_type IN ('whitelist', 'blacklist')),
                    owner_name TEXT,
                    description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"خطأ في تهيئة قاعدة بيانات اللوحات: {e}")
    
    def detect_plates_cascade(self, frame):
        """كشف اللوحات باستخدام Haar Cascade"""
        if self.plate_cascade is None:
            return []
        
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        plates = self.plate_cascade.detectMultiScale(
            gray, scaleFactor=1.1, minNeighbors=5,
            minSize=(self.min_plate_width, self.min_plate_height),
            maxSize=(self.max_plate_width, self.max_plate_height)
        )
        
        detected_plates = []
        for (x, y, w, h) in plates:
            plate_roi = frame[y:y+h, x:x+w]
            detected_plates.append({
                'bbox': (x, y, w, h),
                'roi': plate_roi,
                'confidence': 0.8  # ثقة افتراضية للـ Cascade
            })
        
        return detected_plates
    
    def detect_plates_contour(self, frame):
        """كشف اللوحات باستخدام تحليل الكونتور"""
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        
        # تطبيق فلاتر لتحسين الكشف
        gray = cv2.bilateralFilter(gray, 11, 17, 17)
        edged = cv2.Canny(gray, 30, 200)
        
        # العثور على الكونتورات
        contours, _ = cv2.findContours(edged, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)
        contours = sorted(contours, key=cv2.contourArea, reverse=True)[:10]
        
        detected_plates = []
        
        for contour in contours:
            # تقريب الكونتور
            epsilon = 0.018 * cv2.arcLength(contour, True)
            approx = cv2.approxPolyDP(contour, epsilon, True)
            
            # البحث عن مستطيلات (4 نقاط)
            if len(approx) == 4:
                x, y, w, h = cv2.boundingRect(approx)
                
                # فحص نسبة العرض إلى الارتفاع (اللوحات عادة أوسع من طولها)
                aspect_ratio = w / h
                if 2.0 <= aspect_ratio <= 5.0 and w >= self.min_plate_width and h >= self.min_plate_height:
                    plate_roi = frame[y:y+h, x:x+w]
                    
                    # حساب الثقة بناءً على خصائص اللوحة
                    confidence = self.calculate_plate_confidence(plate_roi, aspect_ratio)
                    
                    if confidence > self.confidence_threshold:
                        detected_plates.append({
                            'bbox': (x, y, w, h),
                            'roi': plate_roi,
                            'confidence': confidence
                        })
        
        return detected_plates
    
    def calculate_plate_confidence(self, plate_roi, aspect_ratio):
        """حساب ثقة كشف اللوحة"""
        try:
            gray_plate = cv2.cvtColor(plate_roi, cv2.COLOR_BGR2GRAY)
            
            # فحص التباين (اللوحات لها تباين عالي)
            contrast = gray_plate.std()
            
            # فحص نسبة العرض إلى الارتفاع
            aspect_score = 1.0 if 2.5 <= aspect_ratio <= 4.0 else 0.5
            
            # فحص وجود حواف (النصوص لها حواف كثيرة)
            edges = cv2.Canny(gray_plate, 50, 150)
            edge_density = np.sum(edges > 0) / edges.size
            
            # حساب الثقة الإجمالية
            confidence = (contrast / 100.0) * aspect_score * (edge_density * 10)
            return min(confidence, 1.0)
            
        except:
            return 0.5
    
    def extract_text_ocr(self, plate_roi):
        """استخراج النص باستخدام OCR"""
        if not self.ocr_available:
            return ""
        
        try:
            # تحسين جودة الصورة للـ OCR
            gray = cv2.cvtColor(plate_roi, cv2.COLOR_BGR2GRAY)
            
            # تطبيق threshold
            _, thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            
            # تكبير الصورة لتحسين دقة OCR
            scale_factor = 3
            height, width = thresh.shape
            resized = cv2.resize(thresh, (width * scale_factor, height * scale_factor))
            
            # إعدادات OCR للغة العربية والإنجليزية
            config = '--oem 3 --psm 8 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZأبتثجحخدذرزسشصضطظعغفقكلمنهوي'
            
            # استخراج النص
            text = self.pytesseract.image_to_string(resized, lang='ara+eng', config=config)
            
            # تنظيف النص
            text = re.sub(r'[^\w\s]', '', text).strip()
            text = re.sub(r'\s+', ' ', text)
            
            return text
            
        except Exception as e:
            print(f"خطأ في OCR: {e}")
            return ""
    
    def extract_text_simple(self, plate_roi):
        """استخراج النص بطريقة بسيطة (بدون OCR)"""
        try:
            gray = cv2.cvtColor(plate_roi, cv2.COLOR_BGR2GRAY)
            
            # تطبيق threshold
            _, thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            
            # العثور على الكونتورات (الأحرف والأرقام)
            contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # فرز الكونتورات من اليسار إلى اليمين
            contours = sorted(contours, key=lambda c: cv2.boundingRect(c)[0])
            
            # عد الأحرف المحتملة
            char_count = 0
            for contour in contours:
                x, y, w, h = cv2.boundingRect(contour)
                if w > 5 and h > 10:  # حجم معقول للحرف
                    char_count += 1
            
            # إرجاع نص تقديري بناءً على عدد الأحرف
            if 5 <= char_count <= 8:
                return f"PLATE_{char_count}_CHARS"
            else:
                return ""
                
        except:
            return ""
    
    def validate_plate_number(self, text):
        """التحقق من صحة رقم اللوحة"""
        if not text or len(text) < 4:
            return False, 0.0
        
        # فحص الأنماط المختلفة
        for pattern in self.plate_patterns:
            if re.search(pattern, text):
                return True, 0.9
        
        # فحص بسيط للأرقام والأحرف
        has_numbers = bool(re.search(r'\d', text))
        has_letters = bool(re.search(r'[A-Za-zأ-ي]', text))
        
        if has_numbers and has_letters:
            return True, 0.7
        
        return False, 0.3
    
    def detect_license_plates(self, frame, camera_id=None):
        """الوظيفة الرئيسية لكشف أرقام السيارات"""
        detected_plates = []
        
        # كشف اللوحات باستخدام طرق مختلفة
        cascade_plates = self.detect_plates_cascade(frame)
        contour_plates = self.detect_plates_contour(frame)
        
        # دمج النتائج
        all_plates = cascade_plates + contour_plates
        
        for plate_data in all_plates:
            bbox = plate_data['bbox']
            plate_roi = plate_data['roi']
            detection_confidence = plate_data['confidence']
            
            # استخراج النص
            if self.ocr_available:
                plate_text = self.extract_text_ocr(plate_roi)
            else:
                plate_text = self.extract_text_simple(plate_roi)
            
            # التحقق من صحة النص
            is_valid, text_confidence = self.validate_plate_number(plate_text)
            
            if is_valid:
                # حساب الثقة الإجمالية
                overall_confidence = (detection_confidence + text_confidence) / 2
                
                # فحص القوائم البيضاء والسوداء
                is_whitelisted, is_blacklisted = self.check_plate_lists(plate_text)
                
                plate_info = {
                    'bbox': bbox,
                    'text': plate_text,
                    'confidence': overall_confidence,
                    'is_whitelisted': is_whitelisted,
                    'is_blacklisted': is_blacklisted,
                    'roi': plate_roi
                }
                
                detected_plates.append(plate_info)
                
                # حفظ في قاعدة البيانات
                if camera_id:
                    self.log_plate_detection(camera_id, plate_info)
                
                # حفظ صورة اللوحة
                if self.save_plate_images:
                    self.save_plate_image(plate_roi, plate_text, camera_id)
                
                print(f"🚗 كشف لوحة: {plate_text} (ثقة: {overall_confidence:.2f})")
        
        return detected_plates
    
    def check_plate_lists(self, plate_number):
        """فحص القوائم البيضاء والسوداء"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT list_type FROM plate_lists 
                WHERE plate_number = ?
            ''', (plate_number,))
            
            result = cursor.fetchone()
            conn.close()
            
            if result:
                list_type = result[0]
                return list_type == 'whitelist', list_type == 'blacklist'
            
            return False, False
            
        except Exception as e:
            print(f"خطأ في فحص قوائم اللوحات: {e}")
            return False, False
    
    def log_plate_detection(self, camera_id, plate_info):
        """تسجيل كشف اللوحة في قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO license_plates 
                (camera_id, plate_number, confidence, is_whitelisted, is_blacklisted)
                VALUES (?, ?, ?, ?, ?)
            ''', (
                camera_id,
                plate_info['text'],
                plate_info['confidence'],
                plate_info['is_whitelisted'],
                plate_info['is_blacklisted']
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"خطأ في تسجيل كشف اللوحة: {e}")
    
    def save_plate_image(self, plate_roi, plate_text, camera_id):
        """حفظ صورة اللوحة"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            camera_str = f"cam{camera_id}" if camera_id else "unknown"
            safe_text = re.sub(r'[^\w]', '_', plate_text)
            filename = f"plate_{camera_str}_{safe_text}_{timestamp}.jpg"
            filepath = os.path.join(self.plates_path, filename)
            
            cv2.imwrite(filepath, plate_roi)
            return filepath
            
        except Exception as e:
            print(f"خطأ في حفظ صورة اللوحة: {e}")
            return None
    
    def draw_plate_overlay(self, frame, detected_plates):
        """رسم تراكب اللوحات على الإطار"""
        overlay_frame = frame.copy()
        
        for plate in detected_plates:
            x, y, w, h = plate['bbox']
            text = plate['text']
            confidence = plate['confidence']
            is_whitelisted = plate['is_whitelisted']
            is_blacklisted = plate['is_blacklisted']
            
            # اختيار اللون حسب حالة اللوحة
            if is_blacklisted:
                color = (0, 0, 255)  # أحمر للمحظورة
                status = "BLACKLISTED"
            elif is_whitelisted:
                color = (0, 255, 0)  # أخضر للمسموحة
                status = "WHITELISTED"
            else:
                color = (255, 255, 0)  # أصفر للعادية
                status = "DETECTED"
            
            # رسم مربع حول اللوحة
            cv2.rectangle(overlay_frame, (x, y), (x + w, y + h), color, 2)
            
            # إضافة النص
            label = f"{text} ({confidence:.2f})"
            cv2.putText(overlay_frame, label, (x, y - 10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
            
            cv2.putText(overlay_frame, status, (x, y + h + 20), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
        
        return overlay_frame
    
    def add_to_whitelist(self, plate_number, owner_name="", description=""):
        """إضافة لوحة للقائمة البيضاء"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT OR REPLACE INTO plate_lists 
                (plate_number, list_type, owner_name, description)
                VALUES (?, 'whitelist', ?, ?)
            ''', (plate_number, owner_name, description))
            
            conn.commit()
            conn.close()
            
            print(f"✅ تم إضافة {plate_number} للقائمة البيضاء")
            return True
            
        except Exception as e:
            print(f"خطأ في إضافة اللوحة للقائمة البيضاء: {e}")
            return False
    
    def add_to_blacklist(self, plate_number, owner_name="", description=""):
        """إضافة لوحة للقائمة السوداء"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT OR REPLACE INTO plate_lists 
                (plate_number, list_type, owner_name, description)
                VALUES (?, 'blacklist', ?, ?)
            ''', (plate_number, owner_name, description))
            
            conn.commit()
            conn.close()
            
            print(f"✅ تم إضافة {plate_number} للقائمة السوداء")
            return True
            
        except Exception as e:
            print(f"خطأ في إضافة اللوحة للقائمة السوداء: {e}")
            return False


def test_license_plate_detector():
    """اختبار محرك قراءة أرقام السيارات"""
    print("🚗 اختبار محرك قراءة أرقام السيارات...")
    
    detector = LicensePlateDetector()
    
    # إضافة بعض اللوحات التجريبية
    detector.add_to_whitelist("أ ب ج 123", "سيارة المدير", "سيارة مسموحة")
    detector.add_to_blacklist("د هـ و 456", "سيارة محظورة", "سيارة غير مرغوب فيها")
    
    print("✅ تم إعداد محرك قراءة أرقام السيارات")
    print("💡 لاختبار الكشف، استخدم كاميرا أو صور تحتوي على لوحات سيارات")


if __name__ == "__main__":
    test_license_plate_detector()
