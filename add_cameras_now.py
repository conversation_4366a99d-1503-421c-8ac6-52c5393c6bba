#!/usr/bin/env python3
"""
إضافة فورية للكاميرات
Instant Camera Addition
"""

import sqlite3
import sys
import os

def add_cameras_instantly():
    """إضافة فورية للكاميرات"""
    print("🚀 إضافة فورية للكاميرات...")
    
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect('database.db')
        cursor = conn.cursor()
        
        # معلومات جهاز XVR
        ip = "**************"
        username = "admin"
        password = "Mnbv@1978"
        
        # إضافة 8 كاميرات
        cameras_added = 0
        
        for channel in range(1, 9):
            camera_name = f"XVR قناة {channel}"
            rtsp_url = f"rtsp://{username}:{password}@{ip}:554/cam/realmonitor?channel={channel}&subtype=0"
            
            try:
                cursor.execute('''
                    INSERT INTO cameras 
                    (name, rtsp_url, username, password, is_active, camera_type, ip_address, port)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    camera_name, 
                    rtsp_url, 
                    username, 
                    password, 
                    1,  # نشط
                    f"XVR_CH{channel}",
                    ip,
                    554
                ))
                
                cameras_added += 1
                print(f"✅ تم إضافة: {camera_name}")
                
            except sqlite3.IntegrityError:
                # الكاميرا موجودة مسبقاً
                cursor.execute('''
                    UPDATE cameras 
                    SET rtsp_url = ?, username = ?, password = ?, is_active = 1
                    WHERE name = ?
                ''', (rtsp_url, username, password, camera_name))
                print(f"🔄 تم تحديث: {camera_name}")
        
        # حفظ التغييرات
        conn.commit()
        conn.close()
        
        print(f"\n🎉 تم إضافة/تحديث {cameras_added} كاميرا!")
        print("✅ يمكنك الآن العودة للنظام ومشاهدة الكاميرات")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الإضافة: {e}")
        return False

def test_first_camera():
    """اختبار أول كاميرا"""
    print("\n🔍 اختبار أول كاميرا...")
    
    try:
        import cv2
        
        rtsp_url = "rtsp://admin:Mnbv@1978@**************:554/cam/realmonitor?channel=1&subtype=0"
        
        cap = cv2.VideoCapture(rtsp_url)
        
        if cap.isOpened():
            ret, frame = cap.read()
            if ret:
                print("✅ الكاميرا الأولى تعمل بشكل صحيح!")
            else:
                print("⚠️ متصل لكن لا يوجد فيديو")
        else:
            print("❌ فشل الاتصال بالكاميرا")
            print("💡 تأكد من:")
            print("   - اتصال الجهاز بالشبكة")
            print("   - صحة كلمة المرور")
            print("   - تفعيل RTSP في الجهاز")
        
        cap.release()
        
    except ImportError:
        print("⚠️ OpenCV غير متوفر - تخطي الاختبار")
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")

def main():
    """الوظيفة الرئيسية"""
    print("=" * 50)
    print("🚀 إضافة فورية لكاميرات XVR")
    print("=" * 50)
    print("📍 IP: **************")
    print("👤 المستخدم: admin")
    print("🔒 كلمة المرور: Mnbv@1978")
    print()
    
    # إضافة الكاميرات
    if add_cameras_instantly():
        # اختبار أول كاميرا
        test_first_camera()
        
        print("\n" + "=" * 50)
        print("🎯 الخطوات التالية:")
        print("1. ارجع للنظام الرئيسي")
        print("2. اضغط F5 أو أعد تشغيل النظام")
        print("3. ستظهر الكاميرات في القائمة")
        print("=" * 50)
    else:
        print("❌ فشل في إضافة الكاميرات")

if __name__ == "__main__":
    try:
        main()
        input("\nاضغط Enter للخروج...")
    except Exception as e:
        print(f"❌ خطأ: {e}")
        input("اضغط Enter للخروج...")
