#!/usr/bin/env python3
"""
Build script for Camera Monitoring System
Creates executable using PyInstaller
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def clean_build_directories():
    """Clean previous build directories"""
    directories_to_clean = ['build', 'dist', '__pycache__']
    
    for directory in directories_to_clean:
        if os.path.exists(directory):
            print(f"Cleaning {directory}...")
            shutil.rmtree(directory)
    
    # Clean .pyc files
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.pyc'):
                os.remove(os.path.join(root, file))

def create_spec_file():
    """Create PyInstaller spec file"""
    spec_content = '''
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('config.json', '.'),
        ('assets', 'assets'),
        ('ui', 'ui'),
        ('core', 'core'),
        ('utils', 'utils'),
    ],
    hiddenimports=[
        'PyQt5.QtCore',
        'PyQt5.QtGui', 
        'PyQt5.QtWidgets',
        'cv2',
        'numpy',
        'sqlite3',
        'threading',
        'json',
        'datetime',
        'time',
        'os',
        'sys'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='CameraMonitoringSystem',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='assets/icon.ico' if os.path.exists('assets/icon.ico') else None,
)
'''
    
    with open('camera_system.spec', 'w') as f:
        f.write(spec_content)
    
    print("Created camera_system.spec file")

def install_requirements():
    """Install required packages"""
    print("Installing requirements...")
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'])
        print("Requirements installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"Failed to install requirements: {e}")
        return False

def build_executable():
    """Build executable using PyInstaller"""
    print("Building executable...")
    
    try:
        # Build using spec file
        cmd = [
            'pyinstaller',
            '--clean',
            '--noconfirm',
            'camera_system.spec'
        ]
        
        subprocess.check_call(cmd)
        print("Build completed successfully!")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"Build failed: {e}")
        return False
    except FileNotFoundError:
        print("PyInstaller not found. Installing...")
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'pyinstaller'])
            # Retry build
            subprocess.check_call(cmd)
            print("Build completed successfully!")
            return True
        except subprocess.CalledProcessError as e:
            print(f"Failed to install PyInstaller or build: {e}")
            return False

def create_installer_files():
    """Create additional installer files"""
    
    # Create README for distribution
    readme_content = """
Camera Monitoring System
========================

A comprehensive Python-based camera monitoring system with the following features:

Features:
- Multi-camera support (RTSP, IP, USB cameras)
- Real-time video streaming and monitoring
- Motion detection with automatic recording
- Manual video recording controls
- User management and authentication
- Arabic language interface support
- Dark theme interface
- Full-screen viewing mode
- Camera management (add/edit/delete)
- Recording playback and management

System Requirements:
- Windows 10 or later
- Minimum 4GB RAM
- 1GB free disk space
- Network connection for IP/RTSP cameras

Installation:
1. Extract all files to a folder
2. Run CameraMonitoringSystem.exe
3. Default login: admin / admin123

Usage:
1. Login with your credentials
2. Add cameras using the Camera Manager
3. View live feeds in the main window
4. Right-click on camera feeds for options
5. Use toolbar buttons for various functions

Support:
For technical support or questions, please contact the system administrator.

Version: 1.0
"""
    
    with open('dist/README.txt', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    # Create batch file for easy startup
    batch_content = """@echo off
title Camera Monitoring System
echo Starting Camera Monitoring System...
CameraMonitoringSystem.exe
pause
"""
    
    with open('dist/Start_Camera_System.bat', 'w') as f:
        f.write(batch_content)
    
    print("Created installer files")

def copy_additional_files():
    """Copy additional required files to dist"""
    files_to_copy = [
        'config.json',
        'requirements.txt'
    ]
    
    for file in files_to_copy:
        if os.path.exists(file):
            shutil.copy2(file, 'dist/')
            print(f"Copied {file} to dist/")
    
    # Create recordings directory
    recordings_dir = 'dist/recordings'
    if not os.path.exists(recordings_dir):
        os.makedirs(recordings_dir)
        print("Created recordings directory")

def main():
    """Main build process"""
    print("=" * 50)
    print("Camera Monitoring System - Build Script")
    print("=" * 50)
    
    # Clean previous builds
    clean_build_directories()
    
    # Install requirements
    if not install_requirements():
        print("Failed to install requirements. Exiting.")
        return 1
    
    # Create spec file
    create_spec_file()
    
    # Build executable
    if not build_executable():
        print("Build failed. Exiting.")
        return 1
    
    # Copy additional files
    copy_additional_files()
    
    # Create installer files
    create_installer_files()
    
    print("\n" + "=" * 50)
    print("Build completed successfully!")
    print("Executable location: dist/CameraMonitoringSystem.exe")
    print("=" * 50)
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
